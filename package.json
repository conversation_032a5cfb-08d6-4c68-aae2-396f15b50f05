{"name": "react-native-web-view", "private": false, "version": "1.0.0", "type": "module", "description": "A modern web application for writing and previewing React Native code with live mobile simulation", "keywords": ["react-native", "web-editor", "expo", "mobile-preview", "code-editor"], "author": "Sodan", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/somdipto/react-native-web-view.git"}, "homepage": "https://github.com/somdipto/react-native-web-view", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "deploy": "npm run build"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "lucide-react": "^0.294.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-split": "^2.0.14", "snack-sdk": "^6.5.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "terser": "^5.41.0", "vite": "^5.0.8"}}