{"hash": "9fed2054", "configHash": "44c56048", "lockfileHash": "0f7dcbf9", "browserHash": "d093267a", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "2cd80974", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "4f3d0196", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "695338ec", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "c7227285", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "ebba25a5", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "634f8627", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "08885cf3", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "7dfda1f1", "needsInterop": false}, "react-split": {"src": "../../react-split/dist/react-split.es.js", "file": "react-split.js", "fileHash": "ee0367b6", "needsInterop": false}, "snack-sdk": {"src": "../../snack-sdk/build/index.js", "file": "snack-sdk.js", "fileHash": "2888f203", "needsInterop": true}, "qrcode": {"src": "../../qrcode/lib/browser.js", "file": "qrcode.js", "fileHash": "c54fae8a", "needsInterop": true}}, "chunks": {"chunk-63ILDAJJ": {"file": "chunk-63ILDAJJ.js"}, "chunk-GKJBSOWT": {"file": "chunk-GKJBSOWT.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}