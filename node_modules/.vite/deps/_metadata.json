{"hash": "c1ab4149", "configHash": "44c56048", "lockfileHash": "644bc389", "browserHash": "28ccc163", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "37ba37d5", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "40c0c2e9", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c7b62674", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4887fe71", "needsInterop": true}, "@monaco-editor/react": {"src": "../../@monaco-editor/react/dist/index.mjs", "file": "@monaco-editor_react.js", "fileHash": "ab72d825", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "3faf7190", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "8271f85d", "needsInterop": true}, "react-dropzone": {"src": "../../react-dropzone/dist/es/index.js", "file": "react-dropzone.js", "fileHash": "c167a439", "needsInterop": false}, "react-split": {"src": "../../react-split/dist/react-split.es.js", "file": "react-split.js", "fileHash": "f6c100e6", "needsInterop": false}, "snack-sdk": {"src": "../../snack-sdk/build/index.js", "file": "snack-sdk.js", "fileHash": "30b72d64", "needsInterop": true}}, "chunks": {"chunk-GKJBSOWT": {"file": "chunk-GKJBSOWT.js"}, "chunk-63ILDAJJ": {"file": "chunk-63ILDAJJ.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}