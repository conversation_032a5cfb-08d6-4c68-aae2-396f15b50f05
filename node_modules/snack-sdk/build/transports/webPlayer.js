"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getWebPlayerIFrameURL = exports.createWebPlayerTransport = void 0;
var TransportImplWebPlayer_1 = __importDefault(require("./TransportImplWebPlayer"));
function createWebPlayerTransport(config) {
    return new TransportImplWebPlayer_1.default(config);
}
exports.createWebPlayerTransport = createWebPlayerTransport;
function getWebPlayerIFrameURL(webPlayerURL, sdkVersion, initialURL, verbose) {
    if (sdkVersion < '40.0.0' || typeof window === 'undefined') {
        return undefined;
    }
    return webPlayerURL.replace('%%SDK_VERSION%%', sdkVersion.split('.')[0]) + "/index.html?initialUrl=" + encodeURIComponent(initialURL) + "&origin=" + encodeURIComponent(window.location.origin) + "&verbose=" + verbose;
}
exports.getWebPlayerIFrameURL = getWebPlayerIFrameURL;
//# sourceMappingURL=webPlayer.js.map