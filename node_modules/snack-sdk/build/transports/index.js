"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTrafficMirroringTransport = exports.createTransport = exports.ConnectionMetricsEmitter = void 0;
var TrafficMirroringTransport_1 = __importDefault(require("./TrafficMirroringTransport"));
var TransportImplSocketIO_1 = __importDefault(require("./TransportImplSocketIO"));
__exportStar(require("./types"), exports);
__exportStar(require("./ConnectionMetricsEmitter"), exports);
var ConnectionMetricsEmitter_1 = require("./ConnectionMetricsEmitter");
Object.defineProperty(exports, "ConnectionMetricsEmitter", { enumerable: true, get: function () { return __importDefault(ConnectionMetricsEmitter_1).default; } });
function createTransport(options) {
    return new TransportImplSocketIO_1.default(options);
}
exports.createTransport = createTransport;
function createTrafficMirroringTransport(options) {
    return new TrafficMirroringTransport_1.default(options);
}
exports.createTrafficMirroringTransport = createTrafficMirroringTransport;
//# sourceMappingURL=index.js.map