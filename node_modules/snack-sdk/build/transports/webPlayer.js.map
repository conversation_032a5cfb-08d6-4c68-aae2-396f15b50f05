{"version": 3, "file": "webPlayer.js", "sourceRoot": "", "sources": ["../../src/transports/webPlayer.ts"], "names": [], "mappings": ";;;;;;AAEA,oFAA8D;AAI9D,SAAgB,wBAAwB,CAAC,MAMxC;IACC,OAAO,IAAI,gCAAsB,CAAC,MAAM,CAAC,CAAC;AAC5C,CAAC;AARD,4DAQC;AAED,SAAgB,qBAAqB,CACnC,YAAoB,EACpB,UAAsB,EACtB,UAAkB,EAClB,OAAgB;IAEhB,IAAI,UAAU,GAAG,QAAQ,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAC1D,OAAO,SAAS,CAAC;KAClB;IAED,OAAU,YAAY,CAAC,OAAO,CAC5B,iBAAiB,EACjB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CACzB,+BAA0B,kBAAkB,CAAC,UAAU,CAAC,gBAAW,kBAAkB,CACpF,MAAM,CAAC,QAAQ,CAAC,MAAM,CACvB,iBAAY,OAAS,CAAC;AACzB,CAAC;AAhBD,sDAgBC", "sourcesContent": ["import { SDKVersion } from 'snack-content';\n\nimport TransportImplWebPlayer from './TransportImplWebPlayer';\nimport { SnackTransport } from './types';\nimport { SnackWindowRef } from '../types';\n\nexport function createWebPlayerTransport(config: {\n  name?: string;\n  verbose?: boolean;\n  webPlayerURL: string;\n  window: Window;\n  ref: SnackWindowRef;\n}): SnackTransport {\n  return new TransportImplWebPlayer(config);\n}\n\nexport function getWebPlayerIFrameURL(\n  webPlayerURL: string,\n  sdkVersion: SDKVersion,\n  initialURL: string,\n  verbose: boolean,\n) {\n  if (sdkVersion < '40.0.0' || typeof window === 'undefined') {\n    return undefined;\n  }\n\n  return `${webPlayerURL.replace(\n    '%%SDK_VERSION%%',\n    sdkVersion.split('.')[0],\n  )}/index.html?initialUrl=${encodeURIComponent(initialURL)}&origin=${encodeURIComponent(\n    window.location.origin,\n  )}&verbose=${verbose}`;\n}\n"]}