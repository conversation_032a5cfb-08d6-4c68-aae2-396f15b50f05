import { SnackTransport, SnackTransportListener, SnackTransportMessage, SnackTransportOptions } from './types';
export default class TrafficMirroringTransport implements SnackTransport {
    private readonly transports;
    constructor(options: SnackTransportOptions);
    addEventListener(type: 'message', listener: SnackTransportListener): void;
    removeEventListener(type: 'message', listener: SnackTransportListener): void;
    postMessage(message: SnackTransportMessage): void;
}
