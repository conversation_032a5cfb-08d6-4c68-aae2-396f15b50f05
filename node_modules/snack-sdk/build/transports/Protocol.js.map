{"version": 3, "file": "Protocol.js", "sourceRoot": "", "sources": ["../../src/transports/Protocol.ts"], "names": [], "mappings": "", "sourcesContent": ["import { SDKVersion } from 'snack-content';\n\nexport type Device = {\n  name: string;\n  id: string;\n  platform: string;\n};\n\nexport type ProtocolConsoleMessage = {\n  type: 'CONSOLE';\n  device: Device;\n  method: 'log' | 'error' | 'warn';\n  payload: any[];\n};\n\nexport type ProtocolErrorMessage = {\n  type: 'ERROR';\n  error: string; // e.g. `{message: \"my error\", stack: \"...\"}`\n  device: Device;\n};\n\nexport type ProtocolResendCodeMessage = {\n  type: 'RESEND_CODE';\n  device: Device;\n};\n\nexport type ProtocolCodeMessageDependencies = {\n  [name: string]: {\n    version: string; // original version\n    resolved: string; // resolved version\n    handle: string; // snackager handle\n  };\n};\n\nexport type ProtocolCodeMessage = {\n  type: 'CODE';\n  diff: { [path: string]: string };\n  s3url: { [path: string]: string };\n  dependencies: ProtocolCodeMessageDependencies;\n  metadata: {\n    expoSDKVersion: SDKVersion;\n    webSnackSDKVersion: string;\n    webHostname?: string; // window.location.hostname,\n    webOSArchitecture?: string; // os.architecture,\n    webOSFamily?: string; // os.family,\n    webOSVersion?: string; // os.version,\n    webLayoutEngine?: string; // platformInfo.layout,\n    webDeviceType?: string; // platformInfo.product,\n    webBrowser?: string; // platformInfo.name,\n    webBrowserVersion?: string; // platformInfo.version,\n    webDescription?: string; // platformInfo.description,\n  };\n};\n\nexport type ProtocolReloadMessage = {\n  type: 'RELOAD_SNACK';\n};\n\nexport type ProtocolRequestStatusMessage = {\n  type: 'REQUEST_STATUS';\n};\n\nexport type ProtocolStatusMessage = {\n  type: 'STATUS_REPORT';\n  previewLocation: string;\n  status: 'FAILURE' | 'SUCCESS';\n};\n\nexport type ProtocolOutgoingMessage =\n  | ProtocolCodeMessage\n  | ProtocolReloadMessage\n  | ProtocolRequestStatusMessage;\n\nexport type ProtocolIncomingMessage =\n  | ProtocolConsoleMessage\n  | ProtocolErrorMessage\n  | ProtocolResendCodeMessage\n  | ProtocolStatusMessage;\n"]}