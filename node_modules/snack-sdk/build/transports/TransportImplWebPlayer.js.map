{"version": 3, "file": "TransportImplWebPlayer.js", "sourceRoot": "", "sources": ["../../src/transports/TransportImplWebPlayer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA4B;AAG5B,0EAAoD;AAWpD;IAAoD,0CAAiB;IAMnE,gCAAY,MAAgC;QAA5C,YACE,kBAAM,EAAE,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,CAAC,SAMtD;QAVO,YAAM,GAA0B,SAAS,CAAC;QAyClD,4BAAsB,GAAG,UAAC,KAAmB;;YAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,KAAI,CAAC,MAAM,EAAE;gBAChC,IAAI;oBACF,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC/B,IAAA,IAAI,GAAK,OAAO,KAAZ,CAAa;oBACzB,IAAI,IAAI,KAAK,SAAS,EAAE;wBACtB,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBACpD,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;qBAC/C;yBAAM,IAAI,IAAI,KAAK,YAAY,EAAE;wBAChC,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBACpD,KAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;qBAChD;yBAAM,IAAI,IAAI,KAAK,SAAS,EAAE;wBAC7B,IAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;wBAC5D,KAAI,CAAC,aAAa,CAAC,YAAY,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;qBACnD;iBACF;gBAAC,WAAM;oBACN,MAAA,KAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,iBAAiB,EAAE,KAAK,CAAC,IAAI,EAAE;iBAClD;aACF;QACH,CAAC,CAAC;QAvDQ,IAAA,MAAM,GAAK,IAAI,GAAG,CAAC,MAAM,CAAC,YAAY,CAAC,OAAjC,CAAkC;QAChD,KAAI,CAAC,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC;QACnC,KAAI,CAAC,eAAe,GAAG,MAAM,CAAC,GAAG,CAAC;QAClC,KAAI,CAAC,MAAM,GAAG,SAAS,CAAC;QACxB,KAAI,CAAC,MAAM,GAAG,MAAM,CAAC;;IACvB,CAAC;IAES,sCAAK,GAAf;;QACE,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,MAAA,IAAI,CAAC,aAAa,0CAAE,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE,KAAK,EAAE;QACpF,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAES,qCAAI,GAAd;QACE,gBAAM,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;QACtF,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAC/B,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;IAC1B,CAAC;IAES,0CAAS,GAAnB;QACE,OAAO,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC;IACjE,CAAC;IAEe,0CAAS,GAAzB,UAA0B,OAAe,EAAE,OAAgC;;;;;gBACzE,gBAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;gBACjB,YAAY,SAAG,IAAI,CAAC,eAAe,0CAAE,OAAO,CAAC;gBACnD,gBAAM,CAAC,YAAY,CAAC,CAAC;gBACrB,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;;;KAChE;IAES,wDAAuB,GAAjC,UAAkC,WAAgC;QAChE,OAAO,IAAI,CAAC;IACd,CAAC;IAsBH,6BAAC;AAAD,CAAC,AAhED,CAAoD,2BAAiB,GAgEpE", "sourcesContent": ["import assert from 'assert';\n\nimport type { ProtocolOutgoingMessage, ProtocolCodeMessage } from './Protocol';\nimport TransportImplBase from './TransportImplBase';\nimport type { SnackWindowRef } from '../types';\n\nexport interface TransportWebPlayerConfig {\n  name?: string;\n  verbose?: boolean;\n  webPlayerURL: string;\n  window: Window;\n  ref: SnackWindowRef;\n}\n\nexport default class TransportImplWebPlayer extends TransportImplBase {\n  private currentWindow: Window | undefined;\n  private targetWindowRef: SnackWindowRef | undefined;\n  private status: 'stopped' | 'started' = 'stopped';\n  private readonly origin: string;\n\n  constructor(config: TransportWebPlayerConfig) {\n    super({ name: config.name, verbose: config.verbose });\n    const { origin } = new URL(config.webPlayerURL);\n    this.currentWindow = config.window;\n    this.targetWindowRef = config.ref;\n    this.status = 'stopped';\n    this.origin = origin;\n  }\n\n  protected start(): void {\n    assert(this.currentWindow);\n    this.currentWindow?.addEventListener('message', this.handleDomWindowMessage, false);\n    this.status = 'started';\n  }\n\n  protected stop(): void {\n    assert(this.currentWindow);\n    this.currentWindow.removeEventListener('message', this.handleDomWindowMessage, false);\n    this.currentWindow = undefined;\n    this.targetWindowRef = undefined;\n    this.status = 'stopped';\n  }\n\n  protected isStarted(): boolean {\n    return this.status === 'started' && this.currentWindow != null;\n  }\n\n  protected async sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void> {\n    assert(this.isStarted);\n    const targetWindow = this.targetWindowRef?.current;\n    assert(targetWindow);\n    targetWindow.postMessage(JSON.stringify(message), this.origin);\n  }\n\n  protected onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean {\n    return true;\n  }\n\n  handleDomWindowMessage = (event: MessageEvent) => {\n    if (event.origin === this.origin) {\n      try {\n        const message = JSON.parse(event.data);\n        const { type } = message;\n        if (type === 'CONNECT') {\n          const connectionId = JSON.stringify(message.device);\n          this.handleChannelEvent('join', connectionId);\n        } else if (type === 'DISCONNECT') {\n          const connectionId = JSON.stringify(message.device);\n          this.handleChannelEvent('leave', connectionId);\n        } else if (type === 'MESSAGE') {\n          const connectionId = JSON.stringify(message.message.device);\n          this.handleMessage(connectionId, message.message);\n        }\n      } catch {\n        this.logger?.warn('Invalid message', event.data);\n      }\n    }\n  };\n}\n"]}