import { SnackFiles, SDKVersion, SnackDependencies } from 'snack-content';
import { ProtocolCodeMessage } from './Protocol';
import { Logger } from '../Logger';
export declare type SnackCode = {
    files: SnackFiles;
    dependencies: SnackDependencies;
    sdkVersion: SDKVersion;
};
export declare type CodeMessageBuilderCallback = (codeMessage: ProtocolCodeMessage, code: SnackCode) => any;
export declare function getFileDiff(oldCode: string, newCode: string): string;
export default class CodeMessageBuilder {
    private callback;
    private code;
    private codeMessage?;
    private logger?;
    private codeUploader?;
    private maxDiffPlaceholder?;
    private placeholderURLs;
    private uploadedURLs;
    private verifyCodeMessageSize;
    constructor(options: {
        callback: CodeMessageBuilderCallback;
        verifyCodeMessageSize: (message: ProtocolCodeMessage) => boolean;
        apiURL?: string;
        logger?: Logger;
        maxDiffPlaceholder?: string;
    });
    setCode(code: SnackCode): void;
    private createCodeMessage;
    private onFileUploaded;
}
