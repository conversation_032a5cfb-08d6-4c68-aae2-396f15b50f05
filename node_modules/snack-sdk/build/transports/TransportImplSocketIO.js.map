{"version": 3, "file": "TransportImplSocketIO.js", "sourceRoot": "", "sources": ["../../src/transports/TransportImplSocketIO.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,qDAAsC;AAGtC,wFAAkE;AAElE,0EAAoD;AAgBpD;IAAmD,yCAAiB;IAMlE,+BAAY,OAA8B;QAA1C,YACE,kBAAM,OAAO,CAAC,SAOf;QAZO,YAAM,GAA8D,IAAI,CAAC;QAkFzE,eAAS,GAAG,UAAC,IAIpB;YACS,IAAA,MAAM,GAAc,IAAI,OAAlB,EAAE,OAAO,GAAK,IAAI,QAAT,CAAU;YACjC,KAAI,CAAC,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QACtC,CAAC,CAAC;QAEM,mBAAa,GAAG,UAAC,IAAyC;;YACxD,IAAA,MAAM,GAAK,IAAI,OAAT,CAAU;YACxB,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,YAAK,KAAI,CAAC,MAAM,0CAAE,EAAE,CAAA,EAAE;gBAC/C,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;aACzC;QACH,CAAC,CAAC;QAEM,oBAAc,GAAG,UAAC,IAAyC;;YACzD,IAAA,MAAM,GAAK,IAAI,OAAT,CAAU;YACxB,IAAI,MAAM,KAAK,EAAE,IAAI,MAAM,YAAK,KAAI,CAAC,MAAM,0CAAE,EAAE,CAAA,EAAE;gBAC/C,KAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aAC1C;QACH,CAAC,CAAC;QAjGQ,IAAA,WAAW,GAAK,OAAO,YAAZ,CAAa;QAChC,IAAI,CAAC,WAAW,EAAE;YAChB,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;SAC7D;QACD,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAC/B,KAAI,CAAC,kBAAkB,GAAG,CAAC,CAAC;;IAC9B,CAAC;IAES,qCAAK,GAAf;QAAA,iBAgCC;QA/BC,IAAI,CAAC,IAAI,EAAE,CAAC;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE5B,IAAI,CAAC,MAAM,GAAG,qBAAE,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,UAAU,EAAE,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC;QAElE,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE;;YACxB,MAAA,KAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,kBAAkB,EAAE,EAAE,OAAO,EAAE,KAAI,CAAC,OAAO,EAAE,MAAM,QAAE,KAAI,CAAC,MAAM,0CAAE,EAAE,EAAE,EAAE;YAC1F,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,kCAAwB,CAAC,aAAa,CAAC;oBACrC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,KAAI,CAAC,SAAS;oBACnC,QAAQ,EAAE,KAAI,CAAC,kBAAkB;iBAClC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QACH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,mBAAmB,EAAE,UAAC,QAAgB;YACtD,KAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;YACnC,IAAI,KAAI,CAAC,SAAS,EAAE;gBAClB,kCAAwB,CAAC,UAAU,CAAC;oBAClC,MAAM,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,KAAI,CAAC,SAAS;oBACnC,QAAQ,UAAA;iBACT,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,aAAa,EAAE,IAAI,CAAC,aAAa,CAAC,CAAC;QAClD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW,EAAE,UAAC,MAAM;;YACjC,MAAA,KAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,6BAA2B,MAAQ,EAAE;YACvD,MAAA,KAAI,CAAC,MAAM,0CAAE,UAAU,GAAG;QAC5B,CAAC,CAAC,CAAC;IACL,CAAC;IAES,oCAAI,GAAd;;QACE,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACvB,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE;YAEjD,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACrB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;SACpB;QACD,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAES,yCAAS,GAAnB;QACE,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;IAC7B,CAAC;IAES,yCAAS,GAAnB,UAAoB,OAAe,EAAE,OAAgC;;QACnE,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,SAAS,EAAE;YAC3B,OAAO,SAAA;YACP,OAAO,SAAA;YACP,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;SACvB,EAAE;QACH,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;IAC3B,CAAC;IAES,uDAAuB,GAAjC,UAAkC,WAAgC;QAChE,mEAAmE;QACnE,yEAAyE;QACzE,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,IAAM,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;YACnC,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;SAC3D;QACD,OAAO,UAAU,GAAG,2BAAiB,CAAC,wBAAwB,CAAC;IACjE,CAAC;IAwBH,4BAAC;AAAD,CAAC,AA1GD,CAAmD,2BAAiB,GA0GnE", "sourcesContent": ["import { io } from 'socket.io-client';\nimport type { Socket } from 'socket.io-client';\n\nimport ConnectionMetricsEmitter from './ConnectionMetricsEmitter';\nimport { ProtocolOutgoingMessage, ProtocolIncomingMessage, ProtocolCodeMessage } from './Protocol';\nimport TransportImplBase from './TransportImplBase';\nimport type { SnackTransportOptions } from './types';\n\ninterface ServerToClientEvents {\n  message: (data: { channel: string; message: ProtocolIncomingMessage; sender: string }) => void;\n  joinChannel: (data: { channel: string; sender: string }) => void;\n  leaveChannel: (data: { channel: string; sender: string }) => void;\n  terminate: (reason: string) => void;\n}\n\ninterface ClientToServerEvents {\n  message: (data: { channel: string; message: ProtocolOutgoingMessage; sender: string }) => void;\n  subscribeChannel: (data: { channel: string; sender: string }) => void;\n  unsubscribeChannel: (data: { channel: string; sender: string }) => void;\n}\n\nexport default class TransportImplSocketIO extends TransportImplBase {\n  private readonly snackpubURL: string;\n  private socket: Socket<ServerToClientEvents, ClientToServerEvents> | null = null;\n  private startTime: number | undefined;\n  private connectionAttempts: number;\n\n  constructor(options: SnackTransportOptions) {\n    super(options);\n    const { snackpubURL } = options;\n    if (!snackpubURL) {\n      throw new Error('The `snackpubURL` option is unspecified.');\n    }\n    this.snackpubURL = snackpubURL;\n    this.connectionAttempts = 0;\n  }\n\n  protected start(): void {\n    this.stop();\n    this.startTime = Date.now();\n\n    this.socket = io(this.snackpubURL, { transports: ['websocket'] });\n\n    this.socket.on('connect', () => {\n      this.socket?.emit('subscribeChannel', { channel: this.channel, sender: this.socket?.id });\n      if (this.startTime) {\n        ConnectionMetricsEmitter.emitSuccessed({\n          timeMs: Date.now() - this.startTime,\n          attempts: this.connectionAttempts,\n        });\n      }\n    });\n    this.socket.io.on('reconnect_attempt', (attempts: number) => {\n      this.connectionAttempts = attempts;\n      if (this.startTime) {\n        ConnectionMetricsEmitter.emitFailed({\n          timeMs: Date.now() - this.startTime,\n          attempts,\n        });\n      }\n    });\n\n    this.socket.on('message', this.onMessage);\n    this.socket.on('joinChannel', this.onJoinChannel);\n    this.socket.on('leaveChannel', this.onLeaveChannel);\n    this.socket.on('terminate', (reason) => {\n      this.logger?.comm(`Terminating connection: ${reason}`);\n      this.socket?.disconnect();\n    });\n  }\n\n  protected stop(): void {\n    if (this.socket != null) {\n      this.logger?.comm('Stopping...', this.logSuffix);\n\n      this.socket.offAny();\n      this.socket.close();\n      this.socket = null;\n    }\n    this.startTime = undefined;\n  }\n\n  protected isStarted(): boolean {\n    return this.socket != null;\n  }\n\n  protected sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void> {\n    this.socket?.emit('message', {\n      channel,\n      message,\n      sender: this.socket.id,\n    });\n    return Promise.resolve();\n  }\n\n  protected onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean {\n    // Calculate unencoded size (quickly) and if that exceeds the limit\n    // then don't bother calculating the exact size (which is more expensive)\n    let approxSize = 0;\n    for (const path in codeMessage.diff) {\n      approxSize += path.length + codeMessage.diff[path].length;\n    }\n    return approxSize < TransportImplBase.CODE_SIZE_LIMIT_FOR_DIFF;\n  }\n\n  private onMessage = (data: {\n    channel: string;\n    message: ProtocolIncomingMessage;\n    sender: string;\n  }) => {\n    const { sender, message } = data;\n    this.handleMessage(sender, message);\n  };\n\n  private onJoinChannel = (data: { channel: string; sender: string }) => {\n    const { sender } = data;\n    if (sender !== '' && sender !== this.socket?.id) {\n      this.handleChannelEvent('join', sender);\n    }\n  };\n\n  private onLeaveChannel = (data: { channel: string; sender: string }) => {\n    const { sender } = data;\n    if (sender !== '' && sender !== this.socket?.id) {\n      this.handleChannelEvent('leave', sender);\n    }\n  };\n}\n"]}