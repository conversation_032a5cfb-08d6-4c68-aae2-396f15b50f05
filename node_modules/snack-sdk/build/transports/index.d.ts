import { SnackTransport, SnackTransportOptions } from './types';
export * from './types';
export * from './ConnectionMetricsEmitter';
export { default as ConnectionMetricsEmitter } from './ConnectionMetricsEmitter';
export declare function createTransport(options: SnackTransportOptions): SnackTransport;
export declare function createTrafficMirroringTransport(options: SnackTransportOptions): SnackTransport;
