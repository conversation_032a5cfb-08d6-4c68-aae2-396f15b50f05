"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var TransportImplPubNub_1 = __importDefault(require("./TransportImplPubNub"));
var TransportImplSocketIO_1 = __importDefault(require("./TransportImplSocketIO"));
var TrafficMirroringTransport = /** @class */ (function () {
    function TrafficMirroringTransport(options) {
        this.transports = [new TransportImplPubNub_1.default(options), new TransportImplSocketIO_1.default(options)];
    }
    TrafficMirroringTransport.prototype.addEventListener = function (type, listener) {
        for (var _i = 0, _a = this.transports; _i < _a.length; _i++) {
            var transport = _a[_i];
            transport.addEventListener(type, listener);
        }
    };
    TrafficMirroringTransport.prototype.removeEventListener = function (type, listener) {
        for (var _i = 0, _a = this.transports; _i < _a.length; _i++) {
            var transport = _a[_i];
            transport.removeEventListener(type, listener);
        }
    };
    TrafficMirroringTransport.prototype.postMessage = function (message) {
        for (var _i = 0, _a = this.transports; _i < _a.length; _i++) {
            var transport = _a[_i];
            transport.postMessage(message);
        }
    };
    return TrafficMirroringTransport;
}());
exports.default = TrafficMirroringTransport;
//# sourceMappingURL=TrafficMirroringTransport.js.map