import { ProtocolOutgoingMessage, ProtocolCodeMessage } from './Protocol';
import TransportImplBase from './TransportImplBase';
import type { SnackTransportOptions } from './types';
export default class TransportImplSocketIO extends TransportImplBase {
    private readonly snackpubURL;
    private socket;
    private startTime;
    private connectionAttempts;
    constructor(options: SnackTransportOptions);
    protected start(): void;
    protected stop(): void;
    protected isStarted(): boolean;
    protected sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void>;
    protected onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean;
    private onMessage;
    private onJoinChannel;
    private onLeaveChannel;
}
