interface ConnectionMetricsCommonPayload {
    timeMs: number;
    attempts: number;
}
export interface ConnectionMetricsSucceeded extends ConnectionMetricsCommonPayload {
    name: 'TRANSPORT_CONNECTION_SUCCEEDED';
}
export interface ConnectionMetricsFailed extends ConnectionMetricsCommonPayload {
    name: 'TRANSPORT_CONNECTION_FAILED';
}
declare type ConnectionMetricsEvents = ConnectionMetricsSucceeded | ConnectionMetricsFailed;
export declare type ConnectionMetricsUpdateListener = (event: ConnectionMetricsEvents) => void;
declare class ConnectionMetricsEmitter {
    private listener;
    private lastEmitState;
    emitSuccessed(payload: ConnectionMetricsCommonPayload): void;
    emitFailed(payload: ConnectionMetricsCommonPayload): void;
    resetState(): void;
    setUpdateListener(listener: ConnectionMetricsUpdateListener): void;
    private emit;
}
declare const _default: ConnectionMetricsEmitter;
export default _default;
