"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var CodeMessageBuilder_1 = __importDefault(require("./CodeMessageBuilder"));
var Logger_1 = require("../Logger");
var TransportImplBase = /** @class */ (function () {
    function TransportImplBase(options) {
        var _this = this;
        this.connectionsCount = 0;
        this.onCodeMessageReady = function (codeMessage) {
            var _a;
            _this.codeMessage = codeMessage;
            (_a = _this.logger) === null || _a === void 0 ? void 0 : _a.comm('Sending code...', _this.logSuffix);
            _this.publish(codeMessage);
        };
        var apiURL = options.apiURL, channel = options.channel, verbose = options.verbose;
        this.channel = channel !== null && channel !== void 0 ? channel : '';
        this.logger = verbose ? Logger_1.createLogger(true) : undefined;
        this.logSuffix = options.name ? " (" + options.name + ")" : '';
        if (channel) {
            this.codeMessageBuilder = new CodeMessageBuilder_1.default({
                verifyCodeMessageSize: this.onVerifyCodeMessageSize,
                callback: this.onCodeMessageReady,
                apiURL: apiURL,
                logger: this.logger,
                maxDiffPlaceholder: 'X'.repeat(TransportImplBase.CODE_SIZE_LIMIT_FOR_DIFF),
            });
        }
        else {
            this.codeMessageBuilder = new CodeMessageBuilder_1.default({
                verifyCodeMessageSize: function () { return true; },
                callback: this.onCodeMessageReady,
                logger: this.logger,
            });
        }
    }
    TransportImplBase.prototype.addEventListener = function (_type, callback) {
        this.callback = callback;
    };
    TransportImplBase.prototype.removeEventListener = function (_type, _callback) {
        this.callback = undefined;
    };
    TransportImplBase.prototype.postMessage = function (message) {
        switch (message.type) {
            case 'start':
                this.start();
                break;
            case 'stop':
                this.stop();
                break;
            case 'update_code':
                this.codeMessageBuilder.setCode(message.data);
                break;
            case 'protocol_message':
                this.publish(message.data);
                break;
            case 'synthetic_event':
                this.onSyntheticEvent(message.data);
                break;
        }
    };
    /**
     * Helper function for derived transports.
     * When the derived transport receives message, it should call this function to process message data.
     */
    TransportImplBase.prototype.handleMessage = function (senderConnectionId, message) {
        this.onProtocolMessage(senderConnectionId, message);
    };
    /**
     * Helper function for derived transports.
     * When the derived transport receives channel join/leave events, it should call this function to process channel presence data.
     */
    TransportImplBase.prototype.handleChannelEvent = function (event, connectionId) {
        var _a, _b, _c;
        if (event === 'join') {
            try {
                var device = JSON.parse(connectionId);
                this.connectionsCount++;
                (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, {
                    type: 'connect',
                    connectionId: connectionId,
                    data: device,
                });
                if (this.codeMessage) {
                    (_b = this.logger) === null || _b === void 0 ? void 0 : _b.comm('Sending code...', this.logSuffix);
                    this.publish(this.codeMessage);
                }
            }
            catch (_d) {
                // Wasn't from the device
            }
        }
        else {
            this.connectionsCount = Math.max(this.connectionsCount - 1, 0);
            (_c = this.callback) === null || _c === void 0 ? void 0 : _c.call(this, {
                type: 'disconnect',
                connectionId: connectionId,
                data: {},
            });
        }
    };
    TransportImplBase.prototype.onProtocolMessage = function (connectionId, message) {
        var _a, _b;
        if (message.type === 'RESEND_CODE') {
            if (this.codeMessage) {
                (_a = this.logger) === null || _a === void 0 ? void 0 : _a.comm('Resending code...', this.logSuffix);
                this.publish(this.codeMessage);
            }
        }
        else {
            (_b = this.callback) === null || _b === void 0 ? void 0 : _b.call(this, {
                type: 'protocol_message',
                connectionId: connectionId,
                data: message,
            });
        }
    };
    TransportImplBase.prototype.publish = function (message) {
        var _a, _b, _c;
        return __awaiter(this, void 0, void 0, function () {
            var e_1;
            return __generator(this, function (_d) {
                switch (_d.label) {
                    case 0:
                        if (!this.connectionsCount) {
                            return [2 /*return*/];
                        }
                        if (!this.isStarted()) return [3 /*break*/, 5];
                        _d.label = 1;
                    case 1:
                        _d.trys.push([1, 3, , 4]);
                        return [4 /*yield*/, this.sendAsync(this.channel, message)];
                    case 2:
                        _d.sent();
                        return [3 /*break*/, 4];
                    case 3:
                        e_1 = _d.sent();
                        (_a = this.logger) === null || _a === void 0 ? void 0 : _a.error('Failed to publish message', message.type, e_1, this.logSuffix);
                        return [3 /*break*/, 4];
                    case 4: return [3 /*break*/, 6];
                    case 5:
                        (_b = this.callback) === null || _b === void 0 ? void 0 : _b.call(this, {
                            type: 'synthetic_event',
                            data: message,
                        });
                        _d.label = 6;
                    case 6:
                        (_c = this.callback) === null || _c === void 0 ? void 0 : _c.call(this, {
                            type: 'send_message',
                            data: message,
                        });
                        return [2 /*return*/];
                }
            });
        });
    };
    TransportImplBase.prototype.onSyntheticEvent = function (data) {
        var _a, _b, _c;
        if (typeof data !== 'string') {
            return;
        }
        try {
            var message = JSON.parse(data);
            switch (message.type) {
                case 'CONNECT':
                    this.connectionsCount++;
                    (_a = this.callback) === null || _a === void 0 ? void 0 : _a.call(this, {
                        type: 'connect',
                        connectionId: JSON.stringify(message.device),
                        data: message.device,
                    });
                    break;
                case 'DISCONNECT':
                    this.connectionsCount = Math.max(this.connectionsCount - 1, 0);
                    (_b = this.callback) === null || _b === void 0 ? void 0 : _b.call(this, {
                        type: 'disconnect',
                        connectionId: JSON.stringify(message.device),
                        data: {},
                    });
                    break;
                case 'MESSAGE':
                    this.onProtocolMessage(JSON.stringify(message.message.device), message.message);
                    break;
            }
        }
        catch (e) {
            (_c = this.logger) === null || _c === void 0 ? void 0 : _c.error('Failed to parse postMessage', e, data, this.logSuffix);
        }
    };
    // Limit for code size in diff message
    TransportImplBase.CODE_SIZE_LIMIT_FOR_DIFF = 32768;
    return TransportImplBase;
}());
exports.default = TransportImplBase;
//# sourceMappingURL=TransportImplBase.js.map