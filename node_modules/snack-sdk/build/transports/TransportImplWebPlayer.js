"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var assert_1 = __importDefault(require("assert"));
var TransportImplBase_1 = __importDefault(require("./TransportImplBase"));
var TransportImplWebPlayer = /** @class */ (function (_super) {
    __extends(TransportImplWebPlayer, _super);
    function TransportImplWebPlayer(config) {
        var _this = _super.call(this, { name: config.name, verbose: config.verbose }) || this;
        _this.status = 'stopped';
        _this.handleDomWindowMessage = function (event) {
            var _a;
            if (event.origin === _this.origin) {
                try {
                    var message = JSON.parse(event.data);
                    var type = message.type;
                    if (type === 'CONNECT') {
                        var connectionId = JSON.stringify(message.device);
                        _this.handleChannelEvent('join', connectionId);
                    }
                    else if (type === 'DISCONNECT') {
                        var connectionId = JSON.stringify(message.device);
                        _this.handleChannelEvent('leave', connectionId);
                    }
                    else if (type === 'MESSAGE') {
                        var connectionId = JSON.stringify(message.message.device);
                        _this.handleMessage(connectionId, message.message);
                    }
                }
                catch (_b) {
                    (_a = _this.logger) === null || _a === void 0 ? void 0 : _a.warn('Invalid message', event.data);
                }
            }
        };
        var origin = new URL(config.webPlayerURL).origin;
        _this.currentWindow = config.window;
        _this.targetWindowRef = config.ref;
        _this.status = 'stopped';
        _this.origin = origin;
        return _this;
    }
    TransportImplWebPlayer.prototype.start = function () {
        var _a;
        assert_1.default(this.currentWindow);
        (_a = this.currentWindow) === null || _a === void 0 ? void 0 : _a.addEventListener('message', this.handleDomWindowMessage, false);
        this.status = 'started';
    };
    TransportImplWebPlayer.prototype.stop = function () {
        assert_1.default(this.currentWindow);
        this.currentWindow.removeEventListener('message', this.handleDomWindowMessage, false);
        this.currentWindow = undefined;
        this.targetWindowRef = undefined;
        this.status = 'stopped';
    };
    TransportImplWebPlayer.prototype.isStarted = function () {
        return this.status === 'started' && this.currentWindow != null;
    };
    TransportImplWebPlayer.prototype.sendAsync = function (channel, message) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            var targetWindow;
            return __generator(this, function (_b) {
                assert_1.default(this.isStarted);
                targetWindow = (_a = this.targetWindowRef) === null || _a === void 0 ? void 0 : _a.current;
                assert_1.default(targetWindow);
                targetWindow.postMessage(JSON.stringify(message), this.origin);
                return [2 /*return*/];
            });
        });
    };
    TransportImplWebPlayer.prototype.onVerifyCodeMessageSize = function (codeMessage) {
        return true;
    };
    return TransportImplWebPlayer;
}(TransportImplBase_1.default));
exports.default = TransportImplWebPlayer;
//# sourceMappingURL=TransportImplWebPlayer.js.map