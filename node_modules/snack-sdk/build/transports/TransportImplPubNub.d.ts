import { ProtocolOutgoingMessage, ProtocolCodeMessage } from './Protocol';
import TransportImplBase from './TransportImplBase';
export declare function calcPubNubCodeMessageSize(channel: string, codeMessage: ProtocolCodeMessage): number;
export default class TransportImplPubNub extends TransportImplBase {
    private pubNub?;
    private pubNubClientId?;
    protected start(): void;
    protected stop(): void;
    protected isStarted(): boolean;
    protected sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void>;
    protected onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean;
    private onPubNubPresence;
    private onPubNubMessage;
    private onPubNubStatus;
}
