{"version": 3, "file": "TransportImplBase.js", "sourceRoot": "", "sources": ["../../src/transports/TransportImplBase.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4EAAsF;AAQtF,oCAAiD;AAEjD;IAYE,2BAAY,OAA8B;QAA1C,iBAqBC;QA3BO,qBAAgB,GAAW,CAAC,CAAC;QAkM7B,uBAAkB,GAA+B,UAAC,WAAW;;YACnE,KAAI,CAAC,WAAW,GAAG,WAAW,CAAC;YAC/B,MAAA,KAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,iBAAiB,EAAE,KAAI,CAAC,SAAS,EAAE;YACrD,KAAI,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;QAC5B,CAAC,CAAC;QA/LQ,IAAA,MAAM,GAAuB,OAAO,OAA9B,EAAE,OAAO,GAAc,OAAO,QAArB,EAAE,OAAO,GAAK,OAAO,QAAZ,CAAa;QAC7C,IAAI,CAAC,OAAO,GAAG,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC;QAC7B,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,qBAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QACvD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,OAAK,OAAO,CAAC,IAAI,MAAG,CAAC,CAAC,CAAC,EAAE,CAAC;QAE1D,IAAI,OAAO,EAAE;YACX,IAAI,CAAC,kBAAkB,GAAG,IAAI,4BAAkB,CAAC;gBAC/C,qBAAqB,EAAE,IAAI,CAAC,uBAAuB;gBACnD,QAAQ,EAAE,IAAI,CAAC,kBAAkB;gBACjC,MAAM,QAAA;gBACN,MAAM,EAAE,IAAI,CAAC,MAAM;gBACnB,kBAAkB,EAAE,GAAG,CAAC,MAAM,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;aAC3E,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,CAAC,kBAAkB,GAAG,IAAI,4BAAkB,CAAC;gBAC/C,qBAAqB,EAAE,cAAM,OAAA,IAAI,EAAJ,CAAI;gBACjC,QAAQ,EAAE,IAAI,CAAC,kBAAkB;gBACjC,MAAM,EAAE,IAAI,CAAC,MAAM;aACpB,CAAC,CAAC;SACJ;IACH,CAAC;IAED,4CAAgB,GAAhB,UAAiB,KAAgB,EAAE,QAAgC;QACjE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAED,+CAAmB,GAAnB,UAAoB,KAAgB,EAAE,SAAiC;QACrE,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC;IAC5B,CAAC;IAED,uCAAW,GAAX,UAAY,OAA8B;QACxC,QAAQ,OAAO,CAAC,IAAI,EAAE;YACpB,KAAK,OAAO;gBACV,IAAI,CAAC,KAAK,EAAE,CAAC;gBACb,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,EAAE,CAAC;gBACZ,MAAM;YACR,KAAK,aAAa;gBAChB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC9C,MAAM;YACR,KAAK,kBAAkB;gBACrB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC3B,MAAM;YACR,KAAK,iBAAiB;gBACpB,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACpC,MAAM;SACT;IACH,CAAC;IA8BD;;;OAGG;IACO,yCAAa,GAAvB,UAAwB,kBAA0B,EAAE,OAAgC;QAClF,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;;OAGG;IACO,8CAAkB,GAA5B,UAA6B,KAAuB,EAAE,YAAoB;;QACxE,IAAI,KAAK,KAAK,MAAM,EAAE;YACpB,IAAI;gBACF,IAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBACxC,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACxB,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;oBACd,IAAI,EAAE,SAAS;oBACf,YAAY,cAAA;oBACZ,IAAI,EAAE,MAAM;iBACb,EAAE;gBACH,IAAI,IAAI,CAAC,WAAW,EAAE;oBACpB,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,SAAS,EAAE;oBACrD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;iBAChC;aACF;YAAC,WAAM;gBACN,yBAAyB;aAC1B;SACF;aAAM;YACL,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;YAC/D,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;gBACd,IAAI,EAAE,YAAY;gBAClB,YAAY,cAAA;gBACZ,IAAI,EAAE,EAAE;aACT,EAAE;SACJ;IACH,CAAC;IAEO,6CAAiB,GAAzB,UAA0B,YAAoB,EAAE,OAAgC;;QAC9E,IAAI,OAAO,CAAC,IAAI,KAAK,aAAa,EAAE;YAClC,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC,SAAS,EAAE;gBACvD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aAChC;SACF;aAAM;YACL,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;gBACd,IAAI,EAAE,kBAAkB;gBACxB,YAAY,cAAA;gBACZ,IAAI,EAAE,OAAO;aACd,EAAE;SACJ;IACH,CAAC;IAEa,mCAAO,GAArB,UAAsB,OAAgC;;;;;;;wBACpD,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;4BAC1B,sBAAO;yBACR;6BACG,IAAI,CAAC,SAAS,EAAE,EAAhB,wBAAgB;;;;wBAEhB,qBAAM,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,EAAA;;wBAA3C,SAA2C,CAAC;;;;wBAE5C,MAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,CAAC,2BAA2B,EAAE,OAAO,CAAC,IAAI,EAAE,GAAC,EAAE,IAAI,CAAC,SAAS,EAAE;;;;wBAGnF,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;4BACd,IAAI,EAAE,iBAAiB;4BACvB,IAAI,EAAE,OAAO;yBACd,EAAE;;;wBAEL,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;4BACd,IAAI,EAAE,cAAc;4BACpB,IAAI,EAAE,OAAO;yBACd,EAAE;;;;;KACJ;IAEO,4CAAgB,GAAxB,UAAyB,IAAS;;QAChC,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YAC5B,OAAO;SACR;QAED,IAAI;YACF,IAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACjC,QAAQ,OAAO,CAAC,IAAI,EAAE;gBACpB,KAAK,SAAS;oBACZ,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACxB,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;wBACd,IAAI,EAAE,SAAS;wBACf,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC5C,IAAI,EAAE,OAAO,CAAC,MAAM;qBACrB,EAAE;oBACH,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;oBAC/D,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY;wBACd,IAAI,EAAE,YAAY;wBAClB,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC;wBAC5C,IAAI,EAAE,EAAE;qBACT,EAAE;oBACH,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;oBAChF,MAAM;aACT;SACF;QAAC,OAAO,CAAC,EAAE;YACV,MAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,CAAC,6BAA6B,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE;SAC5E;IACH,CAAC;IA7LD,sCAAsC;IACZ,0CAAwB,GAAG,KAAK,CAAC;IAmM7D,wBAAC;CAAA,AA7MD,IA6MC;kBA7M6B,iBAAiB", "sourcesContent": ["import CodeMessageBuilder, { CodeMessageBuilderCallback } from './CodeMessageBuilder';\nimport { ProtocolOutgoingMessage, ProtocolIncomingMessage, ProtocolCodeMessage } from './Protocol';\nimport {\n  SnackTransport,\n  SnackTransportListener,\n  SnackTransportMessage,\n  SnackTransportOptions,\n} from './types';\nimport { createLogger, Logger } from '../Logger';\n\nexport default abstract class TransportImplBase implements SnackTransport {\n  protected readonly channel: string;\n  protected readonly logger?: Logger;\n  private callback?: SnackTransportListener;\n  private codeMessageBuilder: CodeMessageBuilder;\n  private codeMessage?: ProtocolCodeMessage;\n  private connectionsCount: number = 0;\n  protected readonly logSuffix: string;\n\n  // Limit for code size in diff message\n  protected static readonly CODE_SIZE_LIMIT_FOR_DIFF = 32768;\n\n  constructor(options: SnackTransportOptions) {\n    const { apiURL, channel, verbose } = options;\n    this.channel = channel ?? '';\n    this.logger = verbose ? createLogger(true) : undefined;\n    this.logSuffix = options.name ? ` (${options.name})` : '';\n\n    if (channel) {\n      this.codeMessageBuilder = new CodeMessageBuilder({\n        verifyCodeMessageSize: this.onVerifyCodeMessageSize,\n        callback: this.onCodeMessageReady,\n        apiURL,\n        logger: this.logger,\n        maxDiffPlaceholder: 'X'.repeat(TransportImplBase.CODE_SIZE_LIMIT_FOR_DIFF),\n      });\n    } else {\n      this.codeMessageBuilder = new CodeMessageBuilder({\n        verifyCodeMessageSize: () => true,\n        callback: this.onCodeMessageReady,\n        logger: this.logger,\n      });\n    }\n  }\n\n  addEventListener(_type: 'message', callback: SnackTransportListener) {\n    this.callback = callback;\n  }\n\n  removeEventListener(_type: 'message', _callback: SnackTransportListener) {\n    this.callback = undefined;\n  }\n\n  postMessage(message: SnackTransportMessage) {\n    switch (message.type) {\n      case 'start':\n        this.start();\n        break;\n      case 'stop':\n        this.stop();\n        break;\n      case 'update_code':\n        this.codeMessageBuilder.setCode(message.data);\n        break;\n      case 'protocol_message':\n        this.publish(message.data);\n        break;\n      case 'synthetic_event':\n        this.onSyntheticEvent(message.data);\n        break;\n    }\n  }\n\n  /**\n   * Start transport\n   */\n  protected abstract start(): void;\n\n  /**\n   * Stop transport\n   */\n  protected abstract stop(): void;\n\n  /**\n   * Is transport started\n   */\n\n  protected abstract isStarted(): boolean;\n\n  /**\n   * Send message to target channel\n   */\n\n  protected abstract sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void>;\n\n  /**\n   * Verify whether the transport is able to send the code message with given size\n   * @returns true if the code message size is valid\n   */\n  protected abstract onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean;\n\n  /**\n   * Helper function for derived transports.\n   * When the derived transport receives message, it should call this function to process message data.\n   */\n  protected handleMessage(senderConnectionId: string, message: ProtocolIncomingMessage) {\n    this.onProtocolMessage(senderConnectionId, message);\n  }\n\n  /**\n   * Helper function for derived transports.\n   * When the derived transport receives channel join/leave events, it should call this function to process channel presence data.\n   */\n  protected handleChannelEvent(event: 'join' | 'leave', connectionId: string) {\n    if (event === 'join') {\n      try {\n        const device = JSON.parse(connectionId);\n        this.connectionsCount++;\n        this.callback?.({\n          type: 'connect',\n          connectionId,\n          data: device,\n        });\n        if (this.codeMessage) {\n          this.logger?.comm('Sending code...', this.logSuffix);\n          this.publish(this.codeMessage);\n        }\n      } catch {\n        // Wasn't from the device\n      }\n    } else {\n      this.connectionsCount = Math.max(this.connectionsCount - 1, 0);\n      this.callback?.({\n        type: 'disconnect',\n        connectionId,\n        data: {},\n      });\n    }\n  }\n\n  private onProtocolMessage(connectionId: string, message: ProtocolIncomingMessage) {\n    if (message.type === 'RESEND_CODE') {\n      if (this.codeMessage) {\n        this.logger?.comm('Resending code...', this.logSuffix);\n        this.publish(this.codeMessage);\n      }\n    } else {\n      this.callback?.({\n        type: 'protocol_message',\n        connectionId,\n        data: message,\n      });\n    }\n  }\n\n  private async publish(message: ProtocolOutgoingMessage) {\n    if (!this.connectionsCount) {\n      return;\n    }\n    if (this.isStarted()) {\n      try {\n        await this.sendAsync(this.channel, message);\n      } catch (e) {\n        this.logger?.error('Failed to publish message', message.type, e, this.logSuffix);\n      }\n    } else {\n      this.callback?.({\n        type: 'synthetic_event',\n        data: message,\n      });\n    }\n    this.callback?.({\n      type: 'send_message',\n      data: message,\n    });\n  }\n\n  private onSyntheticEvent(data: any) {\n    if (typeof data !== 'string') {\n      return;\n    }\n\n    try {\n      const message = JSON.parse(data);\n      switch (message.type) {\n        case 'CONNECT':\n          this.connectionsCount++;\n          this.callback?.({\n            type: 'connect',\n            connectionId: JSON.stringify(message.device),\n            data: message.device,\n          });\n          break;\n        case 'DISCONNECT':\n          this.connectionsCount = Math.max(this.connectionsCount - 1, 0);\n          this.callback?.({\n            type: 'disconnect',\n            connectionId: JSON.stringify(message.device),\n            data: {},\n          });\n          break;\n        case 'MESSAGE':\n          this.onProtocolMessage(JSON.stringify(message.message.device), message.message);\n          break;\n      }\n    } catch (e) {\n      this.logger?.error('Failed to parse postMessage', e, data, this.logSuffix);\n    }\n  }\n\n  private onCodeMessageReady: CodeMessageBuilderCallback = (codeMessage) => {\n    this.codeMessage = codeMessage;\n    this.logger?.comm('Sending code...', this.logSuffix);\n    this.publish(codeMessage);\n  };\n}\n"]}