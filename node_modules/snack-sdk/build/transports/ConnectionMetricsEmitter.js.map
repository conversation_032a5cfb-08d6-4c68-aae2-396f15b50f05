{"version": 3, "file": "ConnectionMetricsEmitter.js", "sourceRoot": "", "sources": ["../../src/transports/ConnectionMetricsEmitter.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAiBA,IAAM,qCAAqC,GAAG,CAAC,CAAC;AAEhD;IAAA;QACU,aAAQ,GAA2C,IAAI,CAAC;IAsClE,CAAC;IAhCQ,gDAAa,GAApB,UAAqB,OAAuC;QAC1D,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,IAAI,IAAI,CAAC,aAAa,KAAK,6BAA6B,EAAE;YAC5F,+DAA+D;YAC/D,mFAAmF;YACnF,IAAI,CAAC,IAAI,YAAG,IAAI,EAAE,gCAAgC,IAAK,OAAO,EAAG,CAAC;YAClE,IAAI,CAAC,aAAa,GAAG,gCAAgC,CAAC;SACvD;IACH,CAAC;IAEM,6CAAU,GAAjB,UAAkB,OAAuC;QACvD,IACE,IAAI,CAAC,aAAa,KAAK,SAAS;YAChC,OAAO,CAAC,QAAQ,IAAI,qCAAqC,EACzD;YACA,+DAA+D;YAC/D,mFAAmF;YACnF,IAAI,CAAC,IAAI,YAAG,IAAI,EAAE,6BAA6B,IAAK,OAAO,EAAG,CAAC;YAC/D,IAAI,CAAC,aAAa,GAAG,6BAA6B,CAAC;SACpD;IACH,CAAC;IAEM,6CAAU,GAAjB;QACE,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC;IACjC,CAAC;IAEM,oDAAiB,GAAxB,UAAyB,QAAyC;QAChE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC3B,CAAC;IAEO,uCAAI,GAAZ,UAAa,KAA8B;;QACzC,MAAA,IAAI,CAAC,QAAQ,+CAAb,IAAI,EAAY,KAAK,EAAE;IACzB,CAAC;IACH,+BAAC;AAAD,CAAC,AAvCD,IAuCC;AAED,kBAAe,IAAI,wBAAwB,EAAE,CAAC", "sourcesContent": ["interface ConnectionMetricsCommonPayload {\n  timeMs: number;\n  attempts: number;\n}\n\nexport interface ConnectionMetricsSucceeded extends ConnectionMetricsCommonPayload {\n  name: 'TRANSPORT_CONNECTION_SUCCEEDED';\n}\n\nexport interface ConnectionMetricsFailed extends ConnectionMetricsCommonPayload {\n  name: 'TRANSPORT_CONNECTION_FAILED';\n}\n\ntype ConnectionMetricsEvents = ConnectionMetricsSucceeded | ConnectionMetricsFailed;\n\nexport type ConnectionMetricsUpdateListener = (event: ConnectionMetricsEvents) => void;\n\nconst METRICS_FAILED_FOR_RECONNECT_ATTEMPTS = 5;\n\nclass ConnectionMetricsEmitter {\n  private listener: ConnectionMetricsUpdateListener | null = null;\n  private lastEmitState:\n    | undefined\n    | 'TRANSPORT_CONNECTION_SUCCEEDED'\n    | 'TRANSPORT_CONNECTION_FAILED';\n\n  public emitSuccessed(payload: ConnectionMetricsCommonPayload) {\n    if (this.lastEmitState === undefined || this.lastEmitState === 'TRANSPORT_CONNECTION_FAILED') {\n      // To reduce the duplicated events, e.g. keeping failed events.\n      // We only log undefined -> succeeded, undefined -> failed, and failed -> successed\n      this.emit({ name: 'TRANSPORT_CONNECTION_SUCCEEDED', ...payload });\n      this.lastEmitState = 'TRANSPORT_CONNECTION_SUCCEEDED';\n    }\n  }\n\n  public emitFailed(payload: ConnectionMetricsCommonPayload) {\n    if (\n      this.lastEmitState === undefined &&\n      payload.attempts >= METRICS_FAILED_FOR_RECONNECT_ATTEMPTS\n    ) {\n      // To reduce the duplicated events, e.g. keeping failed events.\n      // We only log undefined -> succeeded, undefined -> failed, and failed -> succeeded\n      this.emit({ name: 'TRANSPORT_CONNECTION_FAILED', ...payload });\n      this.lastEmitState = 'TRANSPORT_CONNECTION_FAILED';\n    }\n  }\n\n  public resetState() {\n    this.lastEmitState = undefined;\n  }\n\n  public setUpdateListener(listener: ConnectionMetricsUpdateListener) {\n    this.listener = listener;\n  }\n\n  private emit(event: ConnectionMetricsEvents) {\n    this.listener?.(event);\n  }\n}\n\nexport default new ConnectionMetricsEmitter();\n"]}