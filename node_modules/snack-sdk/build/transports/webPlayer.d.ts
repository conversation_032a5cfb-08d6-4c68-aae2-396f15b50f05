import { SDKVersion } from 'snack-content';
import { SnackTransport } from './types';
import { SnackWindowRef } from '../types';
export declare function createWebPlayerTransport(config: {
    name?: string;
    verbose?: boolean;
    webPlayerURL: string;
    window: Window;
    ref: SnackWindowRef;
}): SnackTransport;
export declare function getWebPlayerIFrameURL(webPlayerURL: string, sdkVersion: SDKVersion, initialURL: string, verbose: boolean): string | undefined;
