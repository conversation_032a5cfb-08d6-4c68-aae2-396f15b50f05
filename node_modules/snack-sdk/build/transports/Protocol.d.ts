import { SDKVersion } from 'snack-content';
export declare type Device = {
    name: string;
    id: string;
    platform: string;
};
export declare type ProtocolConsoleMessage = {
    type: 'CONSOLE';
    device: Device;
    method: 'log' | 'error' | 'warn';
    payload: any[];
};
export declare type ProtocolErrorMessage = {
    type: 'ERROR';
    error: string;
    device: Device;
};
export declare type ProtocolResendCodeMessage = {
    type: 'RESEND_CODE';
    device: Device;
};
export declare type ProtocolCodeMessageDependencies = {
    [name: string]: {
        version: string;
        resolved: string;
        handle: string;
    };
};
export declare type ProtocolCodeMessage = {
    type: 'CODE';
    diff: {
        [path: string]: string;
    };
    s3url: {
        [path: string]: string;
    };
    dependencies: ProtocolCodeMessageDependencies;
    metadata: {
        expoSDKVersion: SDKVersion;
        webSnackSDKVersion: string;
        webHostname?: string;
        webOSArchitecture?: string;
        webOSFamily?: string;
        webOSVersion?: string;
        webLayoutEngine?: string;
        webDeviceType?: string;
        webBrowser?: string;
        webBrowserVersion?: string;
        webDescription?: string;
    };
};
export declare type ProtocolReloadMessage = {
    type: 'RELOAD_SNACK';
};
export declare type ProtocolRequestStatusMessage = {
    type: 'REQUEST_STATUS';
};
export declare type ProtocolStatusMessage = {
    type: 'STATUS_REPORT';
    previewLocation: string;
    status: 'FAILURE' | 'SUCCESS';
};
export declare type ProtocolOutgoingMessage = ProtocolCodeMessage | ProtocolReloadMessage | ProtocolRequestStatusMessage;
export declare type ProtocolIncomingMessage = ProtocolConsoleMessage | ProtocolErrorMessage | ProtocolResendCodeMessage | ProtocolStatusMessage;
