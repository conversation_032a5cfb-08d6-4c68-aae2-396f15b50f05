"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
    return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (_) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.calcPubNubCodeMessageSize = void 0;
var pubnub_1 = __importDefault(require("pubnub"));
var TransportImplBase_1 = __importDefault(require("./TransportImplBase"));
function calcPubNubCodeMessageSize(channel, codeMessage) {
    // https://support.pubnub.com/hc/en-us/articles/360051495932-Calculating-message-payload-size-before-publishing
    return (encodeURIComponent(channel +
        JSON.stringify(codeMessage).replace(/[!~*'()]/g, function (x) { return "%" + x.charCodeAt(0).toString(16).toUpperCase(); })).length + 200);
}
exports.calcPubNubCodeMessageSize = calcPubNubCodeMessageSize;
var TransportImplPubNub = /** @class */ (function (_super) {
    __extends(TransportImplPubNub, _super);
    function TransportImplPubNub() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.onPubNubPresence = function (event) {
            switch (event.action) {
                case 'join':
                    _this.handleChannelEvent('join', event.uuid);
                    break;
                case 'timeout':
                case 'leave':
                    _this.handleChannelEvent('leave', event.uuid);
                    break;
            }
        };
        _this.onPubNubMessage = function (event) {
            var message = event.message;
            _this.handleMessage(event.publisher, message);
        };
        _this.onPubNubStatus = function (_event) {
            // TODO: Do something here?
        };
        return _this;
    }
    TransportImplPubNub.prototype.start = function () {
        var _a;
        this.stop();
        if (!this.pubNubClientId) {
            // Keep track of the client ID per transport instance.
            // See: https://support.pubnub.com/hc/en-us/articles/************-How-do-I-set-the-UUID-
            this.pubNubClientId = pubnub_1.default.generateUUID();
        }
        if (this.channel) {
            (_a = this.logger) === null || _a === void 0 ? void 0 : _a.comm('Starting channel...', this.channel, this.logSuffix);
            this.pubNub = new pubnub_1.default({
                publishKey: '******************************************',
                subscribeKey: '******************************************',
                uuid: this.pubNubClientId,
                ssl: true,
                presenceTimeout: 600,
                heartbeatInterval: 60,
            });
            this.pubNub.addListener({
                presence: this.onPubNubPresence,
                message: this.onPubNubMessage,
                status: this.onPubNubStatus,
            });
            this.pubNub.subscribe({
                channels: [this.channel],
                withPresence: true,
            });
        }
    };
    TransportImplPubNub.prototype.stop = function () {
        var _a;
        if (this.pubNub) {
            (_a = this.logger) === null || _a === void 0 ? void 0 : _a.comm('Stopping...', this.logSuffix);
            this.pubNub.removeListener({
                presence: this.onPubNubPresence,
                message: this.onPubNubMessage,
                status: this.onPubNubStatus,
            });
            this.pubNub.unsubscribe({
                channels: [this.channel],
            });
            this.pubNub.stop();
            this.pubNub = undefined;
        }
    };
    TransportImplPubNub.prototype.isStarted = function () {
        return this.pubNub != null;
    };
    TransportImplPubNub.prototype.sendAsync = function (channel, message) {
        var _a;
        return __awaiter(this, void 0, void 0, function () {
            return __generator(this, function (_b) {
                switch (_b.label) {
                    case 0: return [4 /*yield*/, ((_a = this.pubNub) === null || _a === void 0 ? void 0 : _a.publish({
                            channel: channel,
                            message: message,
                        }))];
                    case 1:
                        _b.sent();
                        return [2 /*return*/];
                }
            });
        });
    };
    TransportImplPubNub.prototype.onVerifyCodeMessageSize = function (codeMessage) {
        // Calculate unencoded size (quickly) and if that exceeds the limit
        // then don't bother calculating the exact size (which is more expensive)
        var approxSize = 0;
        for (var path in codeMessage.diff) {
            approxSize += path.length + codeMessage.diff[path].length;
        }
        if (approxSize >= TransportImplBase_1.default.CODE_SIZE_LIMIT_FOR_DIFF) {
            return false;
        }
        // Calculate exact size and check whether it exceeds the limit
        var size = calcPubNubCodeMessageSize(this.channel, codeMessage);
        return size < TransportImplBase_1.default.CODE_SIZE_LIMIT_FOR_DIFF;
    };
    return TransportImplPubNub;
}(TransportImplBase_1.default));
exports.default = TransportImplPubNub;
//# sourceMappingURL=TransportImplPubNub.js.map