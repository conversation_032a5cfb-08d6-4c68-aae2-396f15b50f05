{"version": 3, "file": "TransportImplPubNub.js", "sourceRoot": "", "sources": ["../../src/transports/TransportImplPubNub.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA4B;AAG5B,0EAAoD;AAEpD,SAAgB,yBAAyB,CAAC,OAAe,EAAE,WAAgC;IACzF,+GAA+G;IAC/G,OAAO,CACL,kBAAkB,CAChB,OAAO;QACL,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC,OAAO,CACjC,WAAW,EACX,UAAC,CAAC,IAAK,OAAA,MAAI,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,WAAW,EAAI,EAAhD,CAAgD,CACxD,CACJ,CAAC,MAAM,GAAG,GAAG,CACf,CAAC;AACJ,CAAC;AAXD,8DAWC;AAED;IAAiD,uCAAiB;IAAlE;QAAA,qEAsGC;QApBS,sBAAgB,GAAG,UAAC,KAA2B;YACrD,QAAQ,KAAK,CAAC,MAAM,EAAE;gBACpB,KAAK,MAAM;oBACT,KAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC5C,MAAM;gBACR,KAAK,SAAS,CAAC;gBACf,KAAK,OAAO;oBACV,KAAI,CAAC,kBAAkB,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;oBAC7C,MAAM;aACT;QACH,CAAC,CAAC;QAEM,qBAAe,GAAG,UAAC,KAA0B;YACnD,IAAM,OAAO,GAA4B,KAAK,CAAC,OAAO,CAAC;YACvD,KAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC/C,CAAC,CAAC;QAEM,oBAAc,GAAG,UAAC,MAA0B;YAClD,2BAA2B;QAC7B,CAAC,CAAC;;IACJ,CAAC;IAlGW,mCAAK,GAAf;;QACE,IAAI,CAAC,IAAI,EAAE,CAAC;QAEZ,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,sDAAsD;YACtD,wFAAwF;YACxF,IAAI,CAAC,cAAc,GAAG,gBAAM,CAAC,YAAY,EAAE,CAAC;SAC7C;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE;YACvE,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;gBACvB,UAAU,EAAE,4CAA4C;gBACxD,YAAY,EAAE,4CAA4C;gBAC1D,IAAI,EAAE,IAAI,CAAC,cAAc;gBACzB,GAAG,EAAE,IAAI;gBACT,eAAe,EAAE,GAAG;gBACpB,iBAAiB,EAAE,EAAE;aACtB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACtB,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,OAAO,EAAE,IAAI,CAAC,eAAe;gBAC7B,MAAM,EAAE,IAAI,CAAC,cAAc;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC;gBACpB,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;gBACxB,YAAY,EAAE,IAAI;aACnB,CAAC,CAAC;SACJ;IACH,CAAC;IAES,kCAAI,GAAd;;QACE,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAA,IAAI,CAAC,MAAM,0CAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE;YACjD,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC;gBACzB,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,OAAO,EAAE,IAAI,CAAC,eAAe;gBAC7B,MAAM,EAAE,IAAI,CAAC,cAAc;aAC5B,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;gBACtB,QAAQ,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC;aACzB,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;YACnB,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC;SACzB;IACH,CAAC;IAES,uCAAS,GAAnB;QACE,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC;IAC7B,CAAC;IAEe,uCAAS,GAAzB,UAA0B,OAAe,EAAE,OAAgC;;;;;4BACzE,4BAAM,IAAI,CAAC,MAAM,0CAAE,OAAO,CAAC;4BACzB,OAAO,SAAA;4BACP,OAAO,SAAA;yBACR,IAAC;;wBAHF,SAGE,CAAC;;;;;KACJ;IAES,qDAAuB,GAAjC,UAAkC,WAAgC;QAChE,mEAAmE;QACnE,yEAAyE;QACzE,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,KAAK,IAAM,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE;YACnC,UAAU,IAAI,IAAI,CAAC,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;SAC3D;QACD,IAAI,UAAU,IAAI,2BAAiB,CAAC,wBAAwB,EAAE;YAC5D,OAAO,KAAK,CAAC;SACd;QAED,8DAA8D;QAC9D,IAAM,IAAI,GAAG,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;QAClE,OAAO,IAAI,GAAG,2BAAiB,CAAC,wBAAwB,CAAC;IAC3D,CAAC;IAsBH,0BAAC;AAAD,CAAC,AAtGD,CAAiD,2BAAiB,GAsGjE", "sourcesContent": ["import PubNub from 'pubnub';\n\nimport { ProtocolOutgoingMessage, ProtocolIncomingMessage, ProtocolCodeMessage } from './Protocol';\nimport TransportImplBase from './TransportImplBase';\n\nexport function calcPubNubCodeMessageSize(channel: string, codeMessage: ProtocolCodeMessage) {\n  // https://support.pubnub.com/hc/en-us/articles/360051495932-Calculating-message-payload-size-before-publishing\n  return (\n    encodeURIComponent(\n      channel +\n        JSON.stringify(codeMessage).replace(\n          /[!~*'()]/g,\n          (x) => `%${x.charCodeAt(0).toString(16).toUpperCase()}`,\n        ),\n    ).length + 200\n  );\n}\n\nexport default class TransportImplPubNub extends TransportImplBase {\n  private pubNub?: PubNub;\n  private pubNubClientId?: string;\n\n  protected start() {\n    this.stop();\n\n    if (!this.pubNubClientId) {\n      // Keep track of the client ID per transport instance.\n      // See: https://support.pubnub.com/hc/en-us/articles/************-How-do-I-set-the-UUID-\n      this.pubNubClientId = PubNub.generateUUID();\n    }\n\n    if (this.channel) {\n      this.logger?.comm('Starting channel...', this.channel, this.logSuffix);\n      this.pubNub = new PubNub({\n        publishKey: '******************************************',\n        subscribeKey: '******************************************',\n        uuid: this.pubNubClientId,\n        ssl: true,\n        presenceTimeout: 600,\n        heartbeatInterval: 60,\n      });\n\n      this.pubNub.addListener({\n        presence: this.onPubNubPresence,\n        message: this.onPubNubMessage,\n        status: this.onPubNubStatus,\n      });\n\n      this.pubNub.subscribe({\n        channels: [this.channel],\n        withPresence: true,\n      });\n    }\n  }\n\n  protected stop() {\n    if (this.pubNub) {\n      this.logger?.comm('Stopping...', this.logSuffix);\n      this.pubNub.removeListener({\n        presence: this.onPubNubPresence,\n        message: this.onPubNubMessage,\n        status: this.onPubNubStatus,\n      });\n\n      this.pubNub.unsubscribe({\n        channels: [this.channel],\n      });\n\n      this.pubNub.stop();\n      this.pubNub = undefined;\n    }\n  }\n\n  protected isStarted() {\n    return this.pubNub != null;\n  }\n\n  protected async sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void> {\n    await this.pubNub?.publish({\n      channel,\n      message,\n    });\n  }\n\n  protected onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean {\n    // Calculate unencoded size (quickly) and if that exceeds the limit\n    // then don't bother calculating the exact size (which is more expensive)\n    let approxSize = 0;\n    for (const path in codeMessage.diff) {\n      approxSize += path.length + codeMessage.diff[path].length;\n    }\n    if (approxSize >= TransportImplBase.CODE_SIZE_LIMIT_FOR_DIFF) {\n      return false;\n    }\n\n    // Calculate exact size and check whether it exceeds the limit\n    const size = calcPubNubCodeMessageSize(this.channel, codeMessage);\n    return size < TransportImplBase.CODE_SIZE_LIMIT_FOR_DIFF;\n  }\n\n  private onPubNubPresence = (event: PubNub.PresenceEvent) => {\n    switch (event.action) {\n      case 'join':\n        this.handleChannelEvent('join', event.uuid);\n        break;\n      case 'timeout':\n      case 'leave':\n        this.handleChannelEvent('leave', event.uuid);\n        break;\n    }\n  };\n\n  private onPubNubMessage = (event: PubNub.MessageEvent) => {\n    const message: ProtocolIncomingMessage = event.message;\n    this.handleMessage(event.publisher, message);\n  };\n\n  private onPubNubStatus = (_event: PubNub.StatusEvent) => {\n    // TODO: Do something here?\n  };\n}\n"]}