import type { ProtocolOutgoingMessage, ProtocolCodeMessage } from './Protocol';
import TransportImplBase from './TransportImplBase';
import type { SnackWindowRef } from '../types';
export interface TransportWebPlayerConfig {
    name?: string;
    verbose?: boolean;
    webPlayerURL: string;
    window: Window;
    ref: SnackWindowRef;
}
export default class TransportImplWebPlayer extends TransportImplBase {
    private currentWindow;
    private targetWindowRef;
    private status;
    private readonly origin;
    constructor(config: TransportWebPlayerConfig);
    protected start(): void;
    protected stop(): void;
    protected isStarted(): boolean;
    protected sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void>;
    protected onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean;
    handleDomWindowMessage: (event: MessageEvent) => void;
}
