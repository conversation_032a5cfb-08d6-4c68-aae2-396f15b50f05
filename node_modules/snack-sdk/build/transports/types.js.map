{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/transports/types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { SnackFiles, SnackDependencies, SDKVersion } from 'snack-content';\n\nimport { ProtocolOutgoingMessage, ProtocolIncomingMessage } from './Protocol';\n\nexport type SnackTransportStartMessage = {\n  type: 'start';\n};\n\nexport type SnackTransportStopMessage = {\n  type: 'stop';\n};\n\nexport type SnackTransportUpdateCodeMessage = {\n  type: 'update_code';\n  data: {\n    files: SnackFiles;\n    dependencies: SnackDependencies;\n    sdkVersion: SDKVersion;\n  };\n};\n\nexport type SnackTransportProtocolMessage = {\n  type: 'protocol_message';\n  data: ProtocolOutgoingMessage;\n};\n\nexport type SnackTransportSyntheticMessage = {\n  type: 'synthetic_event';\n  data: any;\n};\n\nexport type SnackTransportMessage =\n  | SnackTransportStartMessage\n  | SnackTransportStopMessage\n  | SnackTransportUpdateCodeMessage\n  | SnackTransportProtocolMessage\n  | SnackTransportSyntheticMessage;\n\nexport type SnackTransportEventConnect = {\n  type: 'connect';\n  connectionId: string;\n  data: any; // DeviceInfo\n};\n\nexport type SnackTransportEventDisconnect = {\n  type: 'disconnect';\n  connectionId: string;\n  // Keeps the `data` keys here for SnackTransportEvent polymorphism, even though we do not use the data in disconnect event actually\n  data: any;\n};\n\nexport type SnackTransportEventProtocolMessage = {\n  type: 'protocol_message';\n  connectionId: string;\n  data: ProtocolIncomingMessage;\n};\n\nexport type SnackTransportEventSendMessage = {\n  type: 'send_message';\n  data: ProtocolOutgoingMessage;\n};\n\nexport type SnackTransportEventSyntheticMessage = {\n  type: 'synthetic_event';\n  data: any;\n};\n\nexport type SnackTransportEvent =\n  | SnackTransportEventConnect\n  | SnackTransportEventDisconnect\n  | SnackTransportEventProtocolMessage\n  | SnackTransportEventSendMessage\n  | SnackTransportEventSyntheticMessage;\n\nexport type SnackTransportListener = (event: SnackTransportEvent) => void;\n\nexport interface SnackTransport {\n  addEventListener(type: 'message', listener: SnackTransportListener): void;\n  removeEventListener(type: 'message', listener: SnackTransportListener): void;\n  postMessage(message: SnackTransportMessage): void;\n}\n\nexport type SnackTransportOptions = {\n  channel?: string;\n  apiURL?: string;\n  verbose?: boolean;\n  name?: string;\n  snackpubURL?: string | undefined;\n};\n"]}