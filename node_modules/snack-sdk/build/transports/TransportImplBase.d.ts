import { ProtocolOutgoingMessage, ProtocolIncomingMessage, ProtocolCodeMessage } from './Protocol';
import { SnackTransport, SnackTransportListener, SnackTransportMessage, SnackTransportOptions } from './types';
import { Logger } from '../Logger';
export default abstract class TransportImplBase implements SnackTransport {
    protected readonly channel: string;
    protected readonly logger?: Logger;
    private callback?;
    private codeMessageBuilder;
    private codeMessage?;
    private connectionsCount;
    protected readonly logSuffix: string;
    protected static readonly CODE_SIZE_LIMIT_FOR_DIFF = 32768;
    constructor(options: SnackTransportOptions);
    addEventListener(_type: 'message', callback: SnackTransportListener): void;
    removeEventListener(_type: 'message', _callback: SnackTransportListener): void;
    postMessage(message: SnackTransportMessage): void;
    /**
     * Start transport
     */
    protected abstract start(): void;
    /**
     * Stop transport
     */
    protected abstract stop(): void;
    /**
     * Is transport started
     */
    protected abstract isStarted(): boolean;
    /**
     * Send message to target channel
     */
    protected abstract sendAsync(channel: string, message: ProtocolOutgoingMessage): Promise<void>;
    /**
     * Verify whether the transport is able to send the code message with given size
     * @returns true if the code message size is valid
     */
    protected abstract onVerifyCodeMessageSize(codeMessage: ProtocolCodeMessage): boolean;
    /**
     * Helper function for derived transports.
     * When the derived transport receives message, it should call this function to process message data.
     */
    protected handleMessage(senderConnectionId: string, message: ProtocolIncomingMessage): void;
    /**
     * Helper function for derived transports.
     * When the derived transport receives channel join/leave events, it should call this function to process channel presence data.
     */
    protected handleChannelEvent(event: 'join' | 'leave', connectionId: string): void;
    private onProtocolMessage;
    private publish;
    private onSyntheticEvent;
    private onCodeMessageReady;
}
