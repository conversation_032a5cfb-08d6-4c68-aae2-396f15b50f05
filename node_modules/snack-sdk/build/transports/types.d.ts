import { SnackFiles, SnackDependencies, SDKVersion } from 'snack-content';
import { ProtocolOutgoingMessage, ProtocolIncomingMessage } from './Protocol';
export declare type SnackTransportStartMessage = {
    type: 'start';
};
export declare type SnackTransportStopMessage = {
    type: 'stop';
};
export declare type SnackTransportUpdateCodeMessage = {
    type: 'update_code';
    data: {
        files: SnackFiles;
        dependencies: SnackDependencies;
        sdkVersion: SDKVersion;
    };
};
export declare type SnackTransportProtocolMessage = {
    type: 'protocol_message';
    data: ProtocolOutgoingMessage;
};
export declare type SnackTransportSyntheticMessage = {
    type: 'synthetic_event';
    data: any;
};
export declare type SnackTransportMessage = SnackTransportStartMessage | SnackTransportStopMessage | SnackTransportUpdateCodeMessage | SnackTransportProtocolMessage | SnackTransportSyntheticMessage;
export declare type SnackTransportEventConnect = {
    type: 'connect';
    connectionId: string;
    data: any;
};
export declare type SnackTransportEventDisconnect = {
    type: 'disconnect';
    connectionId: string;
    data: any;
};
export declare type SnackTransportEventProtocolMessage = {
    type: 'protocol_message';
    connectionId: string;
    data: ProtocolIncomingMessage;
};
export declare type SnackTransportEventSendMessage = {
    type: 'send_message';
    data: ProtocolOutgoingMessage;
};
export declare type SnackTransportEventSyntheticMessage = {
    type: 'synthetic_event';
    data: any;
};
export declare type SnackTransportEvent = SnackTransportEventConnect | SnackTransportEventDisconnect | SnackTransportEventProtocolMessage | SnackTransportEventSendMessage | SnackTransportEventSyntheticMessage;
export declare type SnackTransportListener = (event: SnackTransportEvent) => void;
export interface SnackTransport {
    addEventListener(type: 'message', listener: SnackTransportListener): void;
    removeEventListener(type: 'message', listener: SnackTransportListener): void;
    postMessage(message: SnackTransportMessage): void;
}
export declare type SnackTransportOptions = {
    channel?: string;
    apiURL?: string;
    verbose?: boolean;
    name?: string;
    snackpubURL?: string | undefined;
};
