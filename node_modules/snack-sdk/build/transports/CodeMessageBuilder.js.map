{"version": 3, "file": "CodeMessageBuilder.js", "sourceRoot": "", "sources": ["../../src/transports/CodeMessageBuilder.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,6BAAmC;AACnC,+CAMuB;AACvB,6CAAwC;AAGxC,iEAAqE;AAErE,kDAAsD;AAQtD,IAAM,iBAAiB,GAAc;IACnC,KAAK,EAAE,kCAAkB,CAAC,KAAK;IAC/B,YAAY,EAAE,kCAAkB,CAAC,YAAY;IAC7C,UAAU,EAAE,kCAAkB,CAAC,UAAU;CAC1C,CAAC;AAIF,SAAgB,WAAW,CAAC,OAAe,EAAE,OAAe;IAC1D,IAAM,KAAK,GAAG,kBAAW,CAAC,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;QAC1D,OAAO,EAAE,CAAC;KACX,CAAC,CAAC;IACH,IAAI,KAAK,EAAE;QACT,OAAO,KAAK,CAAC;KACd;SAAM;QACL,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;KAC/C;AACH,CAAC;AATD,kCASC;AAED,IAAM,eAAe,GACnB,kIAAkI,CAAC;AAErI;IAWE,4BAAY,OAMX;QAND,iBAkBC;QA3BO,SAAI,GAAc,iBAAiB,CAAC;QAKpC,oBAAe,GAA+B,EAAE,CAAC;QACjD,iBAAY,GAA+B,EAAE,CAAC;QAiK9C,mBAAc,GAAyB,UAAC,OAAO,EAAE,SAAS,EAAE,MAAM;YAChE,IAAA,IAAI,GAAK,OAAO,KAAZ,CAAa;YACzB,OAAO,KAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAClC,IAAI,SAAS,EAAE;gBACb,KAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE;oBAC7C,IAAM,WAAW,GAAG,KAAI,CAAC,iBAAiB,CAAC,KAAI,CAAC,IAAI,EAAE,KAAI,CAAC,WAAW,EAAE,KAAI,CAAC,IAAI,CAAC,CAAC;oBACnF,IAAI,KAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;wBAC3C,KAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,KAAI,CAAC,IAAI,CAAC,CAAC;qBACvC;iBACF;aACF;QACH,CAAC,CAAC;QAnKA,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;QACjC,IAAI,CAAC,qBAAqB,GAAG,OAAO,CAAC,qBAAqB,CAAC;QAC3D,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;QACrD,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,MAAM;YAChC,CAAC,CAAC,IAAI,sBAAY,CAAC;gBACf,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,QAAQ,EAAE,IAAI,CAAC,cAAc;aAC9B,CAAC;YACJ,CAAC,CAAC,SAAS,CAAC;IAChB,CAAC;IAED,oCAAO,GAAP,UAAQ,IAAe;;QACrB,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YACtB,OAAO;SACR;QAED,IAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC;QACnB,IAAA,KAAK,GAAK,IAAI,MAAT,CAAU;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,4DAA4D;QAC5D,KAAK,IAAM,IAAI,IAAI,QAAQ,CAAC,KAAK,EAAE;YACjC,IAAI,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,YAAK,KAAK,CAAC,IAAI,CAAC,0CAAE,QAAQ,CAAA,EAAE;gBAC3D,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAClC,OAAO,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC/B,MAAA,IAAI,CAAC,YAAY,0CAAE,MAAM,CAAC,IAAI,EAAE,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;aACvD;SACF;QAED,wBAAwB;QACxB,IAAM,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAC7E,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QAE/B,+DAA+D;QAC/D,8CAA8C;QAC9C,IAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;YAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,MAAM,EAAE;gBAC7C,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;aAClC;YACD,OAAO;SACR;QAED,qDAAqD;QACrD,0BAA0B;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,IAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC;iBAC7B,MAAM,CAAC,UAAC,IAAI,IAAK,OAAA,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,KAAK,MAAM,EAA3B,CAA2B,CAAC;gBAC9C,aAAa;iBACZ,IAAI,CAAC,UAAC,CAAC,EAAE,CAAC,IAAK,OAAA,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAnD,CAAmD,CAAC,CAAC;YAEvE,0BAA0B;YAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBACtB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;oBAC3D,IAAM,IAAI,GAAkB,KAAK,CAAC,IAAI,CAAQ,CAAC;oBAC/C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC;oBAC7C,oFAAoF;oBACpF,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;oBAClC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;oBAC5B,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBACrD,IAAI,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,EAAE;wBAC3C,2DAA2D;wBAC3D,gCAAgC;wBAChC,OAAO;qBACR;iBACF;aACF;SACF;QAED,6BAA6B;QAC7B,MAAA,IAAI,CAAC,MAAM,0CAAE,KAAK,CAAC,2BAA2B,EAAE;IAClD,CAAC;IAEO,8CAAiB,GAAzB,UACE,IAAe,EACf,eAAqC,EACrC,QAAoB;QAEZ,IAAA,KAAK,GAA+B,IAAI,MAAnC,EAAE,YAAY,GAAiB,IAAI,aAArB,EAAE,UAAU,GAAK,IAAI,WAAT,CAAU;QAEjD,IAAM,IAAI,gBAAa,KAAK,CAAE,CAAC;QAC/B,IAAM,KAAK,GAAQ,EAAE,CAAC;QACtB,KAAK,IAAM,IAAI,IAAI,KAAK,EAAE;YACxB,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC;YACzB,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,EAAE;gBACzD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;gBAChB,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;aACrE;iBAAM;gBACL,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,EAAE;oBACxB,IACE,CAAA,eAAe,aAAf,eAAe,uBAAf,eAAe,CAAE,IAAI,CAAC,IAAI;wBAC1B,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,KAAK,CAAC,IAAI,EAAE,QAAQ,MAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,QAAQ,EAC5D;wBACA,IAAI,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;qBACzC;yBAAM;wBACL,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE;4BACrF,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,kBAAkB,CAAC;yBACtC;6BAAM;4BACL,IAAI,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;yBAC7C;qBACF;iBACF;qBAAM;oBACL,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;iBACjB;gBACD,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE;oBAC9D,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC;iBAC7B;aACF;SACF;QAED,IAAM,IAAI,GAAoC,EAAE,CAAC;QACjD,KAAK,IAAM,MAAI,IAAI,YAAY,EAAE;YAC/B,IAAM,GAAG,GAAG,YAAY,CAAC,MAAI,CAAC,CAAC;YAC/B,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,iCAAiB,CAAC,MAAI,EAAE,UAAU,CAAC,EAAE;gBACtD,IAAI,CAAC,MAAI,CAAC,GAAG;oBACX,OAAO,EAAE,GAAG,CAAC,OAAO;oBACpB,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,mFAAmF;oBACnF,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;iBAChE,CAAC;aACH;SACF;QAED,IAAM,QAAQ,GAAQ;YACpB,cAAc,EAAE,IAAI,CAAC,UAAU;SAGhC,CAAC;QACF,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,CAAC,SAAS,EAAE;YAC3D,IAAM,EAAE,GAAG,IAAI,uBAAQ,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,SAAS,EAAE,CAAC;YACzD,QAAQ,CAAC,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YAChD,QAAQ,CAAC,iBAAiB,GAAG,EAAE,CAAC,GAAG,CAAC,YAAY,CAAC;YACjD,QAAQ,CAAC,WAAW,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;YAClC,QAAQ,CAAC,YAAY,GAAG,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC;YACtC,QAAQ,CAAC,eAAe,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YAC1C,QAAQ,CAAC,aAAa,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC;YACxC,QAAQ,CAAC,UAAU,GAAG,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YACtC,QAAQ,CAAC,iBAAiB,GAAG,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;SACjD;QAED,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,IAAI,MAAA;YACJ,KAAK,OAAA;YACL,YAAY,EAAE,IAAI;YAClB,QAAQ,UAAA;SACT,CAAC;IACJ,CAAC;IAeH,yBAAC;AAAD,CAAC,AAtLD,IAsLC", "sourcesContent": ["import { createPatch } from 'diff';\nimport {\n  SnackFiles,\n  SDKVersion,\n  SnackCodeFile,\n  SnackDependencies,\n  isModulePreloaded,\n} from 'snack-content';\nimport { UAParser } from 'ua-parser-js';\n\nimport { ProtocolCodeMessage, ProtocolCodeMessageDependencies } from './Protocol';\nimport FileUploader, { FileUploaderCallback } from '../FileUploader';\nimport { Logger } from '../Logger';\nimport { SnackIdentityState } from '../defaultConfig';\n\nexport type SnackCode = {\n  files: SnackFiles;\n  dependencies: SnackDependencies;\n  sdkVersion: SDKVersion;\n};\n\nconst SnackIdentityCode: SnackCode = {\n  files: SnackIdentityState.files,\n  dependencies: SnackIdentityState.dependencies,\n  sdkVersion: SnackIdentityState.sdkVersion,\n};\n\nexport type CodeMessageBuilderCallback = (codeMessage: ProtocolCodeMessage, code: SnackCode) => any;\n\nexport function getFileDiff(oldCode: string, newCode: string): string {\n  const patch = createPatch('code', oldCode, newCode, '', '', {\n    context: 0,\n  });\n  if (patch) {\n    return patch;\n  } else {\n    throw new Error('Error creating a file diff');\n  }\n}\n\nconst PLACEHOLDER_URL =\n  'https://snack-code-uploads.s3.us-west-1.amazonaws.com/~asset/xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx';\n\nexport default class CodeMessageBuilder {\n  private callback: CodeMessageBuilderCallback;\n  private code: SnackCode = SnackIdentityCode;\n  private codeMessage?: ProtocolCodeMessage;\n  private logger?: Logger;\n  private codeUploader?: FileUploader;\n  private maxDiffPlaceholder?: string;\n  private placeholderURLs: { [path: string]: string } = {};\n  private uploadedURLs: { [path: string]: string } = {};\n  private verifyCodeMessageSize: (message: ProtocolCodeMessage) => boolean;\n\n  constructor(options: {\n    callback: CodeMessageBuilderCallback;\n    verifyCodeMessageSize: (message: ProtocolCodeMessage) => boolean;\n    apiURL?: string;\n    logger?: Logger;\n    maxDiffPlaceholder?: string;\n  }) {\n    this.logger = options.logger;\n    this.callback = options.callback;\n    this.verifyCodeMessageSize = options.verifyCodeMessageSize;\n    this.maxDiffPlaceholder = options.maxDiffPlaceholder;\n    this.codeUploader = options.apiURL\n      ? new FileUploader({\n          apiURL: options.apiURL,\n          logger: options.logger,\n          callback: this.onFileUploaded,\n        })\n      : undefined;\n  }\n\n  setCode(code: SnackCode) {\n    if (code === this.code) {\n      return;\n    }\n\n    const prevCode = this.code;\n    const { files } = code;\n    this.code = code;\n\n    // Cancel any uploads for files that were removed or changed\n    for (const path in prevCode.files) {\n      if (prevCode.files[path].contents !== files[path]?.contents) {\n        delete this.placeholderURLs[path];\n        delete this.uploadedURLs[path];\n        this.codeUploader?.remove(path, prevCode.files[path]);\n      }\n    }\n\n    // Generate code message\n    const codeMessage = this.createCodeMessage(code, this.codeMessage, prevCode);\n    this.codeMessage = codeMessage;\n\n    // When the code message size is valid and there are no pending\n    // uploads, then fire the callback immediately\n    if (this.verifyCodeMessageSize(codeMessage)) {\n      if (!Object.keys(this.placeholderURLs).length) {\n        this.callback(codeMessage, code);\n      }\n      return;\n    }\n\n    // Upload files if the code message exceeds the limit\n    // Sort code files by size\n    if (this.codeUploader) {\n      const paths = Object.keys(files)\n        .filter((path) => files[path].type === 'CODE')\n        // @ts-ignore\n        .sort((a, b) => files[b].contents.length - files[a].contents.length);\n\n      // Upload the largest file\n      for (let i = 0; i < paths.length; i++) {\n        const path = paths[i];\n        if (!this.uploadedURLs[path] && !this.placeholderURLs[path]) {\n          const file: SnackCodeFile = files[path] as any;\n          this.placeholderURLs[path] = PLACEHOLDER_URL;\n          // this.logger?.comm('Uploading file', path, `, ${file.contents.length} bytes ...`);\n          this.codeUploader.add(path, file);\n          codeMessage.diff[path] = '';\n          codeMessage.s3url[path] = this.placeholderURLs[path];\n          if (this.verifyCodeMessageSize(codeMessage)) {\n            // Message size is now valid. When the upload completes, it\n            // will try to call the callback\n            return;\n          }\n        }\n      }\n    }\n\n    // Message is still too large\n    this.logger?.error('Message size is too large');\n  }\n\n  private createCodeMessage(\n    code: SnackCode,\n    prevCodeMessage?: ProtocolCodeMessage,\n    prevCode?: SnackCode,\n  ): ProtocolCodeMessage {\n    const { files, dependencies, sdkVersion } = code;\n\n    const diff: any = { ...files };\n    const s3url: any = {};\n    for (const path in files) {\n      const file = files[path];\n      if (this.uploadedURLs[path] || this.placeholderURLs[path]) {\n        diff[path] = '';\n        s3url[path] = this.uploadedURLs[path] || this.placeholderURLs[path];\n      } else {\n        if (file.type === 'CODE') {\n          if (\n            prevCodeMessage?.diff[path] &&\n            prevCode?.files[path].contents === code.files[path].contents\n          ) {\n            diff[path] = prevCodeMessage.diff[path];\n          } else {\n            if (this.maxDiffPlaceholder && file.contents.length >= this.maxDiffPlaceholder.length) {\n              diff[path] = this.maxDiffPlaceholder;\n            } else {\n              diff[path] = getFileDiff('', file.contents);\n            }\n          }\n        } else {\n          diff[path] = '';\n        }\n        if (file.type === 'ASSET' && typeof file.contents === 'string') {\n          s3url[path] = file.contents;\n        }\n      }\n    }\n\n    const deps: ProtocolCodeMessageDependencies = {};\n    for (const name in dependencies) {\n      const dep = dependencies[name];\n      if (dep.handle && !isModulePreloaded(name, sdkVersion)) {\n        deps[name] = {\n          version: dep.version,\n          handle: dep.handle,\n          // Resolved has been replaced by handle. It is still needed for pre SDK 37 runtimes\n          resolved: dep.handle.substring(dep.handle.lastIndexOf('@') + 1),\n        };\n      }\n    }\n\n    const metadata: any = {\n      expoSDKVersion: code.sdkVersion,\n      // TODO: restore sdk version field\n      // webSnackSDKVersion: require('../../package.json').version,\n    };\n    if (typeof navigator !== 'undefined' && navigator.userAgent) {\n      const ua = new UAParser(navigator.userAgent).getResult();\n      metadata.webHostName = window.location.hostname;\n      metadata.webOSArchitecture = ua.cpu.architecture;\n      metadata.webOSFamily = ua.os.name;\n      metadata.webOSVersion = ua.os.version;\n      metadata.webLayoutEngine = ua.engine.name;\n      metadata.webDeviceType = ua.device.type;\n      metadata.webBrowser = ua.browser.name;\n      metadata.webBrowserVersion = ua.browser.version;\n    }\n\n    return {\n      type: 'CODE',\n      diff,\n      s3url,\n      dependencies: deps,\n      metadata,\n    };\n  }\n\n  private onFileUploaded: FileUploaderCallback = (request, resultURL, _error) => {\n    const { path } = request;\n    delete this.placeholderURLs[path];\n    if (resultURL) {\n      this.uploadedURLs[path] = resultURL;\n      if (!Object.keys(this.placeholderURLs).length) {\n        const codeMessage = this.createCodeMessage(this.code, this.codeMessage, this.code);\n        if (this.verifyCodeMessageSize(codeMessage)) {\n          this.callback(codeMessage, this.code);\n        }\n      }\n    }\n  };\n}\n"]}