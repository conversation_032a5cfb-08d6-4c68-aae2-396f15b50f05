{"version": 3, "file": "TrafficMirroringTransport.js", "sourceRoot": "", "sources": ["../../src/transports/TrafficMirroringTransport.ts"], "names": [], "mappings": ";;;;;AAAA,8EAAwD;AACxD,kFAA4D;AAQ5D;IAGE,mCAAY,OAA8B;QACxC,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,6BAAmB,CAAC,OAAO,CAAC,EAAE,IAAI,+BAAqB,CAAC,OAAO,CAAC,CAAC,CAAC;IAC3F,CAAC;IAED,oDAAgB,GAAhB,UAAiB,IAAe,EAAE,QAAgC;QAChE,KAAwB,UAAe,EAAf,KAAA,IAAI,CAAC,UAAU,EAAf,cAAe,EAAf,IAAe,EAAE;YAApC,IAAM,SAAS,SAAA;YAClB,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC5C;IACH,CAAC;IAED,uDAAmB,GAAnB,UAAoB,IAAe,EAAE,QAAgC;QACnE,KAAwB,UAAe,EAAf,KAAA,IAAI,CAAC,UAAU,EAAf,cAAe,EAAf,IAAe,EAAE;YAApC,IAAM,SAAS,SAAA;YAClB,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,+CAAW,GAAX,UAAY,OAA8B;QACxC,KAAwB,UAAe,EAAf,KAAA,IAAI,CAAC,UAAU,EAAf,cAAe,EAAf,IAAe,EAAE;YAApC,IAAM,SAAS,SAAA;YAClB,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAChC;IACH,CAAC;IACH,gCAAC;AAAD,CAAC,AAxBD,IAwBC", "sourcesContent": ["import TransportImplPubNub from './TransportImplPubNub';\nimport TransportImplSocketIO from './TransportImplSocketIO';\nimport {\n  SnackTransport,\n  SnackTransportListener,\n  SnackTransportMessage,\n  SnackTransportOptions,\n} from './types';\n\nexport default class TrafficMirroringTransport implements SnackTransport {\n  private readonly transports: SnackTransport[];\n\n  constructor(options: SnackTransportOptions) {\n    this.transports = [new TransportImplPubNub(options), new TransportImplSocketIO(options)];\n  }\n\n  addEventListener(type: 'message', listener: SnackTransportListener): void {\n    for (const transport of this.transports) {\n      transport.addEventListener(type, listener);\n    }\n  }\n\n  removeEventListener(type: 'message', listener: SnackTransportListener): void {\n    for (const transport of this.transports) {\n      transport.removeEventListener(type, listener);\n    }\n  }\n\n  postMessage(message: SnackTransportMessage): void {\n    for (const transport of this.transports) {\n      transport.postMessage(message);\n    }\n  }\n}\n"]}