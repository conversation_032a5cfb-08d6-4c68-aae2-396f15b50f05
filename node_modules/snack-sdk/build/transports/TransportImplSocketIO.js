"use strict";
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var socket_io_client_1 = require("socket.io-client");
var ConnectionMetricsEmitter_1 = __importDefault(require("./ConnectionMetricsEmitter"));
var TransportImplBase_1 = __importDefault(require("./TransportImplBase"));
var TransportImplSocketIO = /** @class */ (function (_super) {
    __extends(TransportImplSocketIO, _super);
    function TransportImplSocketIO(options) {
        var _this = _super.call(this, options) || this;
        _this.socket = null;
        _this.onMessage = function (data) {
            var sender = data.sender, message = data.message;
            _this.handleMessage(sender, message);
        };
        _this.onJoinChannel = function (data) {
            var _a;
            var sender = data.sender;
            if (sender !== '' && sender !== ((_a = _this.socket) === null || _a === void 0 ? void 0 : _a.id)) {
                _this.handleChannelEvent('join', sender);
            }
        };
        _this.onLeaveChannel = function (data) {
            var _a;
            var sender = data.sender;
            if (sender !== '' && sender !== ((_a = _this.socket) === null || _a === void 0 ? void 0 : _a.id)) {
                _this.handleChannelEvent('leave', sender);
            }
        };
        var snackpubURL = options.snackpubURL;
        if (!snackpubURL) {
            throw new Error('The `snackpubURL` option is unspecified.');
        }
        _this.snackpubURL = snackpubURL;
        _this.connectionAttempts = 0;
        return _this;
    }
    TransportImplSocketIO.prototype.start = function () {
        var _this = this;
        this.stop();
        this.startTime = Date.now();
        this.socket = socket_io_client_1.io(this.snackpubURL, { transports: ['websocket'] });
        this.socket.on('connect', function () {
            var _a, _b;
            (_a = _this.socket) === null || _a === void 0 ? void 0 : _a.emit('subscribeChannel', { channel: _this.channel, sender: (_b = _this.socket) === null || _b === void 0 ? void 0 : _b.id });
            if (_this.startTime) {
                ConnectionMetricsEmitter_1.default.emitSuccessed({
                    timeMs: Date.now() - _this.startTime,
                    attempts: _this.connectionAttempts,
                });
            }
        });
        this.socket.io.on('reconnect_attempt', function (attempts) {
            _this.connectionAttempts = attempts;
            if (_this.startTime) {
                ConnectionMetricsEmitter_1.default.emitFailed({
                    timeMs: Date.now() - _this.startTime,
                    attempts: attempts,
                });
            }
        });
        this.socket.on('message', this.onMessage);
        this.socket.on('joinChannel', this.onJoinChannel);
        this.socket.on('leaveChannel', this.onLeaveChannel);
        this.socket.on('terminate', function (reason) {
            var _a, _b;
            (_a = _this.logger) === null || _a === void 0 ? void 0 : _a.comm("Terminating connection: " + reason);
            (_b = _this.socket) === null || _b === void 0 ? void 0 : _b.disconnect();
        });
    };
    TransportImplSocketIO.prototype.stop = function () {
        var _a;
        if (this.socket != null) {
            (_a = this.logger) === null || _a === void 0 ? void 0 : _a.comm('Stopping...', this.logSuffix);
            this.socket.offAny();
            this.socket.close();
            this.socket = null;
        }
        this.startTime = undefined;
    };
    TransportImplSocketIO.prototype.isStarted = function () {
        return this.socket != null;
    };
    TransportImplSocketIO.prototype.sendAsync = function (channel, message) {
        var _a;
        (_a = this.socket) === null || _a === void 0 ? void 0 : _a.emit('message', {
            channel: channel,
            message: message,
            sender: this.socket.id,
        });
        return Promise.resolve();
    };
    TransportImplSocketIO.prototype.onVerifyCodeMessageSize = function (codeMessage) {
        // Calculate unencoded size (quickly) and if that exceeds the limit
        // then don't bother calculating the exact size (which is more expensive)
        var approxSize = 0;
        for (var path in codeMessage.diff) {
            approxSize += path.length + codeMessage.diff[path].length;
        }
        return approxSize < TransportImplBase_1.default.CODE_SIZE_LIMIT_FOR_DIFF;
    };
    return TransportImplSocketIO;
}(TransportImplBase_1.default));
exports.default = TransportImplSocketIO;
//# sourceMappingURL=TransportImplSocketIO.js.map