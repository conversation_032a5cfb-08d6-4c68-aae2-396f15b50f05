{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/transports/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,0FAAoE;AACpE,kFAA4D;AAG5D,0CAAwB;AACxB,6DAA2C;AAC3C,uEAAiF;AAAxE,qJAAA,OAAO,OAA4B;AAE5C,SAAgB,eAAe,CAAC,OAA8B;IAC5D,OAAO,IAAI,+BAAqB,CAAC,OAAO,CAAC,CAAC;AAC5C,CAAC;AAFD,0CAEC;AAED,SAAgB,+BAA+B,CAAC,OAA8B;IAC5E,OAAO,IAAI,mCAAyB,CAAC,OAAO,CAAC,CAAC;AAChD,CAAC;AAFD,0EAEC", "sourcesContent": ["import TrafficMirroringTransport from './TrafficMirroringTransport';\nimport TransportImplSocketIO from './TransportImplSocketIO';\nimport { SnackTransport, SnackTransportOptions } from './types';\n\nexport * from './types';\nexport * from './ConnectionMetricsEmitter';\nexport { default as ConnectionMetricsEmitter } from './ConnectionMetricsEmitter';\n\nexport function createTransport(options: SnackTransportOptions): SnackTransport {\n  return new TransportImplSocketIO(options);\n}\n\nexport function createTrafficMirroringTransport(options: SnackTransportOptions): SnackTransport {\n  return new TrafficMirroringTransport(options);\n}\n"]}