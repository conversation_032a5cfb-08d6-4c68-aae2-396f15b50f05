!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).PubNub=t()}(this,(function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};function t(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var n=function(){return n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},n.apply(this,arguments)};function r(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}function o(e,t,n,r){return new(n||(n=Promise))((function(o,i){function s(e){try{u(r.next(e))}catch(e){i(e)}}function a(e){try{u(r.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))}function i(e,t){var n,r,o,i,s={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return i={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(i[Symbol.iterator]=function(){return this}),i;function a(i){return function(a){return function(i){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return s.label++,{value:i[1],done:!1};case 5:s.label++,r=i[1],i=[0];continue;case 7:i=s.ops.pop(),s.trys.pop();continue;default:if(!(o=s.trys,(o=o.length>0&&o[o.length-1])||6!==i[0]&&2!==i[0])){s=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){s.label=i[1];break}if(6===i[0]&&s.label<o[1]){s.label=o[1],o=i;break}if(o&&s.label<o[2]){s.label=o[2],s.ops.push(i);break}o[2]&&s.ops.pop(),s.trys.pop();continue}i=t.call(e,s)}catch(e){i=[6,e],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function s(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function a(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,o,i=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=i.next()).done;)s.push(r.value)}catch(e){o={error:e}}finally{try{r&&!r.done&&(n=i.return)&&n.call(i)}finally{if(o)throw o.error}}return s}function u(e,t,n){if(n||2===arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}var c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function l(e){if(e.__esModule)return e;var t=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(e).forEach((function(n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})})),t}var p={exports:{}};!function(e){!function(t,n){var r=Math.pow(2,-24),o=Math.pow(2,32),i=Math.pow(2,53);var s={encode:function(e){var t,r=new ArrayBuffer(256),s=new DataView(r),a=0;function u(e){for(var n=r.byteLength,o=a+e;n<o;)n*=2;if(n!==r.byteLength){var i=s;r=new ArrayBuffer(n),s=new DataView(r);for(var u=a+3>>2,c=0;c<u;++c)s.setUint32(4*c,i.getUint32(4*c))}return t=e,s}function c(){a+=t}function l(e){c(u(1).setUint8(a,e))}function p(e){for(var t=u(e.length),n=0;n<e.length;++n)t.setUint8(a+n,e[n]);c()}function h(e,t){t<24?l(e<<5|t):t<256?(l(e<<5|24),l(t)):t<65536?(l(e<<5|25),function(e){c(u(2).setUint16(a,e))}(t)):t<4294967296?(l(e<<5|26),function(e){c(u(4).setUint32(a,e))}(t)):(l(e<<5|27),function(e){var t=e%o,n=(e-t)/o,r=u(8);r.setUint32(a,n),r.setUint32(a+4,t),c()}(t))}if(function e(t){var r;if(!1===t)return l(244);if(!0===t)return l(245);if(null===t)return l(246);if(t===n)return l(247);switch(typeof t){case"number":if(Math.floor(t)===t){if(0<=t&&t<=i)return h(0,t);if(-i<=t&&t<0)return h(1,-(t+1))}return l(251),function(e){c(u(8).setFloat64(a,e))}(t);case"string":var o=[];for(r=0;r<t.length;++r){var s=t.charCodeAt(r);s<128?o.push(s):s<2048?(o.push(192|s>>6),o.push(128|63&s)):s<55296?(o.push(224|s>>12),o.push(128|s>>6&63),o.push(128|63&s)):(s=(1023&s)<<10,s|=1023&t.charCodeAt(++r),s+=65536,o.push(240|s>>18),o.push(128|s>>12&63),o.push(128|s>>6&63),o.push(128|63&s))}return h(3,o.length),p(o);default:var f;if(Array.isArray(t))for(h(4,f=t.length),r=0;r<f;++r)e(t[r]);else if(t instanceof Uint8Array)h(2,t.length),p(t);else{var d=Object.keys(t);for(h(5,f=d.length),r=0;r<f;++r){var y=d[r];e(y),e(t[y])}}}}(e),"slice"in r)return r.slice(0,a);for(var f=new ArrayBuffer(a),d=new DataView(f),y=0;y<a;++y)d.setUint8(y,s.getUint8(y));return f},decode:function(e,t,i){var s=new DataView(e),a=0;function u(e,t){return a+=t,e}function c(t){return u(new Uint8Array(e,a,t),t)}function l(){return u(s.getUint8(a),1)}function p(){return u(s.getUint16(a),2)}function h(){return u(s.getUint32(a),4)}function f(){return 255===s.getUint8(a)&&(a+=1,!0)}function d(e){if(e<24)return e;if(24===e)return l();if(25===e)return p();if(26===e)return h();if(27===e)return h()*o+h();if(31===e)return-1;throw"Invalid length encoding"}function y(e){var t=l();if(255===t)return-1;var n=d(31&t);if(n<0||t>>5!==e)throw"Invalid indefinite length element";return n}function g(e,t){for(var n=0;n<t;++n){var r=l();128&r&&(r<224?(r=(31&r)<<6|63&l(),t-=1):r<240?(r=(15&r)<<12|(63&l())<<6|63&l(),t-=2):(r=(15&r)<<18|(63&l())<<12|(63&l())<<6|63&l(),t-=3)),r<65536?e.push(r):(r-=65536,e.push(55296|r>>10),e.push(56320|1023&r))}}"function"!=typeof t&&(t=function(e){return e}),"function"!=typeof i&&(i=function(){return n});var m=function e(){var o,h,m=l(),b=m>>5,v=31&m;if(7===b)switch(v){case 25:return function(){var e=new ArrayBuffer(4),t=new DataView(e),n=p(),o=32768&n,i=31744&n,s=1023&n;if(31744===i)i=261120;else if(0!==i)i+=114688;else if(0!==s)return s*r;return t.setUint32(0,o<<16|i<<13|s<<13),t.getFloat32(0)}();case 26:return u(s.getFloat32(a),4);case 27:return u(s.getFloat64(a),8)}if((h=d(v))<0&&(b<2||6<b))throw"Invalid length";switch(b){case 0:return h;case 1:return-1-h;case 2:if(h<0){for(var _=[],S=0;(h=y(b))>=0;)S+=h,_.push(c(h));var w=new Uint8Array(S),O=0;for(o=0;o<_.length;++o)w.set(_[o],O),O+=_[o].length;return w}return c(h);case 3:var P=[];if(h<0)for(;(h=y(b))>=0;)g(P,h);else g(P,h);return String.fromCharCode.apply(null,P);case 4:var E;if(h<0)for(E=[];!f();)E.push(e());else for(E=new Array(h),o=0;o<h;++o)E[o]=e();return E;case 5:var A={};for(o=0;o<h||h<0&&!f();++o){A[e()]=e()}return A;case 6:return t(e(),h);case 7:switch(h){case 20:return!1;case 21:return!0;case 22:return null;case 23:return n;default:return i(h)}}}();if(a!==e.byteLength)throw"Remaining bytes";return m}};e.exports?e.exports=s:t.CBOR||(t.CBOR=s)}(c)}(p);var h=p.exports,f={exports:{}};
/*! lil-uuid - v0.1 - MIT License - https://github.com/lil-js/uuid */
!function(e,t){!function(e){var t="0.1.0",n={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};function r(){var e,t,n="";for(e=0;e<32;e++)t=16*Math.random()|0,8!==e&&12!==e&&16!==e&&20!==e||(n+="-"),n+=(12===e?4:16===e?3&t|8:t).toString(16);return n}function o(e,t){var r=n[t||"all"];return r&&r.test(e)||!1}r.isUUID=o,r.VERSION=t,e.uuid=r,e.isUUID=o}(t),null!==e&&(e.exports=t.uuid)}(f,f.exports);var d=f.exports,y=function(){return d.uuid?d.uuid():d()},g=function(){function e(e){var t,n,r,o,i=e.setup;if(this._PNSDKSuffix={},this.instanceId="pn-".concat(y()),this.secretKey=i.secretKey||i.secret_key,this.subscribeKey=i.subscribeKey||i.subscribe_key,this.publishKey=i.publishKey||i.publish_key,this.sdkName=i.sdkName,this.sdkFamily=i.sdkFamily,this.partnerId=i.partnerId,this.setAuthKey(i.authKey),this.cryptoModule=i.cryptoModule,this.setFilterExpression(i.filterExpression),"string"!=typeof i.origin&&!Array.isArray(i.origin)&&void 0!==i.origin)throw new Error("Origin must be either undefined, a string or a list of strings.");if(this.origin=i.origin||Array.from({length:20},(function(e,t){return"ps".concat(t+1,".pndsn.com")})),this.secure=i.ssl||!1,this.restore=i.restore||!1,this.proxy=i.proxy,this.keepAlive=i.keepAlive,this.keepAliveSettings=i.keepAliveSettings,this.autoNetworkDetection=i.autoNetworkDetection||!1,this.dedupeOnSubscribe=i.dedupeOnSubscribe||!1,this.maximumCacheSize=i.maximumCacheSize||100,this.customEncrypt=i.customEncrypt,this.customDecrypt=i.customDecrypt,this.fileUploadPublishRetryLimit=null!==(t=i.fileUploadPublishRetryLimit)&&void 0!==t?t:5,this.useRandomIVs=null===(n=i.useRandomIVs)||void 0===n||n,this.enableEventEngine=null!==(r=i.enableEventEngine)&&void 0!==r&&r,this.maintainPresenceState=null===(o=i.maintainPresenceState)||void 0===o||o,"undefined"!=typeof location&&"https:"===location.protocol&&(this.secure=!0),this.logVerbosity=i.logVerbosity||!1,this.suppressLeaveEvents=i.suppressLeaveEvents||!1,this.announceFailedHeartbeats=i.announceFailedHeartbeats||!0,this.announceSuccessfulHeartbeats=i.announceSuccessfulHeartbeats||!1,this.useInstanceId=i.useInstanceId||!1,this.useRequestId=i.useRequestId||!1,this.requestMessageCountThreshold=i.requestMessageCountThreshold,i.retryConfiguration&&this._setRetryConfiguration(i.retryConfiguration),this.setTransactionTimeout(i.transactionalRequestTimeout||15e3),this.setSubscribeTimeout(i.subscribeRequestTimeout||31e4),this.setSendBeaconConfig(i.useSendBeacon||!0),i.presenceTimeout?this.setPresenceTimeout(i.presenceTimeout):this._presenceTimeout=300,null!=i.heartbeatInterval&&this.setHeartbeatInterval(i.heartbeatInterval),"string"==typeof i.userId){if("string"==typeof i.uuid)throw new Error("Only one of the following configuration options has to be provided: `uuid` or `userId`");this.setUserId(i.userId)}else{if("string"!=typeof i.uuid)throw new Error("One of the following configuration options has to be provided: `uuid` or `userId`");this.setUUID(i.uuid)}this.setCipherKey(i.cipherKey,i)}return e.prototype.getAuthKey=function(){return this.authKey},e.prototype.setAuthKey=function(e){return this.authKey=e,this},e.prototype.setCipherKey=function(e,t,n){var r;return this.cipherKey=e,this.cipherKey&&(this.cryptoModule=null!==(r=t.cryptoModule)&&void 0!==r?r:t.initCryptoModule({cipherKey:this.cipherKey,useRandomIVs:this.useRandomIVs}),n&&(n.cryptoModule=this.cryptoModule)),this},e.prototype.getUUID=function(){return this.UUID},e.prototype.setUUID=function(e){if(!e||"string"!=typeof e||0===e.trim().length)throw new Error("Missing uuid parameter. Provide a valid string uuid");return this.UUID=e,this},e.prototype.getUserId=function(){return this.UUID},e.prototype.setUserId=function(e){if(!e||"string"!=typeof e||0===e.trim().length)throw new Error("Missing or invalid userId parameter. Provide a valid string userId");return this.UUID=e,this},e.prototype.getFilterExpression=function(){return this.filterExpression},e.prototype.setFilterExpression=function(e){return this.filterExpression=e,this},e.prototype.getPresenceTimeout=function(){return this._presenceTimeout},e.prototype.setPresenceTimeout=function(e){return e>=20?this._presenceTimeout=e:(this._presenceTimeout=20,console.log("WARNING: Presence timeout is less than the minimum. Using minimum value: ",this._presenceTimeout)),this.setHeartbeatInterval(this._presenceTimeout/2-1),this},e.prototype.setProxy=function(e){this.proxy=e},e.prototype.getHeartbeatInterval=function(){return this._heartbeatInterval},e.prototype.setHeartbeatInterval=function(e){return this._heartbeatInterval=e,this},e.prototype.getSubscribeTimeout=function(){return this._subscribeRequestTimeout},e.prototype.setSubscribeTimeout=function(e){return this._subscribeRequestTimeout=e,this},e.prototype.getTransactionTimeout=function(){return this._transactionalRequestTimeout},e.prototype.setTransactionTimeout=function(e){return this._transactionalRequestTimeout=e,this},e.prototype.isSendBeaconEnabled=function(){return this._useSendBeacon},e.prototype.setSendBeaconConfig=function(e){return this._useSendBeacon=e,this},e.prototype.getVersion=function(){return"7.6.3"},e.prototype._setRetryConfiguration=function(e){if(e.minimumdelay<2)throw new Error("Minimum delay can not be set less than 2 seconds for retry");if(e.maximumDelay>150)throw new Error("Maximum delay can not be set more than 150 seconds for retry");if(e.maximumDelay&&maximumRetry>6)throw new Error("Maximum retry for exponential retry policy can not be more than 6");if(e.maximumRetry>10)throw new Error("Maximum retry for linear retry policy can not be more than 10");this.retryConfiguration=e},e.prototype._addPnsdkSuffix=function(e,t){this._PNSDKSuffix[e]=t},e.prototype._getPnsdkSuffix=function(e){var t=this;return Object.keys(this._PNSDKSuffix).reduce((function(n,r){return n+e+t._PNSDKSuffix[r]}),"")},e}();function m(e){var t=e.replace(/==?$/,""),n=Math.floor(t.length/4*3),r=new ArrayBuffer(n),o=new Uint8Array(r),i=0;function s(){var e=t.charAt(i++),n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(e);if(-1===n)throw new Error("Illegal character at ".concat(i,": ").concat(t.charAt(i-1)));return n}for(var a=0;a<n;a+=3){var u=s(),c=s(),l=s(),p=s(),h=(63&u)<<2|c>>4,f=(15&c)<<4|l>>2,d=(3&l)<<6|p>>0;o[a]=h,64!=l&&(o[a+1]=f),64!=p&&(o[a+2]=d)}return r}function b(e){for(var t,n="",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=new Uint8Array(e),i=o.byteLength,s=i%3,a=i-s,u=0;u<a;u+=3)n+=r[(16515072&(t=o[u]<<16|o[u+1]<<8|o[u+2]))>>18]+r[(258048&t)>>12]+r[(4032&t)>>6]+r[63&t];return 1==s?n+=r[(252&(t=o[a]))>>2]+r[(3&t)<<4]+"==":2==s&&(n+=r[(64512&(t=o[a]<<8|o[a+1]))>>10]+r[(1008&t)>>4]+r[(15&t)<<2]+"="),n}var v,_,S,w,O,P=P||function(e,t){var n={},r=n.lib={},o=function(){},i=r.Base={extend:function(e){o.prototype=this;var t=new o;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},s=r.WordArray=i.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes;if(e=e.sigBytes,this.clamp(),r%4)for(var o=0;o<e;o++)t[r+o>>>2]|=(n[o>>>2]>>>24-o%4*8&255)<<24-(r+o)%4*8;else if(65535<n.length)for(o=0;o<e;o+=4)t[r+o>>>2]=n[o>>>2];else t.push.apply(t,n);return this.sigBytes+=e,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=i.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n=[],r=0;r<t;r+=4)n.push(4294967296*e.random()|0);return new s.init(n,t)}}),a=n.enc={},u=a.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++){var o=t[r>>>2]>>>24-r%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new s.init(n,t/2)}},c=a.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++)n.push(String.fromCharCode(t[r>>>2]>>>24-r%4*8&255));return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new s.init(n,t)}},l=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},p=r.BufferedBlockAlgorithm=i.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,o=n.sigBytes,i=this.blockSize,a=o/(4*i);if(t=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*i,o=e.min(4*t,o),t){for(var u=0;u<t;u+=i)this._doProcessBlock(r,u);u=r.splice(0,t),n.sigBytes-=o}return new s.init(u,o)},clone:function(){var e=i.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=p.extend({cfg:i.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=n.algo={};return n}(Math);!function(e){for(var t=P,n=(o=t.lib).WordArray,r=o.Hasher,o=t.algo,i=[],s=[],a=function(e){return 4294967296*(e-(0|e))|0},u=2,c=0;64>c;){var l;e:{l=u;for(var p=e.sqrt(l),h=2;h<=p;h++)if(!(l%h)){l=!1;break e}l=!0}l&&(8>c&&(i[c]=a(e.pow(u,.5))),s[c]=a(e.pow(u,1/3)),c++),u++}var f=[];o=o.SHA256=r.extend({_doReset:function(){this._hash=new n.init(i.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],o=n[1],i=n[2],a=n[3],u=n[4],c=n[5],l=n[6],p=n[7],h=0;64>h;h++){if(16>h)f[h]=0|e[t+h];else{var d=f[h-15],y=f[h-2];f[h]=((d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3)+f[h-7]+((y<<15|y>>>17)^(y<<13|y>>>19)^y>>>10)+f[h-16]}d=p+((u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25))+(u&c^~u&l)+s[h]+f[h],y=((r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22))+(r&o^r&i^o&i),p=l,l=c,c=u,u=a+d|0,a=i,i=o,o=r,r=d+y|0}n[0]=n[0]+r|0,n[1]=n[1]+o|0,n[2]=n[2]+i|0,n[3]=n[3]+a|0,n[4]=n[4]+u|0,n[5]=n[5]+c|0,n[6]=n[6]+l|0,n[7]=n[7]+p|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=e.floor(r/4294967296),n[15+(o+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(o),t.HmacSHA256=r._createHmacHelper(o)}(Math),_=(v=P).enc.Utf8,v.algo.HMAC=v.lib.Base.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=_.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var o=this._oKey=t.clone(),i=this._iKey=t.clone(),s=o.words,a=i.words,u=0;u<n;u++)s[u]^=1549556828,a[u]^=909522486;o.sigBytes=i.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}}),w=(S=P).lib.WordArray,S.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp(),e=[];for(var o=0;o<n;o+=3)for(var i=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;4>s&&o+.75*s<n;s++)e.push(r.charAt(i>>>6*(3-s)&63));if(t=r.charAt(64))for(;e.length%4;)e.push(t);return e.join("")},parse:function(e){var t=e.length,n=this._map;(r=n.charAt(64))&&-1!=(r=e.indexOf(r))&&(t=r);for(var r=[],o=0,i=0;i<t;i++)if(i%4){var s=n.indexOf(e.charAt(i-1))<<i%4*2,a=n.indexOf(e.charAt(i))>>>6-i%4*2;r[o>>>2]|=(s|a)<<24-o%4*8,o++}return w.create(r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},function(e){function t(e,t,n,r,o,i,s){return((e=e+(t&n|~t&r)+o+s)<<i|e>>>32-i)+t}function n(e,t,n,r,o,i,s){return((e=e+(t&r|n&~r)+o+s)<<i|e>>>32-i)+t}function r(e,t,n,r,o,i,s){return((e=e+(t^n^r)+o+s)<<i|e>>>32-i)+t}function o(e,t,n,r,o,i,s){return((e=e+(n^(t|~r))+o+s)<<i|e>>>32-i)+t}for(var i=P,s=(u=i.lib).WordArray,a=u.Hasher,u=i.algo,c=[],l=0;64>l;l++)c[l]=4294967296*e.abs(e.sin(l+1))|0;u=u.MD5=a.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,i){for(var s=0;16>s;s++){var a=e[u=i+s];e[u]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}s=this._hash.words;var u=e[i+0],l=(a=e[i+1],e[i+2]),p=e[i+3],h=e[i+4],f=e[i+5],d=e[i+6],y=e[i+7],g=e[i+8],m=e[i+9],b=e[i+10],v=e[i+11],_=e[i+12],S=e[i+13],w=e[i+14],O=e[i+15],P=t(P=s[0],T=s[1],A=s[2],E=s[3],u,7,c[0]),E=t(E,P,T,A,a,12,c[1]),A=t(A,E,P,T,l,17,c[2]),T=t(T,A,E,P,p,22,c[3]);P=t(P,T,A,E,h,7,c[4]),E=t(E,P,T,A,f,12,c[5]),A=t(A,E,P,T,d,17,c[6]),T=t(T,A,E,P,y,22,c[7]),P=t(P,T,A,E,g,7,c[8]),E=t(E,P,T,A,m,12,c[9]),A=t(A,E,P,T,b,17,c[10]),T=t(T,A,E,P,v,22,c[11]),P=t(P,T,A,E,_,7,c[12]),E=t(E,P,T,A,S,12,c[13]),A=t(A,E,P,T,w,17,c[14]),P=n(P,T=t(T,A,E,P,O,22,c[15]),A,E,a,5,c[16]),E=n(E,P,T,A,d,9,c[17]),A=n(A,E,P,T,v,14,c[18]),T=n(T,A,E,P,u,20,c[19]),P=n(P,T,A,E,f,5,c[20]),E=n(E,P,T,A,b,9,c[21]),A=n(A,E,P,T,O,14,c[22]),T=n(T,A,E,P,h,20,c[23]),P=n(P,T,A,E,m,5,c[24]),E=n(E,P,T,A,w,9,c[25]),A=n(A,E,P,T,p,14,c[26]),T=n(T,A,E,P,g,20,c[27]),P=n(P,T,A,E,S,5,c[28]),E=n(E,P,T,A,l,9,c[29]),A=n(A,E,P,T,y,14,c[30]),P=r(P,T=n(T,A,E,P,_,20,c[31]),A,E,f,4,c[32]),E=r(E,P,T,A,g,11,c[33]),A=r(A,E,P,T,v,16,c[34]),T=r(T,A,E,P,w,23,c[35]),P=r(P,T,A,E,a,4,c[36]),E=r(E,P,T,A,h,11,c[37]),A=r(A,E,P,T,y,16,c[38]),T=r(T,A,E,P,b,23,c[39]),P=r(P,T,A,E,S,4,c[40]),E=r(E,P,T,A,u,11,c[41]),A=r(A,E,P,T,p,16,c[42]),T=r(T,A,E,P,d,23,c[43]),P=r(P,T,A,E,m,4,c[44]),E=r(E,P,T,A,_,11,c[45]),A=r(A,E,P,T,O,16,c[46]),P=o(P,T=r(T,A,E,P,l,23,c[47]),A,E,u,6,c[48]),E=o(E,P,T,A,y,10,c[49]),A=o(A,E,P,T,w,15,c[50]),T=o(T,A,E,P,f,21,c[51]),P=o(P,T,A,E,_,6,c[52]),E=o(E,P,T,A,p,10,c[53]),A=o(A,E,P,T,b,15,c[54]),T=o(T,A,E,P,a,21,c[55]),P=o(P,T,A,E,g,6,c[56]),E=o(E,P,T,A,O,10,c[57]),A=o(A,E,P,T,d,15,c[58]),T=o(T,A,E,P,S,21,c[59]),P=o(P,T,A,E,h,6,c[60]),E=o(E,P,T,A,v,10,c[61]),A=o(A,E,P,T,l,15,c[62]),T=o(T,A,E,P,m,21,c[63]);s[0]=s[0]+P|0,s[1]=s[1]+T|0,s[2]=s[2]+A|0,s[3]=s[3]+E|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,o=8*t.sigBytes;n[o>>>5]|=128<<24-o%32;var i=e.floor(r/4294967296);for(n[15+(o+64>>>9<<4)]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8),n[14+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(n.length+1),this._process(),n=(t=this._hash).words,r=0;4>r;r++)o=n[r],n[r]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8);return t},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),i.MD5=a._createHelper(u),i.HmacMD5=a._createHmacHelper(u)}(Math),function(){var e,t=P,n=(e=t.lib).Base,r=e.WordArray,o=(e=t.algo).EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:e.MD5,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=(a=this.cfg).hasher.create(),o=r.create(),i=o.words,s=a.keySize,a=a.iterations;i.length<s;){u&&n.update(u);var u=n.update(e).finalize(t);n.reset();for(var c=1;c<a;c++)u=n.finalize(u),n.reset();o.concat(u)}return o.sigBytes=4*s,o}});t.EvpKDF=function(e,t,n){return o.create(n).compute(e,t)}}(),P.lib.Cipher||function(e){var t=(f=P).lib,n=t.Base,r=t.WordArray,o=t.BufferedBlockAlgorithm,i=f.enc.Base64,s=f.algo.EvpKDF,a=t.Cipher=o.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){o.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(e){return{encrypt:function(t,n,r){return("string"==typeof n?d:h).encrypt(e,t,n,r)},decrypt:function(t,n,r){return("string"==typeof n?d:h).decrypt(e,t,n,r)}}}});t.StreamCipher=a.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=f.mode={},c=function(e,t,n){var r=this._iv;r?this._iv=undefined:r=this._prevBlock;for(var o=0;o<n;o++)e[t+o]^=r[o]},l=(t.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}})).extend();l.Encryptor=l.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize;c.call(this,e,t,r),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+r)}}),l.Decryptor=l.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,o=e.slice(t,t+r);n.decryptBlock(e,t),c.call(this,e,t,r),this._prevBlock=o}}),u=u.CBC=l,l=(f.pad={}).Pkcs7={pad:function(e,t){for(var n,o=(n=(n=4*t)-e.sigBytes%n)<<24|n<<16|n<<8|n,i=[],s=0;s<n;s+=4)i.push(o);n=r.create(i,n),e.concat(n)},unpad:function(e){e.sigBytes-=255&e.words[e.sigBytes-1>>>2]}},t.BlockCipher=a.extend({cfg:a.cfg.extend({mode:u,padding:l}),reset:function(){a.reset.call(this);var e=(t=this.cfg).iv,t=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var n=t.createEncryptor;else n=t.createDecryptor,this._minBufferSize=1;this._mode=n.call(t,this,e&&e.words)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){e.pad(this._data,this.blockSize);var t=this._process(!0)}else t=this._process(!0),e.unpad(t);return t},blockSize:4});var p=t.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),h=(u=(f.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext;return((e=e.salt)?r.create([1398893684,1701076831]).concat(e).concat(t):t).toString(i)},parse:function(e){var t=(e=i.parse(e)).words;if(1398893684==t[0]&&1701076831==t[1]){var n=r.create(t.slice(2,4));t.splice(0,4),e.sigBytes-=16}return p.create({ciphertext:e,salt:n})}},t.SerializableCipher=n.extend({cfg:n.extend({format:u}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var o=e.createEncryptor(n,r);return t=o.finalize(t),o=o.cfg,p.create({ciphertext:t,key:n,iv:o.iv,algorithm:e,mode:o.mode,padding:o.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}})),f=(f.kdf={}).OpenSSL={execute:function(e,t,n,o){return o||(o=r.random(8)),e=s.create({keySize:t+n}).compute(e,o),n=r.create(e.words.slice(t),4*n),e.sigBytes=4*t,p.create({key:e,iv:n,salt:o})}},d=t.PasswordBasedCipher=h.extend({cfg:h.cfg.extend({kdf:f}),encrypt:function(e,t,n,r){return n=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize),r.iv=n.iv,(e=h.encrypt.call(this,e,t,n.key,r)).mixIn(n),e},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),n=r.kdf.execute(n,e.keySize,e.ivSize,t.salt),r.iv=n.iv,h.decrypt.call(this,e,t,n.key,r)}})}(),function(){for(var e=P,t=e.lib.BlockCipher,n=e.algo,r=[],o=[],i=[],s=[],a=[],u=[],c=[],l=[],p=[],h=[],f=[],d=0;256>d;d++)f[d]=128>d?d<<1:d<<1^283;var y=0,g=0;for(d=0;256>d;d++){var m=(m=g^g<<1^g<<2^g<<3^g<<4)>>>8^255&m^99;r[y]=m,o[m]=y;var b=f[y],v=f[b],_=f[v],S=257*f[m]^16843008*m;i[y]=S<<24|S>>>8,s[y]=S<<16|S>>>16,a[y]=S<<8|S>>>24,u[y]=S,S=16843009*_^65537*v^257*b^16843008*y,c[m]=S<<24|S>>>8,l[m]=S<<16|S>>>16,p[m]=S<<8|S>>>24,h[m]=S,y?(y=b^f[f[f[_^b]]],g^=f[f[g]]):y=g=1}var w=[0,1,2,4,8,16,32,64,128,27,54];n=n.AES=t.extend({_doReset:function(){for(var e=(n=this._key).words,t=n.sigBytes/4,n=4*((this._nRounds=t+6)+1),o=this._keySchedule=[],i=0;i<n;i++)if(i<t)o[i]=e[i];else{var s=o[i-1];i%t?6<t&&4==i%t&&(s=r[s>>>24]<<24|r[s>>>16&255]<<16|r[s>>>8&255]<<8|r[255&s]):(s=r[(s=s<<8|s>>>24)>>>24]<<24|r[s>>>16&255]<<16|r[s>>>8&255]<<8|r[255&s],s^=w[i/t|0]<<24),o[i]=o[i-t]^s}for(e=this._invKeySchedule=[],t=0;t<n;t++)i=n-t,s=t%4?o[i]:o[i-4],e[t]=4>t||4>=i?s:c[r[s>>>24]]^l[r[s>>>16&255]]^p[r[s>>>8&255]]^h[r[255&s]]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,i,s,a,u,r)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,c,l,p,h,o),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,o,i,s,a){for(var u=this._nRounds,c=e[t]^n[0],l=e[t+1]^n[1],p=e[t+2]^n[2],h=e[t+3]^n[3],f=4,d=1;d<u;d++){var y=r[c>>>24]^o[l>>>16&255]^i[p>>>8&255]^s[255&h]^n[f++],g=r[l>>>24]^o[p>>>16&255]^i[h>>>8&255]^s[255&c]^n[f++],m=r[p>>>24]^o[h>>>16&255]^i[c>>>8&255]^s[255&l]^n[f++];h=r[h>>>24]^o[c>>>16&255]^i[l>>>8&255]^s[255&p]^n[f++],c=y,l=g,p=m}y=(a[c>>>24]<<24|a[l>>>16&255]<<16|a[p>>>8&255]<<8|a[255&h])^n[f++],g=(a[l>>>24]<<24|a[p>>>16&255]<<16|a[h>>>8&255]<<8|a[255&c])^n[f++],m=(a[p>>>24]<<24|a[h>>>16&255]<<16|a[c>>>8&255]<<8|a[255&l])^n[f++],h=(a[h>>>24]<<24|a[c>>>16&255]<<16|a[l>>>8&255]<<8|a[255&p])^n[f++],e[t]=y,e[t+1]=g,e[t+2]=m,e[t+3]=h},keySize:8});e.AES=t._createHelper(n)}(),P.mode.ECB=((O=P.lib.BlockCipherMode.extend()).Encryptor=O.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),O.Decryptor=O.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),O);var E=P;function A(e){var t,n=[];for(t=0;t<e.length;t+=1)n[t/4|0]|=e[t]<<24-8*t;return E.lib.WordArray.create(n,e.length)}var T=function(){function e(e){var t=e.config;this._config=t,this._iv="0123456789012345",this._allowedKeyEncodings=["hex","utf8","base64","binary"],this._allowedKeyLengths=[128,256],this._allowedModes=["ecb","cbc"],this._defaultOptions={encryptKey:!0,keyEncoding:"utf8",keyLength:256,mode:"cbc"}}return e.prototype.HMACSHA256=function(e){return E.HmacSHA256(e,this._config.secretKey).toString(E.enc.Base64)},e.prototype.SHA256=function(e){return E.SHA256(e).toString(E.enc.Hex)},e.prototype._parseOptions=function(e){var t=e||{};return t.hasOwnProperty("encryptKey")||(t.encryptKey=this._defaultOptions.encryptKey),t.hasOwnProperty("keyEncoding")||(t.keyEncoding=this._defaultOptions.keyEncoding),t.hasOwnProperty("keyLength")||(t.keyLength=this._defaultOptions.keyLength),t.hasOwnProperty("mode")||(t.mode=this._defaultOptions.mode),-1===this._allowedKeyEncodings.indexOf(t.keyEncoding.toLowerCase())&&(t.keyEncoding=this._defaultOptions.keyEncoding),-1===this._allowedKeyLengths.indexOf(parseInt(t.keyLength,10))&&(t.keyLength=this._defaultOptions.keyLength),-1===this._allowedModes.indexOf(t.mode.toLowerCase())&&(t.mode=this._defaultOptions.mode),t},e.prototype._decodeKey=function(e,t){return"base64"===t.keyEncoding?E.enc.Base64.parse(e):"hex"===t.keyEncoding?E.enc.Hex.parse(e):e},e.prototype._getPaddedKey=function(e,t){return e=this._decodeKey(e,t),t.encryptKey?E.enc.Utf8.parse(this.SHA256(e).slice(0,32)):e},e.prototype._getMode=function(e){return"ecb"===e.mode?E.mode.ECB:E.mode.CBC},e.prototype._getIV=function(e){return"cbc"===e.mode?E.enc.Utf8.parse(this._iv):null},e.prototype._getRandomIV=function(){return E.lib.WordArray.random(16)},e.prototype.encrypt=function(e,t,n){return this._config.customEncrypt?this._config.customEncrypt(e):this.pnEncrypt(e,t,n)},e.prototype.decrypt=function(e,t,n){return this._config.customDecrypt?this._config.customDecrypt(e):this.pnDecrypt(e,t,n)},e.prototype.pnEncrypt=function(e,t,n){if(!t&&!this._config.cipherKey)return e;n=this._parseOptions(n);var r=this._getMode(n),o=this._getPaddedKey(t||this._config.cipherKey,n);if(this._config.useRandomIVs){var i=this._getRandomIV(),s=E.AES.encrypt(e,o,{iv:i,mode:r}).ciphertext;return i.clone().concat(s.clone()).toString(E.enc.Base64)}var a=this._getIV(n);return E.AES.encrypt(e,o,{iv:a,mode:r}).ciphertext.toString(E.enc.Base64)||e},e.prototype.pnDecrypt=function(e,t,n){if(!t&&!this._config.cipherKey)return e;n=this._parseOptions(n);var r=this._getMode(n),o=this._getPaddedKey(t||this._config.cipherKey,n);if(this._config.useRandomIVs){var i=A((u=new Uint8ClampedArray(m(e))).slice(0,16)),s=A(u.slice(16));try{var a=E.AES.decrypt({ciphertext:s},o,{iv:i,mode:r}).toString(E.enc.Utf8);return JSON.parse(a)}catch(e){return null}}else{i=this._getIV(n);try{var u=E.enc.Base64.parse(e);a=E.AES.decrypt({ciphertext:u},o,{iv:i,mode:r}).toString(E.enc.Utf8);return JSON.parse(a)}catch(e){return null}}},e}(),N=function(){function e(e){var t=e.timeEndpoint;this._timeEndpoint=t}return e.prototype.onReconnection=function(e){this._reconnectionCallback=e},e.prototype.startPolling=function(){this._timeTimer=setInterval(this._performTimeLoop.bind(this),3e3)},e.prototype.stopPolling=function(){clearInterval(this._timeTimer)},e.prototype._performTimeLoop=function(){var e=this;this._timeEndpoint((function(t){t.error||(clearInterval(e._timeTimer),e._reconnectionCallback())}))},e}(),C=function(){function e(e){var t=e.config;this.hashHistory=[],this._config=t}return e.prototype.getKey=function(e){var t=function(e){var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n+=1)t=(t<<5)-t+e.charCodeAt(n),t&=t;return t}(JSON.stringify(e.payload)).toString(),n=e.publishMetaData.publishTimetoken;return"".concat(n,"-").concat(t)},e.prototype.isDuplicate=function(e){return this.hashHistory.includes(this.getKey(e))},e.prototype.addEntry=function(e){this.hashHistory.length>=this._config.maximumCacheSize&&this.hashHistory.shift(),this.hashHistory.push(this.getKey(e))},e.prototype.clearHistory=function(){this.hashHistory=[]},e}(),k={PNNetworkUpCategory:"PNNetworkUpCategory",PNNetworkDownCategory:"PNNetworkDownCategory",PNNetworkIssuesCategory:"PNNetworkIssuesCategory",PNTimeoutCategory:"PNTimeoutCategory",PNBadRequestCategory:"PNBadRequestCategory",PNAccessDeniedCategory:"PNAccessDeniedCategory",PNUnknownCategory:"PNUnknownCategory",PNReconnectedCategory:"PNReconnectedCategory",PNConnectedCategory:"PNConnectedCategory",PNRequestMessageCountExceededCategory:"PNRequestMessageCountExceededCategory",PNDisconnectedCategory:"PNDisconnectedCategory",PNConnectionErrorCategory:"PNConnectionErrorCategory",PNDisconnectedUnexpectedlyCategory:"PNDisconnectedUnexpectedlyCategory"},M=function(){function e(e){var t=e.subscribeEndpoint,n=e.leaveEndpoint,r=e.heartbeatEndpoint,o=e.setStateEndpoint,i=e.timeEndpoint,s=e.getFileUrl,a=e.config,u=e.crypto,c=e.listenerManager,l=e.cryptoModule,p=e.eventEmitter;this._listenerManager=c,this._config=a,this._leaveEndpoint=n,this._heartbeatEndpoint=r,this._setStateEndpoint=o,this._subscribeEndpoint=t,this._getFileUrl=s,this._crypto=u,this._cryptoModule=l,this._channels={},this._presenceChannels={},this._heartbeatChannels={},this._heartbeatChannelGroups={},this._channelGroups={},this._presenceChannelGroups={},this._pendingChannelSubscriptions=[],this._pendingChannelGroupSubscriptions=[],this._currentTimetoken=0,this._lastTimetoken=0,this._storedTimetoken=null,this._subscriptionStatusAnnounced=!1,this._isOnline=!0,this._reconnectionManager=new N({timeEndpoint:i}),this._dedupingManager=new C({config:a}),this._cryptoModule&&(this._decoder=new TextDecoder),this._eventEmitter=p}return e.prototype.adaptStateChange=function(e,t){var n=this,r=e.state,o=e.channels,i=void 0===o?[]:o,s=e.channelGroups,a=void 0===s?[]:s,u=e.withHeartbeat,c=void 0!==u&&u;if(i.forEach((function(e){e in n._channels&&(n._channels[e].state=r)})),a.forEach((function(e){e in n._channelGroups&&(n._channelGroups[e].state=r)})),c){var l={};return i.forEach((function(e){return l[e]=r})),a.forEach((function(e){return l[e]=r})),this._heartbeatEndpoint({channels:i,channelGroups:a,state:l},t)}return this._setStateEndpoint({state:r,channels:i,channelGroups:a},t)},e.prototype.adaptPresenceChange=function(e){var t=this,n=e.connected,r=e.channels,o=void 0===r?[]:r,i=e.channelGroups,s=void 0===i?[]:i;n?(o.forEach((function(e){t._heartbeatChannels[e]={state:{}}})),s.forEach((function(e){t._heartbeatChannelGroups[e]={state:{}}}))):(o.forEach((function(e){e in t._heartbeatChannels&&delete t._heartbeatChannels[e]})),s.forEach((function(e){e in t._heartbeatChannelGroups&&delete t._heartbeatChannelGroups[e]})),!1===this._config.suppressLeaveEvents&&this._leaveEndpoint({channels:o,channelGroups:s},(function(e){t._listenerManager.announceStatus(e)}))),this.reconnect()},e.prototype.adaptSubscribeChange=function(e){var t=this,n=e.timetoken,r=e.channels,o=void 0===r?[]:r,i=e.channelGroups,s=void 0===i?[]:i,a=e.withPresence,u=void 0!==a&&a,c=e.withHeartbeats,l=void 0!==c&&c;this._config.subscribeKey&&""!==this._config.subscribeKey?(n&&(this._lastTimetoken=this._currentTimetoken,this._currentTimetoken=n),"0"!==this._currentTimetoken&&0!==this._currentTimetoken&&(this._storedTimetoken=this._currentTimetoken,this._currentTimetoken=0),o.forEach((function(e){t._channels[e]={state:{}},u&&(t._presenceChannels[e]={}),(l||t._config.getHeartbeatInterval())&&(t._heartbeatChannels[e]={}),t._pendingChannelSubscriptions.push(e)})),s.forEach((function(e){t._channelGroups[e]={state:{}},u&&(t._presenceChannelGroups[e]={}),(l||t._config.getHeartbeatInterval())&&(t._heartbeatChannelGroups[e]={}),t._pendingChannelGroupSubscriptions.push(e)})),this._subscriptionStatusAnnounced=!1,this.reconnect()):console&&console.log&&console.log("subscribe key missing; aborting subscribe")},e.prototype.adaptUnsubscribeChange=function(e,t){var n=this,r=e.channels,o=void 0===r?[]:r,i=e.channelGroups,s=void 0===i?[]:i,a=[],u=[];o.forEach((function(e){e in n._channels&&(delete n._channels[e],a.push(e),e in n._heartbeatChannels&&delete n._heartbeatChannels[e]),e in n._presenceChannels&&(delete n._presenceChannels[e],a.push(e))})),s.forEach((function(e){e in n._channelGroups&&(delete n._channelGroups[e],u.push(e),e in n._heartbeatChannelGroups&&delete n._heartbeatChannelGroups[e]),e in n._presenceChannelGroups&&(delete n._presenceChannelGroups[e],u.push(e))})),0===a.length&&0===u.length||(!1!==this._config.suppressLeaveEvents||t||this._leaveEndpoint({channels:a,channelGroups:u},(function(e){e.affectedChannels=a,e.affectedChannelGroups=u,e.currentTimetoken=n._currentTimetoken,e.lastTimetoken=n._lastTimetoken,n._listenerManager.announceStatus(e)})),0===Object.keys(this._channels).length&&0===Object.keys(this._presenceChannels).length&&0===Object.keys(this._channelGroups).length&&0===Object.keys(this._presenceChannelGroups).length&&(this._lastTimetoken=0,this._currentTimetoken=0,this._storedTimetoken=null,this._region=null,this._reconnectionManager.stopPolling()),this.reconnect())},e.prototype.unsubscribeAll=function(e){this.adaptUnsubscribeChange({channels:this.getSubscribedChannels(),channelGroups:this.getSubscribedChannelGroups()},e)},e.prototype.getHeartbeatChannels=function(){return Object.keys(this._heartbeatChannels)},e.prototype.getHeartbeatChannelGroups=function(){return Object.keys(this._heartbeatChannelGroups)},e.prototype.getSubscribedChannels=function(){return Object.keys(this._channels)},e.prototype.getSubscribedChannelGroups=function(){return Object.keys(this._channelGroups)},e.prototype.reconnect=function(){this._startSubscribeLoop(),this._registerHeartbeatTimer()},e.prototype.disconnect=function(){this._stopSubscribeLoop(),this._stopHeartbeatTimer(),this._reconnectionManager.stopPolling()},e.prototype._registerHeartbeatTimer=function(){this._stopHeartbeatTimer(),0!==this._config.getHeartbeatInterval()&&void 0!==this._config.getHeartbeatInterval()&&(this._performHeartbeatLoop(),this._heartbeatTimer=setInterval(this._performHeartbeatLoop.bind(this),1e3*this._config.getHeartbeatInterval()))},e.prototype._stopHeartbeatTimer=function(){this._heartbeatTimer&&(clearInterval(this._heartbeatTimer),this._heartbeatTimer=null)},e.prototype._performHeartbeatLoop=function(){var e=this,t=this.getHeartbeatChannels(),n=this.getHeartbeatChannelGroups(),r={};if(0!==t.length||0!==n.length){this.getSubscribedChannels().forEach((function(t){var n=e._channels[t].state;Object.keys(n).length&&(r[t]=n)})),this.getSubscribedChannelGroups().forEach((function(t){var n=e._channelGroups[t].state;Object.keys(n).length&&(r[t]=n)}));this._heartbeatEndpoint({channels:t,channelGroups:n,state:r},function(t){t.error&&e._config.announceFailedHeartbeats&&e._listenerManager.announceStatus(t),t.error&&e._config.autoNetworkDetection&&e._isOnline&&(e._isOnline=!1,e.disconnect(),e._listenerManager.announceNetworkDown(),e.reconnect()),!t.error&&e._config.announceSuccessfulHeartbeats&&e._listenerManager.announceStatus(t)}.bind(this))}},e.prototype._startSubscribeLoop=function(){var e=this;this._stopSubscribeLoop();var t={},n=[],r=[];if(Object.keys(this._channels).forEach((function(r){var o=e._channels[r].state;Object.keys(o).length&&(t[r]=o),n.push(r)})),Object.keys(this._presenceChannels).forEach((function(e){n.push("".concat(e,"-pnpres"))})),Object.keys(this._channelGroups).forEach((function(n){var o=e._channelGroups[n].state;Object.keys(o).length&&(t[n]=o),r.push(n)})),Object.keys(this._presenceChannelGroups).forEach((function(e){r.push("".concat(e,"-pnpres"))})),0!==n.length||0!==r.length){var o={channels:n,channelGroups:r,state:t,timetoken:this._currentTimetoken,filterExpression:this._config.filterExpression,region:this._region};this._subscribeCall=this._subscribeEndpoint(o,this._processSubscribeResponse.bind(this))}},e.prototype._processSubscribeResponse=function(e,t){var n=this;if(e.error){if(e.errorData&&"Aborted"===e.errorData.message)return;e.category===k.PNTimeoutCategory?this._startSubscribeLoop():e.category===k.PNNetworkIssuesCategory?(this.disconnect(),e.error&&this._config.autoNetworkDetection&&this._isOnline&&(this._isOnline=!1,this._listenerManager.announceNetworkDown()),this._reconnectionManager.onReconnection((function(){n._config.autoNetworkDetection&&!n._isOnline&&(n._isOnline=!0,n._listenerManager.announceNetworkUp()),n.reconnect(),n._subscriptionStatusAnnounced=!0;var t={category:k.PNReconnectedCategory,operation:e.operation,lastTimetoken:n._lastTimetoken,currentTimetoken:n._currentTimetoken};n._listenerManager.announceStatus(t)})),this._reconnectionManager.startPolling(),this._listenerManager.announceStatus(e)):e.category===k.PNBadRequestCategory?(this._stopHeartbeatTimer(),this._listenerManager.announceStatus(e)):this._listenerManager.announceStatus(e)}else{if(this._storedTimetoken?(this._currentTimetoken=this._storedTimetoken,this._storedTimetoken=null):(this._lastTimetoken=this._currentTimetoken,this._currentTimetoken=t.metadata.timetoken),!this._subscriptionStatusAnnounced){var r={};r.category=k.PNConnectedCategory,r.operation=e.operation,r.affectedChannels=this._pendingChannelSubscriptions,r.subscribedChannels=this.getSubscribedChannels(),r.affectedChannelGroups=this._pendingChannelGroupSubscriptions,r.lastTimetoken=this._lastTimetoken,r.currentTimetoken=this._currentTimetoken,this._subscriptionStatusAnnounced=!0,this._listenerManager.announceStatus(r),this._pendingChannelSubscriptions=[],this._pendingChannelGroupSubscriptions=[]}var o=t.messages||[],i=this._config,s=i.requestMessageCountThreshold,a=i.dedupeOnSubscribe;if(s&&o.length>=s){var u={};u.category=k.PNRequestMessageCountExceededCategory,u.operation=e.operation,this._listenerManager.announceStatus(u)}o.forEach((function(e){if(e.channel,e.subscriptionMatch,a){if(n._dedupingManager.isDuplicate(e))return;n._dedupingManager.addEntry(e)}n._eventEmitter.emitEvent(e)})),this._region=t.metadata.region,this._startSubscribeLoop()}},e.prototype._stopSubscribeLoop=function(){this._subscribeCall&&("function"==typeof this._subscribeCall.abort&&this._subscribeCall.abort(),this._subscribeCall=null)},e.prototype._renameEvent=function(e){return"set"===e?"updated":"removed"},e.prototype._renameChannelField=function(e){var t=e.channel,n=r(e,["channel"]);return n.spaceId=t,n},e}(),j={PNTimeOperation:"PNTimeOperation",PNHistoryOperation:"PNHistoryOperation",PNDeleteMessagesOperation:"PNDeleteMessagesOperation",PNFetchMessagesOperation:"PNFetchMessagesOperation",PNMessageCounts:"PNMessageCountsOperation",PNSubscribeOperation:"PNSubscribeOperation",PNUnsubscribeOperation:"PNUnsubscribeOperation",PNPublishOperation:"PNPublishOperation",PNSignalOperation:"PNSignalOperation",PNAddMessageActionOperation:"PNAddActionOperation",PNRemoveMessageActionOperation:"PNRemoveMessageActionOperation",PNGetMessageActionsOperation:"PNGetMessageActionsOperation",PNCreateUserOperation:"PNCreateUserOperation",PNUpdateUserOperation:"PNUpdateUserOperation",PNDeleteUserOperation:"PNDeleteUserOperation",PNGetUserOperation:"PNGetUsersOperation",PNGetUsersOperation:"PNGetUsersOperation",PNCreateSpaceOperation:"PNCreateSpaceOperation",PNUpdateSpaceOperation:"PNUpdateSpaceOperation",PNDeleteSpaceOperation:"PNDeleteSpaceOperation",PNGetSpaceOperation:"PNGetSpacesOperation",PNGetSpacesOperation:"PNGetSpacesOperation",PNGetMembersOperation:"PNGetMembersOperation",PNUpdateMembersOperation:"PNUpdateMembersOperation",PNGetMembershipsOperation:"PNGetMembershipsOperation",PNUpdateMembershipsOperation:"PNUpdateMembershipsOperation",PNListFilesOperation:"PNListFilesOperation",PNGenerateUploadUrlOperation:"PNGenerateUploadUrlOperation",PNPublishFileOperation:"PNPublishFileOperation",PNGetFileUrlOperation:"PNGetFileUrlOperation",PNDownloadFileOperation:"PNDownloadFileOperation",PNGetAllUUIDMetadataOperation:"PNGetAllUUIDMetadataOperation",PNGetUUIDMetadataOperation:"PNGetUUIDMetadataOperation",PNSetUUIDMetadataOperation:"PNSetUUIDMetadataOperation",PNRemoveUUIDMetadataOperation:"PNRemoveUUIDMetadataOperation",PNGetAllChannelMetadataOperation:"PNGetAllChannelMetadataOperation",PNGetChannelMetadataOperation:"PNGetChannelMetadataOperation",PNSetChannelMetadataOperation:"PNSetChannelMetadataOperation",PNRemoveChannelMetadataOperation:"PNRemoveChannelMetadataOperation",PNSetMembersOperation:"PNSetMembersOperation",PNSetMembershipsOperation:"PNSetMembershipsOperation",PNPushNotificationEnabledChannelsOperation:"PNPushNotificationEnabledChannelsOperation",PNRemoveAllPushNotificationsOperation:"PNRemoveAllPushNotificationsOperation",PNWhereNowOperation:"PNWhereNowOperation",PNSetStateOperation:"PNSetStateOperation",PNHereNowOperation:"PNHereNowOperation",PNGetStateOperation:"PNGetStateOperation",PNHeartbeatOperation:"PNHeartbeatOperation",PNChannelGroupsOperation:"PNChannelGroupsOperation",PNRemoveGroupOperation:"PNRemoveGroupOperation",PNChannelsForGroupOperation:"PNChannelsForGroupOperation",PNAddChannelsToGroupOperation:"PNAddChannelsToGroupOperation",PNRemoveChannelsFromGroupOperation:"PNRemoveChannelsFromGroupOperation",PNAccessManagerGrant:"PNAccessManagerGrant",PNAccessManagerGrantToken:"PNAccessManagerGrantToken",PNAccessManagerAudit:"PNAccessManagerAudit",PNAccessManagerRevokeToken:"PNAccessManagerRevokeToken",PNHandshakeOperation:"PNHandshakeOperation",PNReceiveMessagesOperation:"PNReceiveMessagesOperation"},R=function(){function e(e){this._maximumSamplesCount=100,this._trackedLatencies={},this._latencies={},this._telemetryExcludeOperations=[j.PNSubscribeOperation,j.PNReceiveMessagesOperation,j.PNHandshakeOperation],this._maximumSamplesCount=e.maximumSamplesCount||this._maximumSamplesCount}return e.prototype.operationsLatencyForRequest=function(){var e=this,t={};return Object.keys(this._latencies).forEach((function(n){var r=e._latencies[n],o=e._averageLatency(r);o>0&&(t["l_".concat(n)]=o)})),t},e.prototype.startLatencyMeasure=function(e,t){!this._telemetryExcludeOperations.includes(e)&&t&&(this._trackedLatencies[t]=Date.now())},e.prototype.stopLatencyMeasure=function(e,t){if(!this._telemetryExcludeOperations.includes(e)&&t){var n=this._endpointName(e),r=this._latencies[n],o=this._trackedLatencies[t];r||(this._latencies[n]=[],r=this._latencies[n]),r.push(Date.now()-o),r.length>this._maximumSamplesCount&&r.splice(0,r.length-this._maximumSamplesCount),delete this._trackedLatencies[t]}},e.prototype._averageLatency=function(e){return Math.floor(e.reduce((function(e,t){return e+t}),0)/e.length)},e.prototype._endpointName=function(e){var t=null;switch(e){case j.PNPublishOperation:t="pub";break;case j.PNSignalOperation:t="sig";break;case j.PNHistoryOperation:case j.PNFetchMessagesOperation:case j.PNDeleteMessagesOperation:case j.PNMessageCounts:t="hist";break;case j.PNUnsubscribeOperation:case j.PNWhereNowOperation:case j.PNHereNowOperation:case j.PNHeartbeatOperation:case j.PNSetStateOperation:case j.PNGetStateOperation:t="pres";break;case j.PNAddChannelsToGroupOperation:case j.PNRemoveChannelsFromGroupOperation:case j.PNChannelGroupsOperation:case j.PNRemoveGroupOperation:case j.PNChannelsForGroupOperation:t="cg";break;case j.PNPushNotificationEnabledChannelsOperation:case j.PNRemoveAllPushNotificationsOperation:t="push";break;case j.PNCreateUserOperation:case j.PNUpdateUserOperation:case j.PNDeleteUserOperation:case j.PNGetUserOperation:case j.PNGetUsersOperation:case j.PNCreateSpaceOperation:case j.PNUpdateSpaceOperation:case j.PNDeleteSpaceOperation:case j.PNGetSpaceOperation:case j.PNGetSpacesOperation:case j.PNGetMembersOperation:case j.PNUpdateMembersOperation:case j.PNGetMembershipsOperation:case j.PNUpdateMembershipsOperation:t="obj";break;case j.PNAddMessageActionOperation:case j.PNRemoveMessageActionOperation:case j.PNGetMessageActionsOperation:t="msga";break;case j.PNAccessManagerGrant:case j.PNAccessManagerAudit:t="pam";break;case j.PNAccessManagerGrantToken:case j.PNAccessManagerRevokeToken:t="pamv3";break;default:t="time"}return t},e}(),x=function(){function e(e,t,n){this._payload=e,this._setDefaultPayloadStructure(),this.title=t,this.body=n}return Object.defineProperty(e.prototype,"payload",{get:function(){return this._payload},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{set:function(e){this._title=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subtitle",{set:function(e){this._subtitle=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"body",{set:function(e){this._body=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"badge",{set:function(e){this._badge=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sound",{set:function(e){this._sound=e},enumerable:!1,configurable:!0}),e.prototype._setDefaultPayloadStructure=function(){},e.prototype.toObject=function(){return{}},e}(),U=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),Object.defineProperty(r.prototype,"configurations",{set:function(e){e&&e.length&&(this._configurations=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"notification",{get:function(){return this._payload.aps},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"title",{get:function(){return this._title},set:function(e){e&&e.length&&(this._payload.aps.alert.title=e,this._title=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"subtitle",{get:function(){return this._subtitle},set:function(e){e&&e.length&&(this._payload.aps.alert.subtitle=e,this._subtitle=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"body",{get:function(){return this._body},set:function(e){e&&e.length&&(this._payload.aps.alert.body=e,this._body=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"badge",{get:function(){return this._badge},set:function(e){null!=e&&(this._payload.aps.badge=e,this._badge=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"sound",{get:function(){return this._sound},set:function(e){e&&e.length&&(this._payload.aps.sound=e,this._sound=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"silent",{set:function(e){this._isSilent=e},enumerable:!1,configurable:!0}),r.prototype._setDefaultPayloadStructure=function(){this._payload.aps={alert:{}}},r.prototype.toObject=function(){var e=this,t=n({},this._payload),r=t.aps,o=r.alert;if(this._isSilent&&(r["content-available"]=1),"apns2"===this._apnsPushType){if(!this._configurations||!this._configurations.length)throw new ReferenceError("APNS2 configuration is missing");var i=[];this._configurations.forEach((function(t){i.push(e._objectFromAPNS2Configuration(t))})),i.length&&(t.pn_push=i)}return o&&Object.keys(o).length||delete r.alert,this._isSilent&&(delete r.alert,delete r.badge,delete r.sound,o={}),this._isSilent||Object.keys(o).length?t:null},r.prototype._objectFromAPNS2Configuration=function(e){var t=this;if(!e.targets||!e.targets.length)throw new ReferenceError("At least one APNS2 target should be provided");var n=[];e.targets.forEach((function(e){n.push(t._objectFromAPNSTarget(e))}));var r=e.collapseId,o=e.expirationDate,i={auth_method:"token",targets:n,version:"v2"};return r&&r.length&&(i.collapse_id=r),o&&(i.expiration=o.toISOString()),i},r.prototype._objectFromAPNSTarget=function(e){if(!e.topic||!e.topic.length)throw new TypeError("Target 'topic' undefined.");var t=e.topic,n=e.environment,r=void 0===n?"development":n,o=e.excludedDevices,i=void 0===o?[]:o,s={topic:t,environment:r};return i.length&&(s.excluded_devices=i),s},r}(x),I=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),Object.defineProperty(r.prototype,"backContent",{get:function(){return this._backContent},set:function(e){e&&e.length&&(this._payload.back_content=e,this._backContent=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"backTitle",{get:function(){return this._backTitle},set:function(e){e&&e.length&&(this._payload.back_title=e,this._backTitle=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"count",{get:function(){return this._count},set:function(e){null!=e&&(this._payload.count=e,this._count=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"title",{get:function(){return this._title},set:function(e){e&&e.length&&(this._payload.title=e,this._title=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"type",{get:function(){return this._type},set:function(e){e&&e.length&&(this._payload.type=e,this._type=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"subtitle",{get:function(){return this.backTitle},set:function(e){this.backTitle=e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"body",{get:function(){return this.backContent},set:function(e){this.backContent=e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"badge",{get:function(){return this.count},set:function(e){this.count=e},enumerable:!1,configurable:!0}),r.prototype.toObject=function(){return Object.keys(this._payload).length?n({},this._payload):null},r}(x),D=function(e){function o(){return null!==e&&e.apply(this,arguments)||this}return t(o,e),Object.defineProperty(o.prototype,"notification",{get:function(){return this._payload.notification},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"data",{get:function(){return this._payload.data},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"title",{get:function(){return this._title},set:function(e){e&&e.length&&(this._payload.notification.title=e,this._title=e)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"body",{get:function(){return this._body},set:function(e){e&&e.length&&(this._payload.notification.body=e,this._body=e)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"sound",{get:function(){return this._sound},set:function(e){e&&e.length&&(this._payload.notification.sound=e,this._sound=e)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"icon",{get:function(){return this._icon},set:function(e){e&&e.length&&(this._payload.notification.icon=e,this._icon=e)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"tag",{get:function(){return this._tag},set:function(e){e&&e.length&&(this._payload.notification.tag=e,this._tag=e)},enumerable:!1,configurable:!0}),Object.defineProperty(o.prototype,"silent",{set:function(e){this._isSilent=e},enumerable:!1,configurable:!0}),o.prototype._setDefaultPayloadStructure=function(){this._payload.notification={},this._payload.data={}},o.prototype.toObject=function(){var e=n({},this._payload.data),t=null,o={};if(Object.keys(this._payload).length>2){var i=this._payload;i.notification,i.data;var s=r(i,["notification","data"]);e=n(n({},e),s)}return this._isSilent?e.notification=this._payload.notification:t=this._payload.notification,Object.keys(e).length&&(o.data=e),t&&Object.keys(t).length&&(o.notification=t),Object.keys(o).length?o:null},o}(x),F=function(){function e(e,t){this._payload={apns:{},mpns:{},fcm:{}},this._title=e,this._body=t,this.apns=new U(this._payload.apns,e,t),this.mpns=new I(this._payload.mpns,e,t),this.fcm=new D(this._payload.fcm,e,t)}return Object.defineProperty(e.prototype,"debugging",{set:function(e){this._debugging=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._title},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"body",{get:function(){return this._body},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subtitle",{get:function(){return this._subtitle},set:function(e){this._subtitle=e,this.apns.subtitle=e,this.mpns.subtitle=e,this.fcm.subtitle=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"badge",{get:function(){return this._badge},set:function(e){this._badge=e,this.apns.badge=e,this.mpns.badge=e,this.fcm.badge=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sound",{get:function(){return this._sound},set:function(e){this._sound=e,this.apns.sound=e,this.mpns.sound=e,this.fcm.sound=e},enumerable:!1,configurable:!0}),e.prototype.buildPayload=function(e){var t={};if(e.includes("apns")||e.includes("apns2")){this.apns._apnsPushType=e.includes("apns")?"apns":"apns2";var n=this.apns.toObject();n&&Object.keys(n).length&&(t.pn_apns=n)}if(e.includes("mpns")){var r=this.mpns.toObject();r&&Object.keys(r).length&&(t.pn_mpns=r)}if(e.includes("fcm")){var o=this.fcm.toObject();o&&Object.keys(o).length&&(t.pn_gcm=o)}return Object.keys(t).length&&this._debugging&&(t.pn_debug=!0),t},e}(),L=function(){function e(){this._listeners=[]}return e.prototype.addListener=function(e){this._listeners.includes(e)||this._listeners.push(e)},e.prototype.removeListener=function(e){var t=[];this._listeners.forEach((function(n){n!==e&&t.push(n)})),this._listeners=t},e.prototype.removeAllListeners=function(){this._listeners=[]},e.prototype.announcePresence=function(e){this._listeners.forEach((function(t){t.presence&&t.presence(e)}))},e.prototype.announceStatus=function(e){this._listeners.forEach((function(t){t.status&&t.status(e)}))},e.prototype.announceMessage=function(e){this._listeners.forEach((function(t){t.message&&t.message(e)}))},e.prototype.announceSignal=function(e){this._listeners.forEach((function(t){t.signal&&t.signal(e)}))},e.prototype.announceMessageAction=function(e){this._listeners.forEach((function(t){t.messageAction&&t.messageAction(e)}))},e.prototype.announceFile=function(e){this._listeners.forEach((function(t){t.file&&t.file(e)}))},e.prototype.announceObjects=function(e){this._listeners.forEach((function(t){t.objects&&t.objects(e)}))},e.prototype.announceUser=function(e){this._listeners.forEach((function(t){t.user&&t.user(e)}))},e.prototype.announceSpace=function(e){this._listeners.forEach((function(t){t.space&&t.space(e)}))},e.prototype.announceMembership=function(e){this._listeners.forEach((function(t){t.membership&&t.membership(e)}))},e.prototype.announceNetworkUp=function(){var e={};e.category=k.PNNetworkUpCategory,this.announceStatus(e)},e.prototype.announceNetworkDown=function(){var e={};e.category=k.PNNetworkDownCategory,this.announceStatus(e)},e}(),G=function(){function e(e,t){this._config=e,this._cbor=t}return e.prototype.setToken=function(e){e&&e.length>0?this._token=e:this._token=void 0},e.prototype.getToken=function(){return this._token},e.prototype.extractPermissions=function(e){var t={read:!1,write:!1,manage:!1,delete:!1,get:!1,update:!1,join:!1};return 128==(128&e)&&(t.join=!0),64==(64&e)&&(t.update=!0),32==(32&e)&&(t.get=!0),8==(8&e)&&(t.delete=!0),4==(4&e)&&(t.manage=!0),2==(2&e)&&(t.write=!0),1==(1&e)&&(t.read=!0),t},e.prototype.parseToken=function(e){var t=this,n=this._cbor.decodeToken(e);if(void 0!==n){var r=n.res.uuid?Object.keys(n.res.uuid):[],o=Object.keys(n.res.chan),i=Object.keys(n.res.grp),s=n.pat.uuid?Object.keys(n.pat.uuid):[],a=Object.keys(n.pat.chan),u=Object.keys(n.pat.grp),c={version:n.v,timestamp:n.t,ttl:n.ttl,authorized_uuid:n.uuid},l=r.length>0,p=o.length>0,h=i.length>0;(l||p||h)&&(c.resources={},l&&(c.resources.uuids={},r.forEach((function(e){c.resources.uuids[e]=t.extractPermissions(n.res.uuid[e])}))),p&&(c.resources.channels={},o.forEach((function(e){c.resources.channels[e]=t.extractPermissions(n.res.chan[e])}))),h&&(c.resources.groups={},i.forEach((function(e){c.resources.groups[e]=t.extractPermissions(n.res.grp[e])}))));var f=s.length>0,d=a.length>0,y=u.length>0;return(f||d||y)&&(c.patterns={},f&&(c.patterns.uuids={},s.forEach((function(e){c.patterns.uuids[e]=t.extractPermissions(n.pat.uuid[e])}))),d&&(c.patterns.channels={},a.forEach((function(e){c.patterns.channels[e]=t.extractPermissions(n.pat.chan[e])}))),y&&(c.patterns.groups={},u.forEach((function(e){c.patterns.groups[e]=t.extractPermissions(n.pat.grp[e])})))),Object.keys(n.meta).length>0&&(c.meta=n.meta),c.signature=n.sig,c}},e}();function K(e){return encodeURIComponent(e).replace(/[!~*'()]/g,(function(e){return"%".concat(e.charCodeAt(0).toString(16).toUpperCase())}))}function B(e){return function(e){var t=[];return Object.keys(e).forEach((function(e){return t.push(e)})),t}(e).sort()}var H={signPamFromParams:function(e){return B(e).map((function(t){return"".concat(t,"=").concat(K(e[t]))})).join("&")},endsWith:function(e,t){return-1!==e.indexOf(t,this.length-t.length)},createPromise:function(){var e,t;return{promise:new Promise((function(n,r){e=n,t=r})),reject:t,fulfill:e}},encodeString:K,stringToArrayBuffer:function(e){for(var t=new ArrayBuffer(2*e.length),n=new Uint16Array(t),r=0,o=e.length;r<o;r++)n[r]=e.charCodeAt(r);return t},removeSingleOccurance:function(e,t){var n=Object.fromEntries(t.map((function(e){return[e,!1]})));return e.filter((function(e){return!(t.includes(e)&&!n[e])||(n[e]=!0,!1)}))},findUniqueCommonElements:function(e,t){return __spreadArray([],__read(e),!1).filter((function(n){return t.includes(n)&&e.indexOf(n)===e.lastIndexOf(n)&&t.indexOf(n)===t.lastIndexOf(n)}))}},q=function(e){function n(t,n){var r=this.constructor,o=e.call(this,t)||this;return o.name=o.constructor.name,o.status=n,o.message=t,Object.setPrototypeOf(o,r.prototype),o}return t(n,e),n}(Error);function z(e){return(t={message:e}).type="validationError",t.error=!0,t;var t}function V(e,t,n){return e.usePost&&e.usePost(t,n)?e.postURL(t,n):e.usePatch&&e.usePatch(t,n)?e.patchURL(t,n):e.useGetFile&&e.useGetFile(t,n)?e.getFileURL(t,n):e.getURL(t,n)}function W(e){if(e.sdkName)return e.sdkName;var t="PubNub-JS-".concat(e.sdkFamily);e.partnerId&&(t+="-".concat(e.partnerId)),t+="/".concat(e.getVersion());var n=e._getPnsdkSuffix(" ");return n.length>0&&(t+=n),t}function J(e,t,n){return t.usePost&&t.usePost(e,n)?"POST":t.usePatch&&t.usePatch(e,n)?"PATCH":t.useDelete&&t.useDelete(e,n)?"DELETE":t.useGetFile&&t.useGetFile(e,n)?"GETFILE":"GET"}function $(e,t,n,r,o){var i=e.config,s=e.crypto,a=J(e,o,r);n.timestamp=Math.floor((new Date).getTime()/1e3),"PNPublishOperation"===o.getOperation()&&o.usePost&&o.usePost(e,r)&&(a="GET"),"GETFILE"===a&&(a="GET");var u="".concat(a,"\n").concat(i.publishKey,"\n").concat(t,"\n").concat(H.signPamFromParams(n),"\n");if("POST"===a)u+="string"==typeof(c=o.postPayload(e,r))?c:JSON.stringify(c);else if("PATCH"===a){var c;u+="string"==typeof(c=o.patchPayload(e,r))?c:JSON.stringify(c)}var l="v2.".concat(s.HMACSHA256(u));l=(l=(l=l.replace(/\+/g,"-")).replace(/\//g,"_")).replace(/=+$/,""),n.signature=l}function Q(e,t){for(var r=[],o=2;o<arguments.length;o++)r[o-2]=arguments[o];var i=e.networking,s=e.config,a=e.telemetryManager,u=e.tokenManager,c=y(),l=null,p=null,h={};t.getOperation()===j.PNTimeOperation||t.getOperation()===j.PNChannelGroupsOperation?l=r[0]:(h=r[0],l=r[1]),"undefined"==typeof Promise||l||(p=H.createPromise());var f=t.validateParams(e,h);if(f)return l?l(z(f)):p?(p.reject(new q("Validation failed, check status for details",z(f))),p.promise):void 0;var d,g=t.prepareParams(e,h),m=V(t,e,h),b={url:m,operation:t.getOperation(),timeout:t.getRequestTimeout(e),headers:t.getRequestHeaders?t.getRequestHeaders():{},ignoreBody:"function"==typeof t.ignoreBody&&t.ignoreBody(e),forceBuffered:"function"==typeof t.forceBuffered?t.forceBuffered(e,h):null,abortSignal:"function"==typeof t.getAbortSignal?t.getAbortSignal(e,h):null};g.uuid=s.UUID,g.pnsdk=W(s);var v=a.operationsLatencyForRequest();if(Object.keys(v).length&&(g=n(n({},g),v)),s.useInstanceId&&(g.instanceid=s.instanceId),s.useRequestId&&(g.requestid=c),t.isAuthSupported()){var _=u.getToken()||s.getAuthKey();_&&(g.auth=_)}s.secretKey&&$(e,m,g,h,t);var S=function(n,r){if(n.error)return t.handleError&&t.handleError(e,h,n),void(l?l(n):p&&p.reject(new q("PubNub call failed, check status for details",n)));a.stopLatencyMeasure(t.getOperation(),c);var o=t.handleResponse(e,r,h);"function"!=typeof(null==o?void 0:o.then)&&(o=Promise.resolve(o)),o.then((function(e){l?l(n,e):p&&p.fulfill(e)})).catch((function(e){if(l){var n=e;t.getOperation()===j.PNSubscribeOperation&&(n={statusCode:400,error:!0,operation:t.getOperation(),errorData:e,category:k.PNUnknownCategory}),l(n,null)}else p&&p.reject(new q("PubNub call failed, check status for details",e))}))};if(a.startLatencyMeasure(t.getOperation(),c),"POST"===J(e,t,h)){var w=t.postPayload(e,h);d=i.POST(g,w,b,S)}else if("PATCH"===J(e,t,h)){w=t.patchPayload(e,h);d=i.PATCH(g,w,b,S)}else d="DELETE"===J(e,t,h)?i.DELETE(g,b,S):"GETFILE"===J(e,t,h)?i.GETFILE(g,b,S):i.GET(g,b,S);return t.getOperation()===j.PNSubscribeOperation?d:p?p.promise:void 0}var X=Object.freeze({__proto__:null,getOperation:function(){return j.PNAddChannelsToGroupOperation},validateParams:function(e,t){var n=t.channels,r=t.channelGroup,o=e.config;return r?n&&0!==n.length?o.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(H.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channels;return{add:(void 0===n?[]:n).join(",")}},handleResponse:function(){return{}}});var Y=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveChannelsFromGroupOperation},validateParams:function(e,t){var n=t.channels,r=t.channelGroup,o=e.config;return r?n&&0!==n.length?o.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(H.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channels;return{remove:(void 0===n?[]:n).join(",")}},handleResponse:function(){return{}}});var Z=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveGroupOperation},validateParams:function(e,t){var n=t.channelGroup,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(H.encodeString(n),"/remove")},isAuthSupported:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},prepareParams:function(){return{}},handleResponse:function(){return{}}});var ee=Object.freeze({__proto__:null,getOperation:function(){return j.PNChannelGroupsOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e){var t=e.config;return"/v1/channel-registration/sub-key/".concat(t.subscribeKey,"/channel-group")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{groups:t.payload.groups}}});var te=Object.freeze({__proto__:null,getOperation:function(){return j.PNChannelsForGroupOperation},validateParams:function(e,t){var n=t.channelGroup,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(H.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{channels:t.payload.channels}}});var ne=Object.freeze({__proto__:null,getOperation:function(){return j.PNPushNotificationEnabledChannelsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,o=t.channels,i=t.topic,s=e.config;return n?r?"apns2"!==r||i?o&&0!==o.length?s.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,o=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(o.subscribeKey,"/devices-apns2/").concat(n):"/v1/push/sub-key/".concat(o.subscribeKey,"/devices/").concat(n)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,o=t.channels,i=void 0===o?[]:o,s=t.environment,a=void 0===s?"development":s,u=t.topic,c={type:r,add:i.join(",")};return"apns2"===r&&delete(c=n(n({},c),{environment:a,topic:u})).type,c},handleResponse:function(){return{}}});var re=Object.freeze({__proto__:null,getOperation:function(){return j.PNPushNotificationEnabledChannelsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,o=t.channels,i=t.topic,s=e.config;return n?r?"apns2"!==r||i?o&&0!==o.length?s.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,o=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(o.subscribeKey,"/devices-apns2/").concat(n):"/v1/push/sub-key/".concat(o.subscribeKey,"/devices/").concat(n)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,o=t.channels,i=void 0===o?[]:o,s=t.environment,a=void 0===s?"development":s,u=t.topic,c={type:r,remove:i.join(",")};return"apns2"===r&&delete(c=n(n({},c),{environment:a,topic:u})).type,c},handleResponse:function(){return{}}});var oe=Object.freeze({__proto__:null,getOperation:function(){return j.PNPushNotificationEnabledChannelsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,o=t.topic,i=e.config;return n?r?"apns2"!==r||o?i.subscribeKey?void 0:"Missing Subscribe Key":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,o=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(o.subscribeKey,"/devices-apns2/").concat(n):"/v1/push/sub-key/".concat(o.subscribeKey,"/devices/").concat(n)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,o=t.environment,i=void 0===o?"development":o,s=t.topic,a=t.start,u=t.count,c={type:r};return"apns2"===r&&delete(c=n(n({},c),{environment:i,topic:s})).type,a&&(c.start=a),u&&u>0&&(c.count=u),c},handleResponse:function(e,t){return{channels:t}}});var ie=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveAllPushNotificationsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,o=t.topic,i=e.config;return n?r?"apns2"!==r||o?i.subscribeKey?void 0:"Missing Subscribe Key":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,o=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(o.subscribeKey,"/devices-apns2/").concat(n,"/remove"):"/v1/push/sub-key/".concat(o.subscribeKey,"/devices/").concat(n,"/remove")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,o=t.environment,i=void 0===o?"development":o,s=t.topic,a={type:r};return"apns2"===r&&delete(a=n(n({},a),{environment:i,topic:s})).type,a},handleResponse:function(){return{}}});var se=Object.freeze({__proto__:null,getOperation:function(){return j.PNUnsubscribeOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=o.length>0?o.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(i),"/leave")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channelGroups,r=void 0===n?[]:n,o={};return r.length>0&&(o["channel-group"]=r.join(",")),o},handleResponse:function(){return{}}});var ae=Object.freeze({__proto__:null,getOperation:function(){return j.PNWhereNowOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.uuid,o=void 0===r?n.UUID:r;return"/v2/presence/sub-key/".concat(n.subscribeKey,"/uuid/").concat(H.encodeString(o))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return t.payload?{channels:t.payload.channels}:{channels:[]}}});var ue=Object.freeze({__proto__:null,getOperation:function(){return j.PNHeartbeatOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=o.length>0?o.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(i),"/heartbeat")},isAuthSupported:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},prepareParams:function(e,t){var n=t.channelGroups,r=void 0===n?[]:n,o=t.state,i=e.config,s={};return r.length>0&&(s["channel-group"]=r.join(",")),o&&(s.state=JSON.stringify(o)),s.heartbeat=i.getPresenceTimeout(),s},handleResponse:function(){return{}}});var ce=Object.freeze({__proto__:null,getOperation:function(){return j.PNGetStateOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.uuid,o=void 0===r?n.UUID:r,i=t.channels,s=void 0===i?[]:i,a=s.length>0?s.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(a),"/uuid/").concat(o)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channelGroups,r=void 0===n?[]:n,o={};return r.length>0&&(o["channel-group"]=r.join(",")),o},handleResponse:function(e,t,n){var r=n.channels,o=void 0===r?[]:r,i=n.channelGroups,s=void 0===i?[]:i,a={};return 1===o.length&&0===s.length?a[o[0]]=t.payload:a=t.payload,{channels:a}}});var le=Object.freeze({__proto__:null,getOperation:function(){return j.PNSetStateOperation},validateParams:function(e,t){var n=e.config,r=t.state,o=t.channels,i=void 0===o?[]:o,s=t.channelGroups,a=void 0===s?[]:s;return r?n.subscribeKey?0===i.length&&0===a.length?"Please provide a list of channels and/or channel-groups":void 0:"Missing Subscribe Key":"Missing State"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=o.length>0?o.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(i),"/uuid/").concat(H.encodeString(n.UUID),"/data")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.state,r=t.channelGroups,o=void 0===r?[]:r,i={};return i.state=JSON.stringify(n),o.length>0&&(i["channel-group"]=o.join(",")),i},handleResponse:function(e,t){return{state:t.payload}}});var pe=Object.freeze({__proto__:null,getOperation:function(){return j.PNHereNowOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=t.channelGroups,s=void 0===i?[]:i,a="/v2/presence/sub-key/".concat(n.subscribeKey);if(o.length>0||s.length>0){var u=o.length>0?o.join(","):",";a+="/channel/".concat(H.encodeString(u))}return a},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.channelGroups,o=void 0===r?[]:r,i=t.includeUUIDs,s=void 0===i||i,a=t.includeState,u=void 0!==a&&a,c=t.queryParameters,l=void 0===c?{}:c,p={};return s||(p.disable_uuids=1),u&&(p.state=1),o.length>0&&(p["channel-group"]=o.join(",")),p=n(n({},p),l)},handleResponse:function(e,t,n){var r=n.channels,o=void 0===r?[]:r,i=n.channelGroups,s=void 0===i?[]:i,a=n.includeUUIDs,u=void 0===a||a,c=n.includeState,l=void 0!==c&&c;return o.length>1||s.length>0||0===s.length&&0===o.length?function(){var e={};return e.totalChannels=t.payload.total_channels,e.totalOccupancy=t.payload.total_occupancy,e.channels={},Object.keys(t.payload.channels).forEach((function(n){var r=t.payload.channels[n],o=[];return e.channels[n]={occupants:o,name:n,occupancy:r.occupancy},u&&r.uuids.forEach((function(e){l?o.push({state:e.state,uuid:e.uuid}):o.push({state:null,uuid:e})})),e})),e}():function(){var e={},n=[];return e.totalChannels=1,e.totalOccupancy=t.occupancy,e.channels={},e.channels[o[0]]={occupants:n,name:o[0],occupancy:t.occupancy},u&&t.uuids&&t.uuids.forEach((function(e){l?n.push({state:e.state,uuid:e.uuid}):n.push({state:null,uuid:e})})),e}()},handleError:function(e,t,n){402!==n.statusCode||this.getURL(e,t).includes("channel")||(n.errorData.message="You have tried to perform a Global Here Now operation, your keyset configuration does not support that. Please provide a channel, or enable the Global Here Now feature from the Portal.")}});var he=Object.freeze({__proto__:null,getOperation:function(){return j.PNAddMessageActionOperation},validateParams:function(e,t){var n=e.config,r=t.action,o=t.channel;return t.messageTimetoken?n.subscribeKey?o?r?r.value?r.type?r.type.length>15?"Action.type value exceed maximum length of 15":void 0:"Missing Action.type":"Missing Action.value":"Missing Action":"Missing message channel":"Missing Subscribe Key":"Missing message timetoken"},usePost:function(){return!0},postURL:function(e,t){var n=e.config,r=t.channel,o=t.messageTimetoken;return"/v1/message-actions/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(r),"/message/").concat(o)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},getRequestHeaders:function(){return{"Content-Type":"application/json"}},isAuthSupported:function(){return!0},prepareParams:function(){return{}},postPayload:function(e,t){return t.action},handleResponse:function(e,t){return{data:t.data}}});var fe=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveMessageActionOperation},validateParams:function(e,t){var n=e.config,r=t.channel,o=t.actionTimetoken;return t.messageTimetoken?o?n.subscribeKey?r?void 0:"Missing message channel":"Missing Subscribe Key":"Missing action timetoken":"Missing message timetoken"},useDelete:function(){return!0},getURL:function(e,t){var n=e.config,r=t.channel,o=t.actionTimetoken,i=t.messageTimetoken;return"/v1/message-actions/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(r),"/message/").concat(i,"/action/").concat(o)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{data:t.data}}});var de=Object.freeze({__proto__:null,getOperation:function(){return j.PNGetMessageActionsOperation},validateParams:function(e,t){var n=e.config,r=t.channel;return n.subscribeKey?r?void 0:"Missing message channel":"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channel;return"/v1/message-actions/".concat(n.subscribeKey,"/channel/").concat(H.encodeString(r))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.limit,r=t.start,o=t.end,i={};return n&&(i.limit=n),r&&(i.start=r),o&&(i.end=o),i},handleResponse:function(e,t){var n={data:t.data,start:null,end:null};return n.data.length&&(n.end=n.data[n.data.length-1].actionTimetoken,n.start=n.data[0].actionTimetoken),n}}),ye={getOperation:function(){return j.PNListFilesOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"channel can't be empty"},getURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel),"/files")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n={};return t.limit&&(n.limit=t.limit),t.next&&(n.next=t.next),n},handleResponse:function(e,t){return{status:t.status,data:t.data,next:t.next,count:t.count}}},ge={getOperation:function(){return j.PNGenerateUploadUrlOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.name)?void 0:"name can't be empty":"channel can't be empty"},usePost:function(){return!0},postURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel),"/generate-upload-url")},postPayload:function(e,t){return{name:t.name}},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{status:t.status,data:t.data,file_upload_request:t.file_upload_request}}},me={getOperation:function(){return j.PNPublishFileOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.fileId)?(null==t?void 0:t.fileName)?void 0:"file name can't be empty":"file id can't be empty":"channel can't be empty"},getURL:function(e,t){var n=e.config,r=n.publishKey,o=n.subscribeKey,i=function(e,t){var n=JSON.stringify(t);if(e.cryptoModule){var r=e.cryptoModule.encrypt(n);n="string"==typeof r?r:b(r),n=JSON.stringify(n)}return n||""}(e,{message:t.message,file:{name:t.fileName,id:t.fileId}});return"/v1/files/publish-file/".concat(r,"/").concat(o,"/0/").concat(H.encodeString(t.channel),"/0/").concat(H.encodeString(i))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n={};return t.ttl&&(n.ttl=t.ttl),void 0!==t.storeInHistory&&(n.store=t.storeInHistory?"1":"0"),t.meta&&"object"==typeof t.meta&&(n.meta=JSON.stringify(t.meta)),n},handleResponse:function(e,t){return{timetoken:t[2]}}},be=function(e){var t=function(e){var t=this,n=e.generateUploadUrl,r=e.publishFile,s=e.modules,a=s.PubNubFile,u=s.config,c=s.cryptography,l=s.cryptoModule,p=s.networking;return function(e){var s=e.channel,h=e.file,f=e.message,d=e.cipherKey,y=e.meta,g=e.ttl,m=e.storeInHistory;return o(t,void 0,void 0,(function(){var e,t,o,b,v,_,S,w,O,P,E,A,T,N,C,k,M,j,R,x,U,I,D,F,L,G,K,B,H;return i(this,(function(i){switch(i.label){case 0:if(!s)throw new q("Validation failed, check status for details",z("channel can't be empty"));if(!h)throw new q("Validation failed, check status for details",z("file can't be empty"));return e=a.create(h),[4,n({channel:s,name:e.name})];case 1:return t=i.sent(),o=t.file_upload_request,b=o.url,v=o.form_fields,_=t.data,S=_.id,w=_.name,a.supportsEncryptFile&&(d||l)?null!=d?[3,3]:[4,l.encryptFile(e,a)]:[3,6];case 2:return O=i.sent(),[3,5];case 3:return[4,c.encryptFile(d,e,a)];case 4:O=i.sent(),i.label=5;case 5:e=O,i.label=6;case 6:P=v,e.mimeType&&(P=v.map((function(t){return"Content-Type"===t.key?{key:t.key,value:e.mimeType}:t}))),i.label=7;case 7:return i.trys.push([7,21,,22]),a.supportsFileUri&&h.uri?(T=(A=p).POSTFILE,N=[b,P],[4,e.toFileUri()]):[3,10];case 8:return[4,T.apply(A,N.concat([i.sent()]))];case 9:return E=i.sent(),[3,20];case 10:return a.supportsFile?(k=(C=p).POSTFILE,M=[b,P],[4,e.toFile()]):[3,13];case 11:return[4,k.apply(C,M.concat([i.sent()]))];case 12:return E=i.sent(),[3,20];case 13:return a.supportsBuffer?(R=(j=p).POSTFILE,x=[b,P],[4,e.toBuffer()]):[3,16];case 14:return[4,R.apply(j,x.concat([i.sent()]))];case 15:return E=i.sent(),[3,20];case 16:return a.supportsBlob?(I=(U=p).POSTFILE,D=[b,P],[4,e.toBlob()]):[3,19];case 17:return[4,I.apply(U,D.concat([i.sent()]))];case 18:return E=i.sent(),[3,20];case 19:throw new Error("Unsupported environment");case 20:return[3,22];case 21:throw(F=i.sent()).response&&"string"==typeof F.response.text?(L=F.response.text,G=/<Message>(.*)<\/Message>/gi.exec(L),new q(G?"Upload to bucket failed: ".concat(G[1]):"Upload to bucket failed.",F)):new q("Upload to bucket failed.",F);case 22:if(204!==E.status)throw new q("Upload to bucket was unsuccessful",E);K=u.fileUploadPublishRetryLimit,B=!1,H={timetoken:"0"},i.label=23;case 23:return i.trys.push([23,25,,26]),[4,r({channel:s,message:f,fileId:S,fileName:w,meta:y,storeInHistory:m,ttl:g})];case 24:return H=i.sent(),B=!0,[3,26];case 25:return i.sent(),K-=1,[3,26];case 26:if(!B&&K>0)return[3,23];i.label=27;case 27:if(B)return[2,{timetoken:H.timetoken,id:S,name:w}];throw new q("Publish failed. You may want to execute that operation manually using pubnub.publishFile",{channel:s,id:S,name:w})}}))}))}}(e);return function(e,n){var r=t(e);return"function"==typeof n?(r.then((function(e){return n(null,e)})).catch((function(e){return n(e,null)})),r):r}},ve=function(e,t){var n=t.channel,r=t.id,o=t.name,i=e.config,s=e.networking,a=e.tokenManager;if(!n)throw new q("Validation failed, check status for details",z("channel can't be empty"));if(!r)throw new q("Validation failed, check status for details",z("file id can't be empty"));if(!o)throw new q("Validation failed, check status for details",z("file name can't be empty"));var u="/v1/files/".concat(i.subscribeKey,"/channels/").concat(H.encodeString(n),"/files/").concat(r,"/").concat(o),c={};c.uuid=i.getUUID(),c.pnsdk=W(i);var l=a.getToken()||i.getAuthKey();l&&(c.auth=l),i.secretKey&&$(e,u,c,{},{getOperation:function(){return"PubNubGetFileUrlOperation"}});var p=Object.keys(c).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(c[e]))})).join("&");return""!==p?"".concat(s.getStandardOrigin()).concat(u,"?").concat(p):"".concat(s.getStandardOrigin()).concat(u)},_e={getOperation:function(){return j.PNDownloadFileOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.name)?(null==t?void 0:t.id)?void 0:"id can't be empty":"name can't be empty":"channel can't be empty"},useGetFile:function(){return!0},getFileURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel),"/files/").concat(t.id,"/").concat(t.name)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},ignoreBody:function(){return!0},forceBuffered:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t,n){var r=e.PubNubFile,s=e.config,a=e.cryptography,u=e.cryptoModule;return o(void 0,void 0,void 0,(function(){var e,o,c,l;return i(this,(function(i){switch(i.label){case 0:return e=t.response.body,r.supportsEncryptFile&&(n.cipherKey||u)?null!=n.cipherKey?[3,2]:[4,u.decryptFile(r.create({data:e,name:n.name}),r)]:[3,5];case 1:return o=i.sent().data,[3,4];case 2:return[4,a.decrypt(null!==(c=n.cipherKey)&&void 0!==c?c:s.cipherKey,e)];case 3:o=i.sent(),i.label=4;case 4:e=o,i.label=5;case 5:return[2,r.create({data:e,name:null!==(l=t.response.name)&&void 0!==l?l:n.name,mimeType:t.response.type})]}}))}))}},Se={getOperation:function(){return j.PNListFilesOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.id)?(null==t?void 0:t.name)?void 0:"file name can't be empty":"file id can't be empty":"channel can't be empty"},useDelete:function(){return!0},getURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel),"/files/").concat(t.id,"/").concat(t.name)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{status:t.status}}},we={getOperation:function(){return j.PNGetAllUUIDMetadataOperation},validateParams:function(){},getURL:function(e){var t=e.config;return"/v2/objects/".concat(t.subscribeKey,"/uuids")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o,i,s,u,c,l,p,h={include:["status","type"]};return(null==t?void 0:t.include)&&(null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),h.include=h.include.join(","),(null===(r=null==t?void 0:t.include)||void 0===r?void 0:r.totalCount)&&(h.count=null===(o=t.include)||void 0===o?void 0:o.totalCount),(null===(i=null==t?void 0:t.page)||void 0===i?void 0:i.next)&&(h.start=null===(s=t.page)||void 0===s?void 0:s.next),(null===(u=null==t?void 0:t.page)||void 0===u?void 0:u.prev)&&(h.end=null===(c=t.page)||void 0===c?void 0:c.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),h.limit=null!==(l=null==t?void 0:t.limit)&&void 0!==l?l:100,(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,next:t.next,prev:t.prev}}},Oe={getOperation:function(){return j.PNGetUUIDMetadataOperation},validateParams:function(){},getURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(H.encodeString(null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o=e.config,i={};return i.uuid=null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:o.getUUID(),i.include=["status","type","custom"],(null==t?void 0:t.include)&&!1===(null===(r=t.include)||void 0===r?void 0:r.customFields)&&i.include.pop(),i.include=i.include.join(","),i},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Pe={getOperation:function(){return j.PNSetUUIDMetadataOperation},validateParams:function(e,t){if(!(null==t?void 0:t.data))return"Data cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(H.encodeString(null!==(n=t.uuid)&&void 0!==n?n:r.getUUID()))},patchPayload:function(e,t){return t.data},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o=e.config,i={};return i.uuid=null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:o.getUUID(),i.include=["status","type","custom"],(null==t?void 0:t.include)&&!1===(null===(r=t.include)||void 0===r?void 0:r.customFields)&&i.include.pop(),i.include=i.include.join(","),i},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Ee={getOperation:function(){return j.PNRemoveUUIDMetadataOperation},validateParams:function(){},getURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(H.encodeString(null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()))},useDelete:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r=e.config;return{uuid:null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()}},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Ae={getOperation:function(){return j.PNGetAllChannelMetadataOperation},validateParams:function(){},getURL:function(e){var t=e.config;return"/v2/objects/".concat(t.subscribeKey,"/channels")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o,i,s,u,c,l,p,h={include:["status","type"]};return(null==t?void 0:t.include)&&(null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),h.include=h.include.join(","),(null===(r=null==t?void 0:t.include)||void 0===r?void 0:r.totalCount)&&(h.count=null===(o=t.include)||void 0===o?void 0:o.totalCount),(null===(i=null==t?void 0:t.page)||void 0===i?void 0:i.next)&&(h.start=null===(s=t.page)||void 0===s?void 0:s.next),(null===(u=null==t?void 0:t.page)||void 0===u?void 0:u.prev)&&(h.end=null===(c=t.page)||void 0===c?void 0:c.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),h.limit=null!==(l=null==t?void 0:t.limit)&&void 0!==l?l:100,(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},Te={getOperation:function(){return j.PNGetChannelMetadataOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"Channel cannot be empty"},getURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r={include:["status","type","custom"]};return(null==t?void 0:t.include)&&!1===(null===(n=t.include)||void 0===n?void 0:n.customFields)&&r.include.pop(),r.include=r.include.join(","),r},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Ne={getOperation:function(){return j.PNSetChannelMetadataOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.data)?void 0:"Data cannot be empty":"Channel cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel))},patchPayload:function(e,t){return t.data},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r={include:["status","type","custom"]};return(null==t?void 0:t.include)&&!1===(null===(n=t.include)||void 0===n?void 0:n.customFields)&&r.include.pop(),r.include=r.include.join(","),r},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Ce={getOperation:function(){return j.PNRemoveChannelMetadataOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"Channel cannot be empty"},getURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel))},useDelete:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{status:t.status,data:t.data}}},ke={getOperation:function(){return j.PNGetMembersOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"channel cannot be empty"},getURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel),"/uuids")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o,i,s,u,c,l,p,h,f,d,y,g,m={include:[]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.statusField)&&m.include.push("status"),(null===(r=t.include)||void 0===r?void 0:r.customFields)&&m.include.push("custom"),(null===(o=t.include)||void 0===o?void 0:o.UUIDFields)&&m.include.push("uuid"),(null===(i=t.include)||void 0===i?void 0:i.customUUIDFields)&&m.include.push("uuid.custom"),(null===(s=t.include)||void 0===s?void 0:s.UUIDStatusField)&&m.include.push("uuid.status"),(null===(u=t.include)||void 0===u?void 0:u.UUIDTypeField)&&m.include.push("uuid.type")),m.include=m.include.join(","),(null===(c=null==t?void 0:t.include)||void 0===c?void 0:c.totalCount)&&(m.count=null===(l=t.include)||void 0===l?void 0:l.totalCount),(null===(p=null==t?void 0:t.page)||void 0===p?void 0:p.next)&&(m.start=null===(h=t.page)||void 0===h?void 0:h.next),(null===(f=null==t?void 0:t.page)||void 0===f?void 0:f.prev)&&(m.end=null===(d=t.page)||void 0===d?void 0:d.prev),(null==t?void 0:t.filter)&&(m.filter=t.filter),m.limit=null!==(y=null==t?void 0:t.limit)&&void 0!==y?y:100,(null==t?void 0:t.sort)&&(m.sort=Object.entries(null!==(g=t.sort)&&void 0!==g?g:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),m},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},Me={getOperation:function(){return j.PNSetMembersOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.uuids)&&0!==(null==t?void 0:t.uuids.length)?void 0:"UUIDs cannot be empty":"Channel cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(H.encodeString(t.channel),"/uuids")},patchPayload:function(e,t){var n;return(n={set:[],delete:[]})[t.type]=t.uuids.map((function(e){return"string"==typeof e?{uuid:{id:e}}:{uuid:{id:e.id},custom:e.custom,status:e.status}})),n},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o,i,s,u,c,l,p,h={include:["uuid.status","uuid.type","type"]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),(null===(r=t.include)||void 0===r?void 0:r.customUUIDFields)&&h.include.push("uuid.custom"),(null===(o=t.include)||void 0===o?void 0:o.UUIDFields)&&h.include.push("uuid")),h.include=h.include.join(","),(null===(i=null==t?void 0:t.include)||void 0===i?void 0:i.totalCount)&&(h.count=!0),(null===(s=null==t?void 0:t.page)||void 0===s?void 0:s.next)&&(h.start=null===(u=t.page)||void 0===u?void 0:u.next),(null===(c=null==t?void 0:t.page)||void 0===c?void 0:c.prev)&&(h.end=null===(l=t.page)||void 0===l?void 0:l.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),null!=t.limit&&(h.limit=t.limit),(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},je={getOperation:function(){return j.PNGetMembershipsOperation},validateParams:function(){},getURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(H.encodeString(null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()),"/channels")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o,i,s,u,c,l,p,h,f,d,y,g,m={include:[]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.statusField)&&m.include.push("status"),(null===(r=t.include)||void 0===r?void 0:r.customFields)&&m.include.push("custom"),(null===(o=t.include)||void 0===o?void 0:o.channelFields)&&m.include.push("channel"),(null===(i=t.include)||void 0===i?void 0:i.customChannelFields)&&m.include.push("channel.custom"),(null===(s=t.include)||void 0===s?void 0:s.channelStatusField)&&m.include.push("channel.status"),(null===(u=t.include)||void 0===u?void 0:u.channelTypeField)&&m.include.push("channel.type")),m.include=m.include.join(","),(null===(c=null==t?void 0:t.include)||void 0===c?void 0:c.totalCount)&&(m.count=null===(l=t.include)||void 0===l?void 0:l.totalCount),(null===(p=null==t?void 0:t.page)||void 0===p?void 0:p.next)&&(m.start=null===(h=t.page)||void 0===h?void 0:h.next),(null===(f=null==t?void 0:t.page)||void 0===f?void 0:f.prev)&&(m.end=null===(d=t.page)||void 0===d?void 0:d.prev),(null==t?void 0:t.filter)&&(m.filter=t.filter),m.limit=null!==(y=null==t?void 0:t.limit)&&void 0!==y?y:100,(null==t?void 0:t.sort)&&(m.sort=Object.entries(null!==(g=t.sort)&&void 0!==g?g:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),m},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},Re={getOperation:function(){return j.PNSetMembershipsOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channels)||0===(null==t?void 0:t.channels.length))return"Channels cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(H.encodeString(null!==(n=t.uuid)&&void 0!==n?n:r.getUUID()),"/channels")},patchPayload:function(e,t){var n;return(n={set:[],delete:[]})[t.type]=t.channels.map((function(e){return"string"==typeof e?{channel:{id:e}}:{channel:{id:e.id},custom:e.custom,status:e.status}})),n},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,o,i,s,u,c,l,p,h={include:["channel.status","channel.type","status"]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),(null===(r=t.include)||void 0===r?void 0:r.customChannelFields)&&h.include.push("channel.custom"),(null===(o=t.include)||void 0===o?void 0:o.channelFields)&&h.include.push("channel")),h.include=h.include.join(","),(null===(i=null==t?void 0:t.include)||void 0===i?void 0:i.totalCount)&&(h.count=!0),(null===(s=null==t?void 0:t.page)||void 0===s?void 0:s.next)&&(h.start=null===(u=t.page)||void 0===u?void 0:u.next),(null===(c=null==t?void 0:t.page)||void 0===c?void 0:c.prev)&&(h.end=null===(l=t.page)||void 0===l?void 0:l.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),null!=t.limit&&(h.limit=t.limit),(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}};var xe=Object.freeze({__proto__:null,getOperation:function(){return j.PNAccessManagerAudit},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e){var t=e.config;return"/v2/auth/audit/sub-key/".concat(t.subscribeKey)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(e,t){var n=t.channel,r=t.channelGroup,o=t.authKeys,i=void 0===o?[]:o,s={};return n&&(s.channel=n),r&&(s["channel-group"]=r),i.length>0&&(s.auth=i.join(",")),s},handleResponse:function(e,t){return t.payload}});var Ue=Object.freeze({__proto__:null,getOperation:function(){return j.PNAccessManagerGrant},validateParams:function(e,t){var n=e.config;return n.subscribeKey?n.publishKey?n.secretKey?null==t.uuids||t.authKeys?null==t.uuids||null==t.channels&&null==t.channelGroups?void 0:"Both channel/channelgroup and uuid cannot be used in the same request":"authKeys are required for grant request on uuids":"Missing Secret Key":"Missing Publish Key":"Missing Subscribe Key"},getURL:function(e){var t=e.config;return"/v2/auth/grant/sub-key/".concat(t.subscribeKey)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(e,t){var n=t.channels,r=void 0===n?[]:n,o=t.channelGroups,i=void 0===o?[]:o,s=t.uuids,a=void 0===s?[]:s,u=t.ttl,c=t.read,l=void 0!==c&&c,p=t.write,h=void 0!==p&&p,f=t.manage,d=void 0!==f&&f,y=t.get,g=void 0!==y&&y,m=t.join,b=void 0!==m&&m,v=t.update,_=void 0!==v&&v,S=t.authKeys,w=void 0===S?[]:S,O=t.delete,P={};return P.r=l?"1":"0",P.w=h?"1":"0",P.m=d?"1":"0",P.d=O?"1":"0",P.g=g?"1":"0",P.j=b?"1":"0",P.u=_?"1":"0",r.length>0&&(P.channel=r.join(",")),i.length>0&&(P["channel-group"]=i.join(",")),w.length>0&&(P.auth=w.join(",")),a.length>0&&(P["target-uuid"]=a.join(",")),(u||0===u)&&(P.ttl=u),P},handleResponse:function(){return{}}});function Ie(e){var t,n,r,o,i=void 0!==(null==e?void 0:e.authorizedUserId),s=void 0!==(null===(t=null==e?void 0:e.resources)||void 0===t?void 0:t.users),a=void 0!==(null===(n=null==e?void 0:e.resources)||void 0===n?void 0:n.spaces),u=void 0!==(null===(r=null==e?void 0:e.patterns)||void 0===r?void 0:r.users),c=void 0!==(null===(o=null==e?void 0:e.patterns)||void 0===o?void 0:o.spaces);return u||s||c||a||i}function De(e){var t=0;return e.join&&(t|=128),e.update&&(t|=64),e.get&&(t|=32),e.delete&&(t|=8),e.manage&&(t|=4),e.write&&(t|=2),e.read&&(t|=1),t}function Fe(e,t){if(Ie(t))return function(e,t){var n=t.ttl,r=t.resources,o=t.patterns,i=t.meta,s=t.authorizedUserId,a={ttl:0,permissions:{resources:{channels:{},groups:{},uuids:{},users:{},spaces:{}},patterns:{channels:{},groups:{},uuids:{},users:{},spaces:{}},meta:{}}};if(r){var u=r.users,c=r.spaces,l=r.groups;u&&Object.keys(u).forEach((function(e){a.permissions.resources.uuids[e]=De(u[e])})),c&&Object.keys(c).forEach((function(e){a.permissions.resources.channels[e]=De(c[e])})),l&&Object.keys(l).forEach((function(e){a.permissions.resources.groups[e]=De(l[e])}))}if(o){var p=o.users,h=o.spaces,f=o.groups;p&&Object.keys(p).forEach((function(e){a.permissions.patterns.uuids[e]=De(p[e])})),h&&Object.keys(h).forEach((function(e){a.permissions.patterns.channels[e]=De(h[e])})),f&&Object.keys(f).forEach((function(e){a.permissions.patterns.groups[e]=De(f[e])}))}return(n||0===n)&&(a.ttl=n),i&&(a.permissions.meta=i),s&&(a.permissions.uuid="".concat(s)),a}(0,t);var n=t.ttl,r=t.resources,o=t.patterns,i=t.meta,s=t.authorized_uuid,a={ttl:0,permissions:{resources:{channels:{},groups:{},uuids:{},users:{},spaces:{}},patterns:{channels:{},groups:{},uuids:{},users:{},spaces:{}},meta:{}}};if(r){var u=r.uuids,c=r.channels,l=r.groups;u&&Object.keys(u).forEach((function(e){a.permissions.resources.uuids[e]=De(u[e])})),c&&Object.keys(c).forEach((function(e){a.permissions.resources.channels[e]=De(c[e])})),l&&Object.keys(l).forEach((function(e){a.permissions.resources.groups[e]=De(l[e])}))}if(o){var p=o.uuids,h=o.channels,f=o.groups;p&&Object.keys(p).forEach((function(e){a.permissions.patterns.uuids[e]=De(p[e])})),h&&Object.keys(h).forEach((function(e){a.permissions.patterns.channels[e]=De(h[e])})),f&&Object.keys(f).forEach((function(e){a.permissions.patterns.groups[e]=De(f[e])}))}return(n||0===n)&&(a.ttl=n),i&&(a.permissions.meta=i),s&&(a.permissions.uuid="".concat(s)),a}var Le=Object.freeze({__proto__:null,getOperation:function(){return j.PNAccessManagerGrantToken},extractPermissions:De,validateParams:function(e,t){var n,r,o,i,s,a,u=e.config;if(!u.subscribeKey)return"Missing Subscribe Key";if(!u.publishKey)return"Missing Publish Key";if(!u.secretKey)return"Missing Secret Key";if(!t.resources&&!t.patterns)return"Missing either Resources or Patterns.";var c=void 0!==(null==t?void 0:t.authorized_uuid),l=void 0!==(null===(n=null==t?void 0:t.resources)||void 0===n?void 0:n.uuids),p=void 0!==(null===(r=null==t?void 0:t.resources)||void 0===r?void 0:r.channels),h=void 0!==(null===(o=null==t?void 0:t.resources)||void 0===o?void 0:o.groups),f=void 0!==(null===(i=null==t?void 0:t.patterns)||void 0===i?void 0:i.uuids),d=void 0!==(null===(s=null==t?void 0:t.patterns)||void 0===s?void 0:s.channels),y=void 0!==(null===(a=null==t?void 0:t.patterns)||void 0===a?void 0:a.groups),g=c||l||f||p||d||h||y;return Ie(t)&&g?"Cannot mix `users`, `spaces` and `authorizedUserId` with `uuids`, `channels`, `groups` and `authorized_uuid`":(!t.resources||t.resources.uuids&&0!==Object.keys(t.resources.uuids).length||t.resources.channels&&0!==Object.keys(t.resources.channels).length||t.resources.groups&&0!==Object.keys(t.resources.groups).length||t.resources.users&&0!==Object.keys(t.resources.users).length||t.resources.spaces&&0!==Object.keys(t.resources.spaces).length)&&(!t.patterns||t.patterns.uuids&&0!==Object.keys(t.patterns.uuids).length||t.patterns.channels&&0!==Object.keys(t.patterns.channels).length||t.patterns.groups&&0!==Object.keys(t.patterns.groups).length||t.patterns.users&&0!==Object.keys(t.patterns.users).length||t.patterns.spaces&&0!==Object.keys(t.patterns.spaces).length)?void 0:"Missing values for either Resources or Patterns."},postURL:function(e){var t=e.config;return"/v3/pam/".concat(t.subscribeKey,"/grant")},usePost:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(){return{}},postPayload:function(e,t){return Fe(0,t)},handleResponse:function(e,t){return t.data.token}}),Ge={getOperation:function(){return j.PNAccessManagerRevokeToken},validateParams:function(e,t){return e.config.secretKey?t?void 0:"token can't be empty":"Missing Secret Key"},getURL:function(e,t){var n=e.config;return"/v3/pam/".concat(n.subscribeKey,"/grant/").concat(H.encodeString(t))},useDelete:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(e){return{uuid:e.config.getUUID()}},handleResponse:function(e,t){return{status:t.status,data:t.data}}};function Ke(e,t){var n=JSON.stringify(t);if(e.cryptoModule){var r=e.cryptoModule.encrypt(n);n="string"==typeof r?r:b(r),n=JSON.stringify(n)}return n||""}var Be=Object.freeze({__proto__:null,getOperation:function(){return j.PNPublishOperation},validateParams:function(e,t){var n=e.config,r=t.message;return t.channel?r?n.subscribeKey?void 0:"Missing Subscribe Key":"Missing Message":"Missing Channel"},usePost:function(e,t){var n=t.sendByPost;return void 0!==n&&n},getURL:function(e,t){var n=e.config,r=t.channel,o=Ke(e,t.message);return"/publish/".concat(n.publishKey,"/").concat(n.subscribeKey,"/0/").concat(H.encodeString(r),"/0/").concat(H.encodeString(o))},postURL:function(e,t){var n=e.config,r=t.channel;return"/publish/".concat(n.publishKey,"/").concat(n.subscribeKey,"/0/").concat(H.encodeString(r),"/0")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},postPayload:function(e,t){return Ke(e,t.message)},prepareParams:function(e,t){var n=t.meta,r=t.replicate,o=void 0===r||r,i=t.storeInHistory,s=t.ttl,a={};return null!=i&&(a.store=i?"1":"0"),s&&(a.ttl=s),!1===o&&(a.norep="true"),n&&"object"==typeof n&&(a.meta=JSON.stringify(n)),a},handleResponse:function(e,t){return{timetoken:t[2]}}});var He=Object.freeze({__proto__:null,getOperation:function(){return j.PNSignalOperation},validateParams:function(e,t){var n=e.config,r=t.message;return t.channel?r?n.subscribeKey?void 0:"Missing Subscribe Key":"Missing Message":"Missing Channel"},getURL:function(e,t){var n,r=e.config,o=t.channel,i=t.message,s=(n=i,JSON.stringify(n));return"/signal/".concat(r.publishKey,"/").concat(r.subscribeKey,"/0/").concat(H.encodeString(o),"/0/").concat(H.encodeString(s))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{timetoken:t[2]}}});var qe=Object.freeze({__proto__:null,getOperation:function(){return j.PNHistoryOperation},validateParams:function(e,t){var n=t.channel,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing channel"},getURL:function(e,t){var n=t.channel,r=e.config;return"/v2/history/sub-key/".concat(r.subscribeKey,"/channel/").concat(H.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.start,r=t.end,o=t.reverse,i=t.count,s=void 0===i?100:i,a=t.stringifiedTimeToken,u=void 0!==a&&a,c=t.includeMeta,l=void 0!==c&&c,p={include_token:"true"};return p.count=s,n&&(p.start=n),r&&(p.end=r),u&&(p.string_message_token="true"),null!=o&&(p.reverse=o.toString()),l&&(p.include_meta="true"),p},handleResponse:function(e,t){var n={messages:[],startTimeToken:t[1],endTimeToken:t[2]};return Array.isArray(t[0])&&t[0].forEach((function(t){var r=function(e,t){var n={};if(!e.cryptoModule)return n.payload=t,n;try{var r=e.cryptoModule.decrypt(t),o=r instanceof ArrayBuffer?JSON.parse((new TextDecoder).decode(r)):r;return n.payload=o,n}catch(r){e.config.logVerbosity&&console&&console.log&&console.log("decryption error",r.message),n.payload=t,n.error="Error while decrypting message content: ".concat(r.message)}return n}(e,t.message),o={timetoken:t.timetoken,entry:r.payload};t.meta&&(o.meta=t.meta),r.error&&(o.error=r.error),n.messages.push(o)})),n}});var ze=Object.freeze({__proto__:null,getOperation:function(){return j.PNDeleteMessagesOperation},validateParams:function(e,t){var n=t.channel,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing channel"},useDelete:function(){return!0},getURL:function(e,t){var n=t.channel,r=e.config;return"/v3/history/sub-key/".concat(r.subscribeKey,"/channel/").concat(H.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.start,r=t.end,o={};return n&&(o.start=n),r&&(o.end=r),o},handleResponse:function(e,t){return t.payload}});var Ve=Object.freeze({__proto__:null,getOperation:function(){return j.PNMessageCounts},validateParams:function(e,t){var n=t.channels,r=t.timetoken,o=t.channelTimetokens,i=e.config;return n?r&&o?"timetoken and channelTimetokens are incompatible together":o&&o.length>1&&n.length!==o.length?"Length of channelTimetokens and channels do not match":i.subscribeKey?void 0:"Missing Subscribe Key":"Missing channel"},getURL:function(e,t){var n=t.channels,r=e.config,o=n.join(",");return"/v3/history/sub-key/".concat(r.subscribeKey,"/message-counts/").concat(H.encodeString(o))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.timetoken,r=t.channelTimetokens,o={};if(r&&1===r.length){var i=a(r,1)[0];o.timetoken=i}else r?o.channelsTimetoken=r.join(","):n&&(o.timetoken=n);return o},handleResponse:function(e,t){return{channels:t.channels}}});var We=Object.freeze({__proto__:null,getOperation:function(){return j.PNFetchMessagesOperation},validateParams:function(e,t){var n=t.channels,r=t.includeMessageActions,o=void 0!==r&&r,i=e.config;if(!n||0===n.length)return"Missing channels";if(!i.subscribeKey)return"Missing Subscribe Key";if(o&&n.length>1)throw new TypeError("History can return actions data for a single channel only. Either pass a single channel or disable the includeMessageActions flag.")},getURL:function(e,t){var n=t.channels,r=void 0===n?[]:n,o=t.includeMessageActions,i=void 0!==o&&o,s=e.config,a=i?"history-with-actions":"history",u=r.length>0?r.join(","):",";return"/v3/".concat(a,"/sub-key/").concat(s.subscribeKey,"/channel/").concat(H.encodeString(u))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channels,r=t.start,o=t.end,i=t.includeMessageActions,s=t.count,a=t.stringifiedTimeToken,u=void 0!==a&&a,c=t.includeMeta,l=void 0!==c&&c,p=t.includeUuid,h=t.includeUUID,f=void 0===h||h,d=t.includeMessageType,y=void 0===d||d,g={};return g.max=s||(n.length>1||!0===i?25:100),r&&(g.start=r),o&&(g.end=o),u&&(g.string_message_token="true"),l&&(g.include_meta="true"),f&&!1!==p&&(g.include_uuid="true"),y&&(g.include_message_type="true"),g},handleResponse:function(e,t){var n={channels:{}};return Object.keys(t.channels||{}).forEach((function(r){n.channels[r]=[],(t.channels[r]||[]).forEach((function(t){var o={},i=function(e,t){var n={};if(!e.cryptoModule)return n.payload=t,n;try{var r=e.cryptoModule.decrypt(t),o=r instanceof ArrayBuffer?JSON.parse((new TextDecoder).decode(r)):r;return n.payload=o,n}catch(r){e.config.logVerbosity&&console&&console.log&&console.log("decryption error",r.message),n.payload=t,n.error="Error while decrypting message content: ".concat(r.message)}return n}(e,t.message);o.channel=r,o.timetoken=t.timetoken,o.message=i.payload,o.messageType=t.message_type,o.uuid=t.uuid,t.actions&&(o.actions=t.actions,o.data=t.actions),t.meta&&(o.meta=t.meta),i.error&&(o.error=i.error),n.channels[r].push(o)}))})),t.more&&(n.more=t.more),n}});var Je=Object.freeze({__proto__:null,getOperation:function(){return j.PNTimeOperation},getURL:function(){return"/time/0"},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},prepareParams:function(){return{}},isAuthSupported:function(){return!1},handleResponse:function(e,t){return{timetoken:t[0]}},validateParams:function(){}});var $e=Object.freeze({__proto__:null,getOperation:function(){return j.PNSubscribeOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=o.length>0?o.join(","):",";return"/v2/subscribe/".concat(n.subscribeKey,"/").concat(H.encodeString(i),"/0")},getRequestTimeout:function(e){return e.config.getSubscribeTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=e.config,r=t.state,o=t.channelGroups,i=void 0===o?[]:o,s=t.timetoken,a=t.filterExpression,u=t.region,c={heartbeat:n.getPresenceTimeout()};return i.length>0&&(c["channel-group"]=i.join(",")),a&&a.length>0&&(c["filter-expr"]=a),Object.keys(r).length&&(c.state=JSON.stringify(r)),s&&(c.tt=s),u&&(c.tr=u),c},handleResponse:function(e,t){var n=[];t.m.forEach((function(e){var t={timetoken:e.p.t,region:e.p.r},r={shard:parseInt(e.a,10),subscriptionMatch:e.b,channel:e.c,messageType:e.e,payload:e.d,flags:e.f,issuingClientId:e.i,subscribeKey:e.k,originationTimetoken:e.o,userMetadata:e.u,publishMetaData:t};n.push(r)}));var r={timetoken:t.t.t,region:t.t.r};return{messages:n,metadata:r}}}),Qe={getOperation:function(){return j.PNHandshakeOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channels)&&!(null==t?void 0:t.channelGroups))return"channels and channleGroups both should not be empty"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=o.length>0?o.join(","):",";return"/v2/subscribe/".concat(n.subscribeKey,"/").concat(H.encodeString(i),"/0")},getRequestTimeout:function(e){return e.config.getSubscribeTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n={};return t.channelGroups&&t.channelGroups.length>0&&(n["channel-group"]=t.channelGroups.join(",")),n.tt=0,t.state&&(n.state=JSON.stringify(t.state)),t.filterExpression&&t.filterExpression.length>0&&(n["filter-expr"]=t.filterExpression),n.ee="",n},handleResponse:function(e,t){return{region:t.t.r,timetoken:t.t.t}}},Xe={getOperation:function(){return j.PNReceiveMessagesOperation},validateParams:function(e,t){return(null==t?void 0:t.channels)||(null==t?void 0:t.channelGroups)?(null==t?void 0:t.timetoken)?(null==t?void 0:t.region)?void 0:"region can not be empty":"timetoken can not be empty":"channels and channleGroups both should not be empty"},getURL:function(e,t){var n=e.config,r=t.channels,o=void 0===r?[]:r,i=o.length>0?o.join(","):",";return"/v2/subscribe/".concat(n.subscribeKey,"/").concat(H.encodeString(i),"/0")},getRequestTimeout:function(e){return e.config.getSubscribeTimeout()},isAuthSupported:function(){return!0},getAbortSignal:function(e,t){return t.abortSignal},prepareParams:function(e,t){var n={};return t.channelGroups&&t.channelGroups.length>0&&(n["channel-group"]=t.channelGroups.join(",")),t.filterExpression&&t.filterExpression.length>0&&(n["filter-expr"]=t.filterExpression),n.tt=t.timetoken,n.tr=t.region,n.ee="",n},handleResponse:function(e,t){var n=[];return t.m.forEach((function(e){var t={shard:parseInt(e.a,10),subscriptionMatch:e.b,channel:e.c,messageType:e.e,payload:e.d,flags:e.f,issuingClientId:e.i,subscribeKey:e.k,originationTimetoken:e.o,userMetadata:e.u,publishMetaData:{timetoken:e.p.t,region:e.p.r}};n.push(t)})),{messages:n,metadata:{region:t.t.r,timetoken:t.t.t}}}},Ye=function(){function e(e){void 0===e&&(e=!1),this.sync=e,this.listeners=new Set}return e.prototype.subscribe=function(e){var t=this;return this.listeners.add(e),function(){t.listeners.delete(e)}},e.prototype.notify=function(e){var t=this,n=function(){t.listeners.forEach((function(t){t(e)}))};this.sync?n():setTimeout(n,0)},e}(),Ze=function(){function e(e){this.label=e,this.transitionMap=new Map,this.enterEffects=[],this.exitEffects=[]}return e.prototype.transition=function(e,t){var n;if(this.transitionMap.has(t.type))return null===(n=this.transitionMap.get(t.type))||void 0===n?void 0:n(e,t)},e.prototype.on=function(e,t){return this.transitionMap.set(e,t),this},e.prototype.with=function(e,t){return[this,e,null!=t?t:[]]},e.prototype.onEnter=function(e){return this.enterEffects.push(e),this},e.prototype.onExit=function(e){return this.exitEffects.push(e),this},e}(),et=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n.prototype.describe=function(e){return new Ze(e)},n.prototype.start=function(e,t){this.currentState=e,this.currentContext=t,this.notify({type:"engineStarted",state:e,context:t})},n.prototype.transition=function(e){var t,n,r,o,i,u;if(!this.currentState)throw new Error("Start the engine first");this.notify({type:"eventReceived",event:e});var c=this.currentState.transition(this.currentContext,e);if(c){var l=a(c,3),p=l[0],h=l[1],f=l[2];try{for(var d=s(this.currentState.exitEffects),y=d.next();!y.done;y=d.next()){var g=y.value;this.notify({type:"invocationDispatched",invocation:g(this.currentContext)})}}catch(e){t={error:e}}finally{try{y&&!y.done&&(n=d.return)&&n.call(d)}finally{if(t)throw t.error}}var m=this.currentState;this.currentState=p;var b=this.currentContext;this.currentContext=h,this.notify({type:"transitionDone",fromState:m,fromContext:b,toState:p,toContext:h,event:e});try{for(var v=s(f),_=v.next();!_.done;_=v.next()){g=_.value;this.notify({type:"invocationDispatched",invocation:g})}}catch(e){r={error:e}}finally{try{_&&!_.done&&(o=v.return)&&o.call(v)}finally{if(r)throw r.error}}try{for(var S=s(this.currentState.enterEffects),w=S.next();!w.done;w=S.next()){g=w.value;this.notify({type:"invocationDispatched",invocation:g(this.currentContext)})}}catch(e){i={error:e}}finally{try{w&&!w.done&&(u=S.return)&&u.call(S)}finally{if(i)throw i.error}}}},n}(Ye),tt=function(){function e(e){this.dependencies=e,this.instances=new Map,this.handlers=new Map}return e.prototype.on=function(e,t){this.handlers.set(e,t)},e.prototype.dispatch=function(e){if("CANCEL"!==e.type){var t=this.handlers.get(e.type);if(!t)throw new Error("Unhandled invocation '".concat(e.type,"'"));var n=t(e.payload,this.dependencies);e.managed&&this.instances.set(e.type,n),n.start()}else if(this.instances.has(e.payload)){var r=this.instances.get(e.payload);null==r||r.cancel(),this.instances.delete(e.payload)}},e.prototype.dispose=function(){var e,t;try{for(var n=s(this.instances.entries()),r=n.next();!r.done;r=n.next()){var o=a(r.value,2),i=o[0];o[1].cancel(),this.instances.delete(i)}}catch(t){e={error:t}}finally{try{r&&!r.done&&(t=n.return)&&t.call(n)}finally{if(e)throw e.error}}},e}();function nt(e,t){var n=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return{type:e,payload:null==t?void 0:t.apply(void 0,u([],a(n),!1))}};return n.type=e,n}function rt(e,t){var n=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return{type:e,payload:t.apply(void 0,u([],a(n),!1)),managed:!1}};return n.type=e,n}function ot(e,t){var n=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return{type:e,payload:t.apply(void 0,u([],a(n),!1)),managed:!0}};return n.type=e,n.cancel={type:"CANCEL",payload:e,managed:!1},n}var it=function(e){function n(){var t=this.constructor,n=e.call(this,"The operation was aborted.")||this;return n.name="AbortError",Object.setPrototypeOf(n,t.prototype),n}return t(n,e),n}(Error),st=function(e){function n(){var t=null!==e&&e.apply(this,arguments)||this;return t._aborted=!1,t}return t(n,e),Object.defineProperty(n.prototype,"aborted",{get:function(){return this._aborted},enumerable:!1,configurable:!0}),n.prototype.throwIfAborted=function(){if(this._aborted)throw new it},n.prototype.abort=function(){this._aborted=!0,this.notify(new it)},n}(Ye),at=function(e){function n(t,n,r){var o=e.call(this,t,n)||this;return o.asyncFunction=r,o.abortSignal=new st,o}return t(n,e),n.prototype.start=function(){this.asyncFunction(this.payload,this.abortSignal,this.dependencies).catch((function(e){}))},n.prototype.cancel=function(){this.abortSignal.abort()},n}((function(e,t){this.payload=e,this.dependencies=t})),ut=function(e){return function(t,n){return new at(t,n,e)}},ct=ot("HANDSHAKE",(function(e,t){return{channels:e,groups:t}})),lt=ot("RECEIVE_MESSAGES",(function(e,t,n){return{channels:e,groups:t,cursor:n}})),pt=rt("EMIT_MESSAGES",(function(e){return e})),ht=rt("EMIT_STATUS",(function(e){return e})),ft=ot("RECEIVE_RECONNECT",(function(e){return e})),dt=ot("HANDSHAKE_RECONNECT",(function(e){return e})),yt=nt("SUBSCRIPTION_CHANGED",(function(e,t){return{channels:e,groups:t}})),gt=nt("SUBSCRIPTION_RESTORED",(function(e,t,n,r){return{channels:e,groups:t,cursor:{timetoken:n,region:null!=r?r:0}}})),mt=nt("HANDSHAKE_SUCCESS",(function(e){return e})),bt=nt("HANDSHAKE_FAILURE",(function(e){return e})),vt=nt("HANDSHAKE_RECONNECT_SUCCESS",(function(e){return{cursor:e}})),_t=nt("HANDSHAKE_RECONNECT_FAILURE",(function(e){return e})),St=nt("HANDSHAKE_RECONNECT_GIVEUP",(function(e){return e})),wt=nt("RECEIVE_SUCCESS",(function(e,t){return{cursor:e,events:t}})),Ot=nt("RECEIVE_FAILURE",(function(e){return e})),Pt=nt("RECEIVE_RECONNECT_SUCCESS",(function(e,t){return{cursor:e,events:t}})),Et=nt("RECEIVE_RECONNECT_FAILURE",(function(e){return e})),At=nt("RECEIVING_RECONNECT_GIVEUP",(function(e){return e})),Tt=nt("DISCONNECT",(function(){return{}})),Nt=nt("RECONNECT",(function(e,t){return{cursor:{timetoken:null!=e?e:"",region:null!=t?t:0}}})),Ct=nt("UNSUBSCRIBE_ALL",(function(){return{}})),kt=function(e){function r(t,r){var s=e.call(this,r)||this;return s.on(ct.type,ut((function(e,r,a){var u=a.handshake,c=a.presenceState,l=a.config;return o(s,void 0,void 0,(function(){var o,s;return i(this,(function(i){switch(i.label){case 0:r.throwIfAborted(),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,u(n({abortSignal:r,channels:e.channels,channelGroups:e.groups,filterExpression:l.filterExpression},l.maintainPresenceState&&{state:c}))];case 2:return o=i.sent(),[2,t.transition(mt(o))];case 3:return(s=i.sent())instanceof Error&&"Aborted"===s.message?[2]:s instanceof q?[2,t.transition(bt(s))]:[3,4];case 4:return[2]}}))}))}))),s.on(lt.type,ut((function(e,n,r){var a=r.receiveMessages,u=r.config;return o(s,void 0,void 0,(function(){var r,o;return i(this,(function(i){switch(i.label){case 0:n.throwIfAborted(),i.label=1;case 1:return i.trys.push([1,3,,4]),[4,a({abortSignal:n,channels:e.channels,channelGroups:e.groups,timetoken:e.cursor.timetoken,region:e.cursor.region,filterExpression:u.filterExpression})];case 2:return r=i.sent(),t.transition(wt(r.metadata,r.messages)),[3,4];case 3:return(o=i.sent())instanceof Error&&"Aborted"===o.message?[2]:o instanceof q&&!n.aborted?[2,t.transition(Ot(o))]:[3,4];case 4:return[2]}}))}))}))),s.on(pt.type,ut((function(e,t,n){var r=n.emitMessages;return o(s,void 0,void 0,(function(){return i(this,(function(t){return e.length>0&&r(e),[2]}))}))}))),s.on(ht.type,ut((function(e,t,n){var r=n.emitStatus;return o(s,void 0,void 0,(function(){return i(this,(function(t){return r(e),[2]}))}))}))),s.on(ft.type,ut((function(e,n,r){var a=r.receiveMessages,u=r.delay,c=r.config;return o(s,void 0,void 0,(function(){var r,o;return i(this,(function(i){switch(i.label){case 0:return c.retryConfiguration&&c.retryConfiguration.shouldRetry(e.reason,e.attempts)?(n.throwIfAborted(),[4,u(c.retryConfiguration.getDelay(e.attempts,e.reason))]):[3,6];case 1:i.sent(),n.throwIfAborted(),i.label=2;case 2:return i.trys.push([2,4,,5]),[4,a({abortSignal:n,channels:e.channels,channelGroups:e.groups,timetoken:e.cursor.timetoken,region:e.cursor.region,filterExpression:c.filterExpression})];case 3:return r=i.sent(),[2,t.transition(Pt(r.metadata,r.messages))];case 4:return(o=i.sent())instanceof Error&&"Aborted"===o.message?[2]:o instanceof q?[2,t.transition(Et(o))]:[3,5];case 5:return[3,7];case 6:return[2,t.transition(At(new q(c.retryConfiguration.getGiveupReason(e.reason,e.attempts))))];case 7:return[2]}}))}))}))),s.on(dt.type,ut((function(e,r,a){var u=a.handshake,c=a.delay,l=a.presenceState,p=a.config;return o(s,void 0,void 0,(function(){var o,s;return i(this,(function(i){switch(i.label){case 0:return p.retryConfiguration&&p.retryConfiguration.shouldRetry(e.reason,e.attempts)?(r.throwIfAborted(),[4,c(p.retryConfiguration.getDelay(e.attempts,e.reason))]):[3,6];case 1:i.sent(),r.throwIfAborted(),i.label=2;case 2:return i.trys.push([2,4,,5]),[4,u(n({abortSignal:r,channels:e.channels,channelGroups:e.groups,filterExpression:p.filterExpression},p.maintainPresenceState&&{state:l}))];case 3:return o=i.sent(),[2,t.transition(vt(o))];case 4:return(s=i.sent())instanceof Error&&"Aborted"===s.message?[2]:s instanceof q?[2,t.transition(_t(s))]:[3,5];case 5:return[3,7];case 6:return[2,t.transition(St(new q(p.retryConfiguration.getGiveupReason(e.reason,e.attempts))))];case 7:return[2]}}))}))}))),s}return t(r,e),r}(tt),Mt=new Ze("HANDSHAKE_FAILED");Mt.on(yt.type,(function(e,t){return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),Mt.on(Nt.type,(function(e,t){return Ft.with({channels:e.channels,groups:e.groups,cursor:t.payload.cursor||e.cursor})})),Mt.on(gt.type,(function(e,t){var n,r;return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region?t.payload.cursor.region:null!==(r=null===(n=null==e?void 0:e.cursor)||void 0===n?void 0:n.region)&&void 0!==r?r:0}})})),Mt.on(Ct.type,(function(e){return Lt.with()}));var jt=new Ze("HANDSHAKE_STOPPED");jt.on(yt.type,(function(e,t){return jt.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),jt.on(Nt.type,(function(e,t){return Ft.with(n(n({},e),{cursor:t.payload.cursor||e.cursor}))})),jt.on(gt.type,(function(e,t){var n;return jt.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region||(null===(n=null==e?void 0:e.cursor)||void 0===n?void 0:n.region)||0}})})),jt.on(Ct.type,(function(e){return Lt.with()}));var Rt=new Ze("RECEIVE_FAILED");Rt.on(Nt.type,(function(e,t){var n;return Ft.with({channels:e.channels,groups:e.groups,cursor:{timetoken:t.payload.cursor.timetoken?null===(n=t.payload.cursor)||void 0===n?void 0:n.timetoken:e.cursor.timetoken,region:t.payload.cursor.region||e.cursor.region}})})),Rt.on(yt.type,(function(e,t){return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),Rt.on(gt.type,(function(e,t){return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region||e.cursor.region}})})),Rt.on(Ct.type,(function(e){return Lt.with(void 0)}));var xt=new Ze("RECEIVE_STOPPED");xt.on(yt.type,(function(e,t){return xt.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),xt.on(gt.type,(function(e,t){return xt.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region||e.cursor.region}})})),xt.on(Nt.type,(function(e,t){var n;return Ft.with({channels:e.channels,groups:e.groups,cursor:{timetoken:t.payload.cursor.timetoken?null===(n=t.payload.cursor)||void 0===n?void 0:n.timetoken:e.cursor.timetoken,region:t.payload.cursor.region||e.cursor.region}})})),xt.on(Ct.type,(function(){return Lt.with(void 0)}));var Ut=new Ze("RECEIVE_RECONNECTING");Ut.onEnter((function(e){return ft(e)})),Ut.onExit((function(){return ft.cancel})),Ut.on(Pt.type,(function(e,t){return It.with({channels:e.channels,groups:e.groups,cursor:t.payload.cursor},[pt(t.payload.events)])})),Ut.on(Et.type,(function(e,t){return Ut.with(n(n({},e),{attempts:e.attempts+1,reason:t.payload}))})),Ut.on(At.type,(function(e,t){var n;return Rt.with({groups:e.groups,channels:e.channels,cursor:e.cursor,reason:t.payload},[ht({category:k.PNDisconnectedUnexpectedlyCategory,error:null===(n=t.payload)||void 0===n?void 0:n.message})])})),Ut.on(Tt.type,(function(e){return xt.with({channels:e.channels,groups:e.groups,cursor:e.cursor},[ht({category:k.PNDisconnectedCategory})])})),Ut.on(gt.type,(function(e,t){return It.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region||e.cursor.region}})})),Ut.on(yt.type,(function(e,t){return It.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),Ut.on(Ct.type,(function(e){return Lt.with(void 0,[ht({category:k.PNDisconnectedCategory})])}));var It=new Ze("RECEIVING");It.onEnter((function(e){return lt(e.channels,e.groups,e.cursor)})),It.onExit((function(){return lt.cancel})),It.on(wt.type,(function(e,t){return It.with({channels:e.channels,groups:e.groups,cursor:t.payload.cursor},[pt(t.payload.events)])})),It.on(yt.type,(function(e,t){return 0===t.payload.channels.length&&0===t.payload.groups.length?Lt.with(void 0):It.with({cursor:e.cursor,channels:t.payload.channels,groups:t.payload.groups})})),It.on(gt.type,(function(e,t){return 0===t.payload.channels.length&&0===t.payload.groups.length?Lt.with(void 0):It.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region||e.cursor.region}})})),It.on(Ot.type,(function(e,t){return Ut.with(n(n({},e),{attempts:0,reason:t.payload}))})),It.on(Tt.type,(function(e){return xt.with({channels:e.channels,groups:e.groups,cursor:e.cursor},[ht({category:k.PNDisconnectedCategory})])})),It.on(Ct.type,(function(e){return Lt.with(void 0,[ht({category:k.PNDisconnectedCategory})])}));var Dt=new Ze("HANDSHAKE_RECONNECTING");Dt.onEnter((function(e){return dt(e)})),Dt.onExit((function(){return dt.cancel})),Dt.on(vt.type,(function(e,t){var n,r,o={timetoken:(null===(n=e.cursor)||void 0===n?void 0:n.timetoken)?null===(r=e.cursor)||void 0===r?void 0:r.timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region};return It.with({channels:e.channels,groups:e.groups,cursor:o},[ht({category:k.PNConnectedCategory})])})),Dt.on(_t.type,(function(e,t){return Dt.with(n(n({},e),{attempts:e.attempts+1,reason:t.payload}))})),Dt.on(St.type,(function(e,t){var n;return Mt.with({groups:e.groups,channels:e.channels,cursor:e.cursor,reason:t.payload},[ht({category:k.PNConnectionErrorCategory,error:null===(n=t.payload)||void 0===n?void 0:n.message})])})),Dt.on(Tt.type,(function(e){return jt.with({channels:e.channels,groups:e.groups,cursor:e.cursor})})),Dt.on(yt.type,(function(e,t){return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),Dt.on(gt.type,(function(e,t){var n,r;return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:(null===(n=t.payload.cursor)||void 0===n?void 0:n.region)||(null===(r=null==e?void 0:e.cursor)||void 0===r?void 0:r.region)||0}})})),Dt.on(Ct.type,(function(e){return Lt.with(void 0)}));var Ft=new Ze("HANDSHAKING");Ft.onEnter((function(e){return ct(e.channels,e.groups)})),Ft.onExit((function(){return ct.cancel})),Ft.on(yt.type,(function(e,t){return 0===t.payload.channels.length&&0===t.payload.groups.length?Lt.with(void 0):Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),Ft.on(mt.type,(function(e,t){var n,r;return It.with({channels:e.channels,groups:e.groups,cursor:{timetoken:(null===(n=null==e?void 0:e.cursor)||void 0===n?void 0:n.timetoken)?null===(r=null==e?void 0:e.cursor)||void 0===r?void 0:r.timetoken:t.payload.timetoken,region:t.payload.region}},[ht({category:k.PNConnectedCategory})])})),Ft.on(bt.type,(function(e,t){return Dt.with({channels:e.channels,groups:e.groups,cursor:e.cursor,attempts:0,reason:t.payload})})),Ft.on(Tt.type,(function(e){return jt.with({channels:e.channels,groups:e.groups,cursor:e.cursor})})),Ft.on(gt.type,(function(e,t){var n;return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:{timetoken:t.payload.cursor.timetoken,region:t.payload.cursor.region||(null===(n=null==e?void 0:e.cursor)||void 0===n?void 0:n.region)||0}})})),Ft.on(Ct.type,(function(e){return Lt.with()}));var Lt=new Ze("UNSUBSCRIBED");Lt.on(yt.type,(function(e,t){return Ft.with({channels:t.payload.channels,groups:t.payload.groups})})),Lt.on(gt.type,(function(e,t){return Ft.with({channels:t.payload.channels,groups:t.payload.groups,cursor:t.payload.cursor})}));var Gt=function(){function e(e){var t=this;this.engine=new et,this.channels=[],this.groups=[],this.dependencies=e,this.dispatcher=new kt(this.engine,e),this._unsubscribeEngine=this.engine.subscribe((function(e){"invocationDispatched"===e.type&&t.dispatcher.dispatch(e.invocation)})),this.engine.start(Lt,void 0)}return Object.defineProperty(e.prototype,"_engine",{get:function(){return this.engine},enumerable:!1,configurable:!0}),e.prototype.subscribe=function(e){var t=this,n=e.channels,r=e.channelGroups,o=e.timetoken,i=e.withPresence;this.channels=u(u([],a(this.channels),!1),a(null!=n?n:[]),!1),this.groups=u(u([],a(this.groups),!1),a(null!=r?r:[]),!1),i&&(this.channels.map((function(e){return t.channels.push("".concat(e,"-pnpres"))})),this.groups.map((function(e){return t.groups.push("".concat(e,"-pnpres"))}))),o?this.engine.transition(gt(Array.from(new Set(u(u([],a(this.channels),!1),a(null!=n?n:[]),!1))),Array.from(new Set(u(u([],a(this.groups),!1),a(null!=r?r:[]),!1))),o)):this.engine.transition(yt(Array.from(new Set(u(u([],a(this.channels),!1),a(null!=n?n:[]),!1))),Array.from(new Set(u(u([],a(this.groups),!1),a(null!=r?r:[]),!1))))),this.dependencies.join&&this.dependencies.join({channels:Array.from(new Set(this.channels.filter((function(e){return!e.endsWith("-pnpres")})))),groups:Array.from(new Set(this.groups.filter((function(e){return!e.endsWith("-pnpres")}))))})},e.prototype.unsubscribe=function(e){var t=this,n=e.channels,r=void 0===n?[]:n,o=e.channelGroups,i=void 0===o?[]:o,s=H.removeSingleOccurance(this.channels,u(u([],a(r),!1),a(r.map((function(e){return"".concat(e,"-pnpres")}))),!1)),c=H.removeSingleOccurance(this.groups,u(u([],a(i),!1),a(i.map((function(e){return"".concat(e,"-pnpres")}))),!1));if(new Set(this.channels).size!==new Set(s).size||new Set(this.groups).size!==new Set(c).size){var l=H.findUniqueCommonElements(this.channels,r),p=H.findUniqueCommonElements(this.groups,i);this.dependencies.presenceState&&(null==l||l.forEach((function(e){return delete t.dependencies.presenceState[e]})),null==p||p.forEach((function(e){return delete t.dependencies.presenceState[e]}))),this.channels=s,this.groups=c,this.engine.transition(yt(Array.from(new Set(this.channels.slice(0))),Array.from(new Set(this.groups.slice(0))))),this.dependencies.leave&&this.dependencies.leave({channels:l.slice(0),groups:p.slice(0)})}},e.prototype.unsubscribeAll=function(){this.channels=[],this.groups=[],this.dependencies.presenceState&&(this.dependencies.presenceState={}),this.engine.transition(yt(this.channels.slice(0),this.groups.slice(0))),this.dependencies.leaveAll&&this.dependencies.leaveAll()},e.prototype.reconnect=function(e){var t=e.timetoken,n=e.region;this.engine.transition(Nt(t,n))},e.prototype.disconnect=function(){this.engine.transition(Tt()),this.dependencies.leaveAll&&this.dependencies.leaveAll()},e.prototype.getSubscribedChannels=function(){return Array.from(new Set(this.channels))},e.prototype.getSubscribedChannelGroups=function(){return Array.from(new Set(this.groups))},e.prototype.dispose=function(){this.disconnect(),this._unsubscribeEngine(),this.dispatcher.dispose()},e}(),Kt=nt("RECONNECT",(function(){return{}})),Bt=nt("DISCONNECT",(function(){return{}})),Ht=nt("JOINED",(function(e,t){return{channels:e,groups:t}})),qt=nt("LEFT",(function(e,t){return{channels:e,groups:t}})),zt=nt("LEFT_ALL",(function(){return{}})),Vt=nt("HEARTBEAT_SUCCESS",(function(e){return{statusCode:e}})),Wt=nt("HEARTBEAT_FAILURE",(function(e){return e})),Jt=nt("HEARTBEAT_GIVEUP",(function(){return{}})),$t=nt("TIMES_UP",(function(){return{}})),Qt=rt("HEARTBEAT",(function(e,t){return{channels:e,groups:t}})),Xt=rt("LEAVE",(function(e,t){return{channels:e,groups:t}})),Yt=rt("EMIT_STATUS",(function(e){return e})),Zt=ot("WAIT",(function(){return{}})),en=ot("DELAYED_HEARTBEAT",(function(e){return e})),tn=function(e){function r(t,r){var s=e.call(this,r)||this;return s.on(Qt.type,ut((function(e,r,a){var u=a.heartbeat,c=a.presenceState,l=a.config;return o(s,void 0,void 0,(function(){var r;return i(this,(function(o){switch(o.label){case 0:return o.trys.push([0,2,,3]),[4,u(n({channels:e.channels,channelGroups:e.groups},l.maintainPresenceState&&{state:c}))];case 1:return o.sent(),t.transition(Vt(200)),[3,3];case 2:return(r=o.sent())instanceof q?[2,t.transition(Wt(r))]:[3,3];case 3:return[2]}}))}))}))),s.on(Xt.type,ut((function(e,t,n){var r=n.leave,a=n.config;return o(s,void 0,void 0,(function(){return i(this,(function(t){switch(t.label){case 0:if(a.suppressLeaveEvents)return[3,4];t.label=1;case 1:return t.trys.push([1,3,,4]),[4,r({channels:e.channels,channelGroups:e.groups})];case 2:case 3:return t.sent(),[3,4];case 4:return[2]}}))}))}))),s.on(Zt.type,ut((function(e,n,r){var a=r.heartbeatDelay;return o(s,void 0,void 0,(function(){return i(this,(function(e){switch(e.label){case 0:return n.throwIfAborted(),[4,a()];case 1:return e.sent(),n.throwIfAborted(),[2,t.transition($t())]}}))}))}))),s.on(en.type,ut((function(e,r,a){var u=a.heartbeat,c=a.retryDelay,l=a.presenceState,p=a.config;return o(s,void 0,void 0,(function(){var o;return i(this,(function(i){switch(i.label){case 0:return p.retryConfiguration&&p.retryConfiguration.shouldRetry(e.reason,e.attempts)?(r.throwIfAborted(),[4,c(p.retryConfiguration.getDelay(e.attempts,e.reason))]):[3,6];case 1:i.sent(),r.throwIfAborted(),i.label=2;case 2:return i.trys.push([2,4,,5]),[4,u(n({channels:e.channels,channelGroups:e.groups},p.maintainPresenceState&&{state:l}))];case 3:return i.sent(),[2,t.transition(Vt(200))];case 4:return(o=i.sent())instanceof Error&&"Aborted"===o.message?[2]:o instanceof q?[2,t.transition(Wt(o))]:[3,5];case 5:return[3,7];case 6:return[2,t.transition(Jt())];case 7:return[2]}}))}))}))),s.on(Yt.type,ut((function(e,t,r){var a=r.emitStatus,u=r.config;return o(s,void 0,void 0,(function(){var t;return i(this,(function(r){return u.announceFailedHeartbeats&&!0===(null===(t=null==e?void 0:e.status)||void 0===t?void 0:t.error)?a(e.status):u.announceSuccessfulHeartbeats&&200===e.statusCode&&a(n(n({},e),{operation:j.PNHeartbeatOperation,error:!1})),[2]}))}))}))),s}return t(r,e),r}(tt),nn=new Ze("HEARTBEAT_STOPPED");nn.on(Ht.type,(function(e,t){return nn.with({channels:u(u([],a(e.channels),!1),a(t.payload.channels),!1),groups:u(u([],a(e.groups),!1),a(t.payload.groups),!1)})})),nn.on(qt.type,(function(e,t){return nn.with({channels:e.channels.filter((function(e){return!t.payload.channels.includes(e)})),groups:e.groups.filter((function(e){return!t.payload.groups.includes(e)}))})})),nn.on(Kt.type,(function(e,t){return an.with({channels:e.channels,groups:e.groups})})),nn.on(zt.type,(function(e,t){return un.with(void 0)}));var rn=new Ze("HEARTBEAT_COOLDOWN");rn.onEnter((function(){return Zt()})),rn.onExit((function(){return Zt.cancel})),rn.on($t.type,(function(e,t){return an.with({channels:e.channels,groups:e.groups})})),rn.on(Ht.type,(function(e,t){return an.with({channels:u(u([],a(e.channels),!1),a(t.payload.channels),!1),groups:u(u([],a(e.groups),!1),a(t.payload.groups),!1)})})),rn.on(qt.type,(function(e,t){return an.with({channels:e.channels.filter((function(e){return!t.payload.channels.includes(e)})),groups:e.groups.filter((function(e){return!t.payload.groups.includes(e)}))},[Xt(t.payload.channels,t.payload.groups)])})),rn.on(Bt.type,(function(e){return nn.with({channels:e.channels,groups:e.groups},[Xt(e.channels,e.groups)])})),rn.on(zt.type,(function(e,t){return un.with(void 0,[Xt(e.channels,e.groups)])}));var on=new Ze("HEARTBEAT_FAILED");on.on(Ht.type,(function(e,t){return an.with({channels:u(u([],a(e.channels),!1),a(t.payload.channels),!1),groups:u(u([],a(e.groups),!1),a(t.payload.groups),!1)})})),on.on(qt.type,(function(e,t){return an.with({channels:e.channels.filter((function(e){return!t.payload.channels.includes(e)})),groups:e.groups.filter((function(e){return!t.payload.groups.includes(e)}))},[Xt(t.payload.channels,t.payload.groups)])})),on.on(Kt.type,(function(e,t){return an.with({channels:e.channels,groups:e.groups})})),on.on(Bt.type,(function(e,t){return nn.with({channels:e.channels,groups:e.groups},[Xt(e.channels,e.groups)])})),on.on(zt.type,(function(e,t){return un.with(void 0,[Xt(e.channels,e.groups)])}));var sn=new Ze("HEARBEAT_RECONNECTING");sn.onEnter((function(e){return en(e)})),sn.onExit((function(){return en.cancel})),sn.on(Ht.type,(function(e,t){return an.with({channels:u(u([],a(e.channels),!1),a(t.payload.channels),!1),groups:u(u([],a(e.groups),!1),a(t.payload.groups),!1)})})),sn.on(qt.type,(function(e,t){return an.with({channels:e.channels.filter((function(e){return!t.payload.channels.includes(e)})),groups:e.groups.filter((function(e){return!t.payload.groups.includes(e)}))},[Xt(t.payload.channels,t.payload.groups)])})),sn.on(Bt.type,(function(e,t){nn.with({channels:e.channels,groups:e.groups},[Xt(e.channels,e.groups)])})),sn.on(Vt.type,(function(e,t){return rn.with({channels:e.channels,groups:e.groups})})),sn.on(Wt.type,(function(e,t){return sn.with(n(n({},e),{attempts:e.attempts+1,reason:t.payload}))})),sn.on(Jt.type,(function(e,t){return on.with({channels:e.channels,groups:e.groups})})),sn.on(zt.type,(function(e,t){return un.with(void 0,[Xt(e.channels,e.groups)])}));var an=new Ze("HEARTBEATING");an.onEnter((function(e){return Qt(e.channels,e.groups)})),an.on(Vt.type,(function(e,t){return rn.with({channels:e.channels,groups:e.groups})})),an.on(Ht.type,(function(e,t){return an.with({channels:u(u([],a(e.channels),!1),a(t.payload.channels),!1),groups:u(u([],a(e.groups),!1),a(t.payload.groups),!1)})})),an.on(qt.type,(function(e,t){return an.with({channels:e.channels.filter((function(e){return!t.payload.channels.includes(e)})),groups:e.groups.filter((function(e){return!t.payload.groups.includes(e)}))},[Xt(t.payload.channels,t.payload.groups)])})),an.on(Wt.type,(function(e,t){return sn.with(n(n({},e),{attempts:0,reason:t.payload}))})),an.on(Bt.type,(function(e){return nn.with({channels:e.channels,groups:e.groups},[Xt(e.channels,e.groups)])})),an.on(zt.type,(function(e,t){return un.with(void 0,[Xt(e.channels,e.groups)])}));var un=new Ze("HEARTBEAT_INACTIVE");un.on(Ht.type,(function(e,t){return an.with({channels:t.payload.channels,groups:t.payload.groups})}));var cn=function(){function e(e){var t=this;this.engine=new et,this.channels=[],this.groups=[],this.dispatcher=new tn(this.engine,e),this.dependencies=e,this._unsubscribeEngine=this.engine.subscribe((function(e){"invocationDispatched"===e.type&&t.dispatcher.dispatch(e.invocation)})),this.engine.start(un,void 0)}return Object.defineProperty(e.prototype,"_engine",{get:function(){return this.engine},enumerable:!1,configurable:!0}),e.prototype.join=function(e){var t=e.channels,n=e.groups;this.channels=u(u([],a(this.channels),!1),a(null!=t?t:[]),!1),this.groups=u(u([],a(this.groups),!1),a(null!=n?n:[]),!1),this.engine.transition(Ht(this.channels.slice(0),this.groups.slice(0)))},e.prototype.leave=function(e){var t=this,n=e.channels,r=e.groups;this.dependencies.presenceState&&(null==n||n.forEach((function(e){return delete t.dependencies.presenceState[e]})),null==r||r.forEach((function(e){return delete t.dependencies.presenceState[e]}))),this.engine.transition(qt(null!=n?n:[],null!=r?r:[]))},e.prototype.leaveAll=function(){this.engine.transition(zt())},e.prototype.dispose=function(){this._unsubscribeEngine(),this.dispatcher.dispose()},e}(),ln=function(){function e(){}return e.LinearRetryPolicy=function(e){return{delay:e.delay,maximumRetry:e.maximumRetry,shouldRetry:function(e,t){var n;return 403!==(null===(n=null==e?void 0:e.status)||void 0===n?void 0:n.statusCode)&&this.maximumRetry>t},getDelay:function(e,t){var n;return 1e3*((null!==(n=t.retryAfter)&&void 0!==n?n:this.delay)+Math.random())},getGiveupReason:function(e,t){var n;return this.maximumRetry<=t?"retry attempts exhaused.":403===(null===(n=null==e?void 0:e.status)||void 0===n?void 0:n.statusCode)?"forbidden operation.":"unknown error"}}},e.ExponentialRetryPolicy=function(e){return{minimumDelay:e.minimumDelay,maximumDelay:e.maximumDelay,maximumRetry:e.maximumRetry,shouldRetry:function(e,t){var n;return 403!==(null===(n=null==e?void 0:e.status)||void 0===n?void 0:n.statusCode)&&this.maximumRetry>t},getDelay:function(e,t){var n;return 1e3*((null!==(n=t.retryAfter)&&void 0!==n?n:Math.min(Math.pow(2,e),this.maximumDelay))+Math.random())},getGiveupReason:function(e,t){var n;return this.maximumRetry<=t?"retry attempts exhaused.":403===(null===(n=null==e?void 0:e.status)||void 0===n?void 0:n.statusCode)?"forbidden operation.":"unknown error"}}},e}(),pn=function(){function e(e){var t=e.modules,n=e.listenerManager,r=e.getFileUrl;this.modules=t,this.listenerManager=n,this.getFileUrl=r,this._channelListenerMap=new Map,this._groupListenerMap=new Map,t.cryptoModule&&(this._decoder=new TextDecoder)}return e.prototype.emitEvent=function(e){var t=e.channel,o=e.publishMetaData,i=e.subscriptionMatch;if(t===i&&(i=null),e.channel.endsWith("-pnpres")){var s={channel:null,subscription:null};t&&(s.channel=t.substring(0,t.lastIndexOf("-pnpres"))),i&&(s.subscription=i.substring(0,i.lastIndexOf("-pnpres"))),s.action=e.payload.action,s.state=e.payload.data,s.timetoken=o.timetoken,s.occupancy=e.payload.occupancy,s.uuid=e.payload.uuid,s.timestamp=e.payload.timestamp,e.payload.join&&(s.join=e.payload.join),e.payload.leave&&(s.leave=e.payload.leave),e.payload.timeout&&(s.timeout=e.payload.timeout),s.actualChannel=null!=i?t:null,s.subscribedChannel=null!=i?i:t,this.listenerManager.announcePresence(s),this._announce("presence",s,s.channel,s.subscription)}else if(1===e.messageType){(s={channel:null,subscription:null}).channel=t,s.subscription=i,s.timetoken=o.timetoken,s.publisher=e.issuingClientId,e.userMetadata&&(s.userMetadata=e.userMetadata),s.message=e.payload,this.listenerManager.announceSignal(s),this._announce("signal",s,s.channel,s.subscription)}else if(2===e.messageType){if((s={channel:null,subscription:null}).channel=t,s.subscription=i,s.timetoken=o.timetoken,s.publisher=e.issuingClientId,e.userMetadata&&(s.userMetadata=e.userMetadata),s.message={event:e.payload.event,type:e.payload.type,data:e.payload.data},this.listenerManager.announceObjects(s),this._announce("objects",s,s.channel,s.subscription),"uuid"===e.payload.type){var a=this._renameChannelField(s),u=n(n({},a),{message:n(n({},a.message),{event:this._renameEvent(a.message.event),type:"user"})});this.listenerManager.announceUser(u),this._announce("user",u,s.channel,s.subscription)}else if("channel"===e.payload.type){a=this._renameChannelField(s);var c=n(n({},a),{message:n(n({},a.message),{event:this._renameEvent(a.message.event),type:"space"})});this.listenerManager.announceSpace(c),this._announce("space",c,s.channel,s.subscription)}else if("membership"===e.payload.type){var l=(a=this._renameChannelField(s)).message.data,p=l.uuid,h=l.channel,f=r(l,["uuid","channel"]);f.user=p,f.space=h;var d=n(n({},a),{message:n(n({},a.message),{event:this._renameEvent(a.message.event),data:f})});this.listenerManager.announceMembership(d),this._announce("membership",d,s.channel,s.subscription)}}else if(3===e.messageType){(s={}).channel=t,s.subscription=i,s.timetoken=o.timetoken,s.publisher=e.issuingClientId,s.data={messageTimetoken:e.payload.data.messageTimetoken,actionTimetoken:e.payload.data.actionTimetoken,type:e.payload.data.type,uuid:e.issuingClientId,value:e.payload.data.value},s.event=e.payload.event,this.listenerManager.announceMessageAction(s),this._announce("messageAction",s,s.channel,s.subscription)}else if(4===e.messageType){(s={}).channel=t,s.subscription=i,s.timetoken=o.timetoken,s.publisher=e.issuingClientId;var y=e.payload;if(this.modules.cryptoModule){var g=void 0;try{g=(m=this.modules.cryptoModule.decrypt(e.payload))instanceof ArrayBuffer?JSON.parse(this._decoder.decode(m)):m}catch(e){g=null,s.error="Error while decrypting message content: ".concat(e.message)}null!==g&&(y=g)}e.userMetadata&&(s.userMetadata=e.userMetadata),s.message=y.message,s.file={id:y.file.id,name:y.file.name,url:this.getFileUrl({id:y.file.id,name:y.file.name,channel:t})},this.listenerManager.announceFile(s),this._announce("file",s,s.channel,s.subscription)}else{if((s={channel:null,subscription:null}).channel=t,s.subscription=i,s.timetoken=o.timetoken,s.publisher=e.issuingClientId,e.userMetadata&&(s.userMetadata=e.userMetadata),this.modules.cryptoModule){g=void 0;try{var m;g=(m=this.modules.cryptoModule.decrypt(e.payload))instanceof ArrayBuffer?JSON.parse(this._decoder.decode(m)):m}catch(e){g=null,s.error="Error while decrypting message content: ".concat(e.message)}s.message=null!=g?g:e.payload}else s.message=e.payload;s.actualChannel=null!=i?t:null,s.subscribedChannel=null!=i?i:t,this.listenerManager.announceMessage(s),this._announce("message",s,s.channel,s.subscription)}},e.prototype.addListener=function(e,t,n){var r=this;t&&n?(null==t||t.forEach((function(t){r._channelListenerMap[t]?r._channelListenerMap[t].includes(e)||r._channelListenerMap[t].push(e):r._channelListenerMap[t]=[e]})),null==n||n.forEach((function(t){r._groupListenerMap[t]?r._groupListenerMap[t].includes(e)||r._groupListenerMap[t].push(e):r._groupListenerMap[t]=[e]}))):this.listenerManager.addListener(e)},e.prototype.removeListener=function(e,t,n){var r=this;t&&n?(null==t||t.forEach((function(t){var n;r._channelListenerMap[t]=null===(n=r._channelListenerMap[t])||void 0===n?void 0:n.filter((function(t){return t!==e}))})),null==n||n.forEach((function(t){var n;r._groupListenerMap[t]=null===(n=r._groupListenerMap[t])||void 0===n?void 0:n.filter((function(t){return t!==e}))}))):this.listenerManager.removeListener(e)},e.prototype.removeAllListeners=function(){this.listenerManager.removeAllListeners()},e.prototype._renameEvent=function(e){return"set"===e?"updated":"removed"},e.prototype._renameChannelField=function(e){var t=e.channel,n=r(e,["channel"]);return n.spaceId=t,n},e.prototype._announce=function(e,t,n,r){var o,i;null===(o=this._channelListenerMap[n])||void 0===o||o.forEach((function(n){return n[e]&&n[e](t)})),null===(i=this._groupListenerMap[r])||void 0===i||i.forEach((function(n){return n[e]&&n[e](t)}))},e}(),hn=function(){function e(){}return e.prototype.subscribe=function(){var e,t;this.pubnub.subscribe(n({channels:this.channelNames,channelGroups:this.groupNames},(null===(t=null===(e=this.options)||void 0===e?void 0:e.cursor)||void 0===t?void 0:t.timetoken)&&{timetoken:this.options.cursor.timetoken}))},e.prototype.unsubscribe=function(){this.pubnub.unsubscribe({channels:this.channelNames.filter((function(e){return!e.endsWith("-pnpres")})),channelGroups:this.groupNames.filter((function(e){return!e.endsWith("-pnpres")}))})},Object.defineProperty(e.prototype,"onMessage",{set:function(e){this.listener.message=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onPresence",{set:function(e){this.listener.presence=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onSignal",{set:function(e){this.listener.signal=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onObjects",{set:function(e){this.listener.objects=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onMessageAction",{set:function(e){this.listener.messageAction=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"onFile",{set:function(e){this.listener.file=e},enumerable:!1,configurable:!0}),e.prototype.addListener=function(e){this.eventEmitter.addListener(e,this.channelNames.filter((function(e){return!e.endsWith("-pnpres")})),this.groupNames.filter((function(e){return!e.endsWith("-pnpres")})))},e.prototype.removeListener=function(e){this.eventEmitter.removeListener(e,this.channelNames,this.groupNames)},Object.defineProperty(e.prototype,"channels",{get:function(){return this.channelNames.slice(0)},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"channelGroups",{get:function(){return this.groupNames.slice(0)},enumerable:!1,configurable:!0}),e}(),fn=function(e){function n(t){var n=t.channels,r=void 0===n?[]:n,o=t.channelGroups,i=void 0===o?[]:o,s=t.subscriptionOptions,c=t.eventEmitter,l=t.pubnub,p=e.call(this)||this;return p.channelNames=[],p.groupNames=[],p.subscriptionList=[],p.options=s,p.eventEmitter=c,p.pubnub=l,r.filter((function(e){return!e.endsWith("-pnpres")})).forEach((function(e){var t=p.pubnub.channel(e).subscription(p.options);p.channelNames=u(u([],a(p.channelNames),!1),a(t.channels),!1),p.subscriptionList.push(t)})),i.filter((function(e){return!e.endsWith("-pnpres")})).forEach((function(e){var t=p.pubnub.channelGroup(e).subscription(p.options);p.groupNames=u(u([],a(p.groupNames),!1),a(t.channelGroups),!1),p.subscriptionList.push(t)})),p.listener={},c.addListener(p.listener,p.channelNames.filter((function(e){return!e.endsWith("-pnpres")})),p.groupNames.filter((function(e){return!e.endsWith("-pnpres")}))),p}return t(n,e),n.prototype.addSubscription=function(e){this.subscriptionList.push(e),this.channelNames=u(u([],a(this.channelNames),!1),a(e.channels),!1),this.groupNames=u(u([],a(this.groupNames),!1),a(e.channelGroups),!1),this.eventEmitter.addListener(this.listener,e.channels,e.channelGroups)},n.prototype.removeSubscription=function(e){var t=e.channels,n=e.channelGroups;this.channelNames=this.channelNames.filter((function(e){return!t.includes(e)})),this.groupNames=this.groupNames.filter((function(e){return!n.includes(e)})),this.subscriptionList=this.subscriptionList.filter((function(t){return t!==e})),this.eventEmitter.removeListener(this.listener,t,n)},n.prototype.addSubscriptionSet=function(e){this.subscriptionList=u(u([],a(this.subscriptionList),!1),a(e.subscriptions),!1),this.channelNames=u(u([],a(this.channelNames),!1),a(e.channels),!1),this.groupNames=u(u([],a(this.groupNames),!1),a(e.channelGroups),!1),this.eventEmitter.addListener(this.listener,e.channels,e.channelGroups)},n.prototype.removeSubscriptionSet=function(e){var t=e.channels,n=e.channelGroups;this.channelNames=this.channelNames.filter((function(e){return!t.includes(e)})),this.groupNames=this.groupNames.filter((function(e){return!n.includes(e)})),this.subscriptionList=this.subscriptionList.filter((function(t){return!e.subscriptions.includes(t)})),this.eventEmitter.removeListener(this.listener,t,n)},Object.defineProperty(n.prototype,"subscriptions",{get:function(){return this.subscriptionList.slice(0)},enumerable:!1,configurable:!0}),n}(hn),dn=function(e){function r(t){var n=t.channels,r=t.channelGroups,o=t.subscriptionOptions,i=t.eventEmitter,s=t.pubnub,a=e.call(this)||this;return a.channelNames=[],a.groupNames=[],a.channelNames=n,a.groupNames=r,a.options=o,a.pubnub=s,a.eventEmitter=i,a.listener={},i.addListener(a.listener,a.channelNames.filter((function(e){return!e.endsWith("-pnpres")})),a.groupNames.filter((function(e){return!e.endsWith("-pnpres")}))),a}return t(r,e),r.prototype.addSubscription=function(e){return new fn({channels:u(u([],a(this.channelNames),!1),a(e.channels),!1),channelGroups:u(u([],a(this.groupNames),!1),a(e.channelGroups),!1),subscriptionOptions:n(n({},this.options),null==e?void 0:e.options),eventEmitter:this.eventEmitter,pubnub:this.pubnub})},r}(hn),yn=function(){function e(e,t,n){this.name=e,this.eventEmitter=t,this.pubnub=n}return e.prototype.subscription=function(e){return new dn({channels:(null==e?void 0:e.receivePresenceEvents)?[this.name,"".concat(this.name,"-pnpres")]:[this.name],channelGroups:[],subscriptionOptions:e,eventEmitter:this.eventEmitter,pubnub:this.pubnub})},e}(),gn=function(){function e(e,t,n){this.name=e,this.eventEmitter=t,this.pubnub=n}return e.prototype.subscription=function(e){return new dn({channels:[],channelGroups:(null==e?void 0:e.receivePresenceEvents)?[this.name,"".concat(this.name,"-pnpres")]:[this.name],subscriptionOptions:e,eventEmitter:this.eventEmitter,pubnub:this.pubnub})},e}(),mn=function(){function e(e,t,n){this.id=e,this.eventEmitter=t,this.pubnub=n}return e.prototype.subscription=function(e){return new dn({channels:[this.id],channelGroups:[],subscriptionOptions:e,eventEmitter:this.eventEmitter,pubnub:this.pubnub})},e}(),bn=function(){function e(e,t,n){this.id=e,this.eventEmitter=t,this.pubnub=n}return e.prototype.subscription=function(e){return new dn({channels:[this.id],channelGroups:[],subscriptionOptions:e,eventEmitter:this.eventEmitter,pubnub:this.pubnub})},e}(),vn=function(){function e(e){var t=this,r=e.networking,o=e.cbor,i=new g({setup:e});this._config=i;var c=new T({config:i}),l=e.cryptography;r.init(i);var p=new G(i,o);this._tokenManager=p;var h=new R({maximumSamplesCount:6e4});this._telemetryManager=h;var f=this._config.cryptoModule,d={config:i,networking:r,crypto:c,cryptography:l,tokenManager:p,telemetryManager:h,PubNubFile:e.PubNubFile,cryptoModule:f};this.File=e.PubNubFile,this.encryptFile=function(e,t){return 1==arguments.length&&"string"!=typeof e&&d.cryptoModule?(t=e,d.cryptoModule.encryptFile(t,this.File)):l.encryptFile(e,t,this.File)},this.decryptFile=function(e,t){return 1==arguments.length&&"string"!=typeof e&&d.cryptoModule?(t=e,d.cryptoModule.decryptFile(t,this.File)):l.decryptFile(e,t,this.File)};var y=Q.bind(this,d,Je),m=Q.bind(this,d,se),v=Q.bind(this,d,ue),_=Q.bind(this,d,le),S=Q.bind(this,d,$e),w=new L;if(this._listenerManager=w,this.iAmHere=Q.bind(this,d,ue),this.iAmAway=Q.bind(this,d,se),this.setPresenceState=Q.bind(this,d,le),this.handshake=Q.bind(this,d,Qe),this.receiveMessages=Q.bind(this,d,Xe),this._eventEmitter=new pn({modules:d,listenerManager:this._listenerManager,getFileUrl:function(e){return ve(d,e)}}),!0===i.enableEventEngine){if(i.maintainPresenceState&&(this.presenceState={},this.setState=function(e){var n,r;return null===(n=e.channels)||void 0===n||n.forEach((function(n){return t.presenceState[n]=e.state})),null===(r=e.channelGroups)||void 0===r||r.forEach((function(n){return t.presenceState[n]=e.state})),t.setPresenceState({channels:e.channels,channelGroups:e.channelGroups,state:t.presenceState})}),i.getHeartbeatInterval()){var O=new cn({heartbeat:this.iAmHere,leave:this.iAmAway,heartbeatDelay:function(){return new Promise((function(e){return setTimeout(e,1e3*d.config.getHeartbeatInterval())}))},retryDelay:function(e){return new Promise((function(t){return setTimeout(t,e)}))},config:d.config,presenceState:this.presenceState,emitStatus:function(e){w.announceStatus(e)}});this.presenceEventEngine=O,this.join=this.presenceEventEngine.join.bind(O),this.leave=this.presenceEventEngine.leave.bind(O),this.leaveAll=this.presenceEventEngine.leaveAll.bind(O)}var P=new Gt({handshake:this.handshake,receiveMessages:this.receiveMessages,delay:function(e){return new Promise((function(t){return setTimeout(t,e)}))},join:this.join,leave:this.leave,leaveAll:this.leaveAll,presenceState:this.presenceState,config:d.config,emitMessages:function(e){var n,r;try{for(var o=s(e),i=o.next();!i.done;i=o.next()){var a=i.value;t._eventEmitter.emitEvent(a)}}catch(e){n={error:e}}finally{try{i&&!i.done&&(r=o.return)&&r.call(o)}finally{if(n)throw n.error}}},emitStatus:function(e){w.announceStatus(e)}});this.subscribe=P.subscribe.bind(P),this.unsubscribe=P.unsubscribe.bind(P),this.unsubscribeAll=P.unsubscribeAll.bind(P),this.reconnect=P.reconnect.bind(P),this.disconnect=P.disconnect.bind(P),this.destroy=P.dispose.bind(P),this.getSubscribedChannels=P.getSubscribedChannels.bind(P),this.getSubscribedChannelGroups=P.getSubscribedChannelGroups.bind(P),this.eventEngine=P}else{var E=new M({timeEndpoint:y,leaveEndpoint:m,heartbeatEndpoint:v,setStateEndpoint:_,subscribeEndpoint:S,crypto:d.crypto,config:d.config,listenerManager:w,getFileUrl:function(e){return ve(d,e)},cryptoModule:d.cryptoModule,eventEmitter:this._eventEmitter});this.subscribe=E.adaptSubscribeChange.bind(E),this.unsubscribe=E.adaptUnsubscribeChange.bind(E),this.disconnect=E.disconnect.bind(E),this.reconnect=E.reconnect.bind(E),this.unsubscribeAll=E.unsubscribeAll.bind(E),this.getSubscribedChannels=E.getSubscribedChannels.bind(E),this.getSubscribedChannelGroups=E.getSubscribedChannelGroups.bind(E),this.setState=E.adaptStateChange.bind(E),this.presence=E.adaptPresenceChange.bind(E),this.destroy=function(e){E.unsubscribeAll(e),E.disconnect()}}this.addListener=this._eventEmitter.addListener.bind(this._eventEmitter),this.removeListener=this._eventEmitter.removeListener.bind(this._eventEmitter),this.removeAllListeners=this._eventEmitter.removeAllListeners.bind(this._eventEmitter),this.parseToken=p.parseToken.bind(p),this.setToken=p.setToken.bind(p),this.getToken=p.getToken.bind(p),this.channelGroups={listGroups:Q.bind(this,d,ee),listChannels:Q.bind(this,d,te),addChannels:Q.bind(this,d,X),removeChannels:Q.bind(this,d,Y),deleteGroup:Q.bind(this,d,Z)},this.push={addChannels:Q.bind(this,d,ne),removeChannels:Q.bind(this,d,re),deleteDevice:Q.bind(this,d,ie),listChannels:Q.bind(this,d,oe)},this.hereNow=Q.bind(this,d,pe),this.whereNow=Q.bind(this,d,ae),this.getState=Q.bind(this,d,ce),this.grant=Q.bind(this,d,Ue),this.grantToken=Q.bind(this,d,Le),this.audit=Q.bind(this,d,xe),this.revokeToken=Q.bind(this,d,Ge),this.publish=Q.bind(this,d,Be),this.fire=function(e,n){return e.replicate=!1,e.storeInHistory=!1,t.publish(e,n)},this.signal=Q.bind(this,d,He),this.history=Q.bind(this,d,qe),this.deleteMessages=Q.bind(this,d,ze),this.messageCounts=Q.bind(this,d,Ve),this.fetchMessages=Q.bind(this,d,We),this.addMessageAction=Q.bind(this,d,he),this.removeMessageAction=Q.bind(this,d,fe),this.getMessageActions=Q.bind(this,d,de),this.listFiles=Q.bind(this,d,ye);var A=Q.bind(this,d,ge);this.publishFile=Q.bind(this,d,me),this.sendFile=be({generateUploadUrl:A,publishFile:this.publishFile,modules:d}),this.getFileUrl=function(e){return ve(d,e)},this.downloadFile=Q.bind(this,d,_e),this.deleteFile=Q.bind(this,d,Se),this.channel=function(e){return new yn(e,t._eventEmitter,t)},this.channelGroup=function(e){return new gn(e,t._eventEmitter,t)},this.channelMetadata=function(e){return new mn(e,t._eventEmitter,t)},this.userMetadata=function(e){return new bn(e,t._eventEmitter,t)},this.subscriptionSet=function(e){return new fn({channels:e.channels,channelGroups:e.channelGroups,subscriptionOptions:e.subscriptionOptions,eventEmitter:t._eventEmitter,pubnub:t})},this.objects={getAllUUIDMetadata:Q.bind(this,d,we),getUUIDMetadata:Q.bind(this,d,Oe),setUUIDMetadata:Q.bind(this,d,Pe),removeUUIDMetadata:Q.bind(this,d,Ee),getAllChannelMetadata:Q.bind(this,d,Ae),getChannelMetadata:Q.bind(this,d,Te),setChannelMetadata:Q.bind(this,d,Ne),removeChannelMetadata:Q.bind(this,d,Ce),getChannelMembers:Q.bind(this,d,ke),setChannelMembers:function(e){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];return Q.call.apply(Q,u([t,d,Me,n({type:"set"},e)],a(r),!1))},removeChannelMembers:function(e){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];return Q.call.apply(Q,u([t,d,Me,n({type:"delete"},e)],a(r),!1))},getMemberships:Q.bind(this,d,je),setMemberships:function(e){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];return Q.call.apply(Q,u([t,d,Re,n({type:"set"},e)],a(r),!1))},removeMemberships:function(e){for(var r=[],o=1;o<arguments.length;o++)r[o-1]=arguments[o];return Q.call.apply(Q,u([t,d,Re,n({type:"delete"},e)],a(r),!1))}},this.createUser=function(e){return t.objects.setUUIDMetadata({uuid:e.userId,data:e.data,include:e.include})},this.updateUser=this.createUser,this.removeUser=function(e){return t.objects.removeUUIDMetadata({uuid:null==e?void 0:e.userId})},this.fetchUser=function(e){return t.objects.getUUIDMetadata({uuid:null==e?void 0:e.userId,include:null==e?void 0:e.include})},this.fetchUsers=this.objects.getAllUUIDMetadata,this.createSpace=function(e){return t.objects.setChannelMetadata({channel:e.spaceId,data:e.data,include:e.include})},this.updateSpace=this.createSpace,this.removeSpace=function(e){return t.objects.removeChannelMetadata({channel:e.spaceId})},this.fetchSpace=function(e){return t.objects.getChannelMetadata({channel:e.spaceId,include:e.include})},this.fetchSpaces=this.objects.getAllChannelMetadata,this.addMemberships=function(e){var n,r;return"string"==typeof e.spaceId?t.objects.setChannelMembers({channel:e.spaceId,uuids:null===(n=e.users)||void 0===n?void 0:n.map((function(e){return"string"==typeof e?e:{id:e.userId,custom:e.custom,status:e.status}})),limit:0}):t.objects.setMemberships({uuid:e.userId,channels:null===(r=e.spaces)||void 0===r?void 0:r.map((function(e){return"string"==typeof e?e:{id:e.spaceId,custom:e.custom,status:e.status}})),limit:0})},this.updateMemberships=this.addMemberships,this.removeMemberships=function(e){return"string"==typeof e.spaceId?t.objects.removeChannelMembers({channel:e.spaceId,uuids:e.userIds,limit:0}):t.objects.removeMemberships({uuid:e.userId,channels:e.spaceIds,limit:0})},this.fetchMemberships=function(e){return"string"==typeof e.spaceId?t.objects.getChannelMembers({channel:e.spaceId,filter:e.filter,limit:e.limit,page:e.page,include:{customFields:e.include.customFields,UUIDFields:e.include.userFields,customUUIDFields:e.include.customUserFields,statusField:e.include.statusField,UUIDStatusField:e.include.userStatusField,UUIDTypeField:e.include.userTypeField,totalCount:e.include.totalCount},sort:null!=e.sort?Object.fromEntries(Object.entries(e.sort).map((function(e){var t=a(e,2),n=t[0],r=t[1];return[n.replace("user","uuid"),r]}))):null}).then((function(e){var t;return e.data=null===(t=e.data)||void 0===t?void 0:t.map((function(e){return{user:e.uuid,custom:e.custom,updated:e.updated,eTag:e.eTag}})),e})):t.objects.getMemberships({uuid:e.userId,filter:e.filter,limit:e.limit,page:e.page,include:{customFields:e.include.customFields,channelFields:e.include.spaceFields,customChannelFields:e.include.customSpaceFields,statusField:e.include.statusField,channelStatusField:e.include.spaceStatusField,channelTypeField:e.include.spaceTypeField,totalCount:e.include.totalCount},sort:null!=e.sort?Object.fromEntries(Object.entries(e.sort).map((function(e){var t=a(e,2),n=t[0],r=t[1];return[n.replace("space","channel"),r]}))):null}).then((function(e){var t;return e.data=null===(t=e.data)||void 0===t?void 0:t.map((function(e){return{space:e.channel,custom:e.custom,updated:e.updated,eTag:e.eTag}})),e}))},this.time=y,this.stop=this.destroy,this.encrypt=function(e,t){if(void 0===t&&d.cryptoModule){var n=d.cryptoModule.encrypt(e);return"string"==typeof n?n:b(n)}return c.encrypt(e,t)},this.decrypt=function(e,t){if(void 0===t&&f){var n=d.cryptoModule.decrypt(e);return n instanceof ArrayBuffer?JSON.parse((new TextDecoder).decode(n)):n}return c.decrypt(e,t)},this.getAuthKey=d.config.getAuthKey.bind(d.config),this.setAuthKey=d.config.setAuthKey.bind(d.config),this.getUUID=d.config.getUUID.bind(d.config),this.setUUID=d.config.setUUID.bind(d.config),this.getUserId=d.config.getUserId.bind(d.config),this.setUserId=d.config.setUserId.bind(d.config),this.getFilterExpression=d.config.getFilterExpression.bind(d.config),this.setFilterExpression=d.config.setFilterExpression.bind(d.config),this.setCipherKey=function(t){return d.config.setCipherKey(t,e,d)},this.setHeartbeatInterval=d.config.setHeartbeatInterval.bind(d.config),r.hasModule("proxy")&&(this.setProxy=function(e){d.config.setProxy(e),t.reconnect()})}return e.prototype.getVersion=function(){return this._config.getVersion()},e.prototype._addPnsdkSuffix=function(e,t){this._config._addPnsdkSuffix(e,t)},e.prototype.networkDownDetected=function(){this._listenerManager.announceNetworkDown(),this._config.restore?this.disconnect():this.destroy(!0)},e.prototype.networkUpDetected=function(){this._listenerManager.announceNetworkUp(),this.reconnect()},e.notificationPayload=function(e,t){return new F(e,t)},e.generateUUID=function(){return y()},e.OPERATIONS=j,e.CATEGORIES=k,e.LinearRetryPolicy=ln.LinearRetryPolicy,e.ExponentialRetryPolicy=ln.ExponentialRetryPolicy,e}(),_n=function(){function e(e){var t=this;this._modules={},Object.keys(e).forEach((function(n){t._modules[n]=e[n].bind(t)}))}return e.prototype.init=function(e){this._config=e,Array.isArray(this._config.origin)?this._currentSubDomain=Math.floor(Math.random()*this._config.origin.length):this._currentSubDomain=0,this._coreParams={},this.shiftStandardOrigin()},e.prototype.nextOrigin=function(){var e=this._config.secure?"https://":"http://";if("string"==typeof this._config.origin)return"".concat(e).concat(this._config.origin);this._currentSubDomain+=1,this._currentSubDomain>=this._config.origin.length&&(this._currentSubDomain=0);var t=this._config.origin[this._currentSubDomain];return"".concat(e).concat(t)},e.prototype.hasModule=function(e){return e in this._modules},e.prototype.shiftStandardOrigin=function(){return this._standardOrigin=this.nextOrigin(),this._standardOrigin},e.prototype.getStandardOrigin=function(){return this._standardOrigin},e.prototype.POSTFILE=function(e,t,n){return this._modules.postfile(e,t,n)},e.prototype.GETFILE=function(e,t,n){return this._modules.getfile(e,t,n)},e.prototype.POST=function(e,t,n,r){return this._modules.post(e,t,n,r)},e.prototype.PATCH=function(e,t,n,r){return this._modules.patch(e,t,n,r)},e.prototype.GET=function(e,t,n){return this._modules.get(e,t,n)},e.prototype.DELETE=function(e,t,n){return this._modules.del(e,t,n)},e.prototype._detectErrorCategory=function(e){if("ENOTFOUND"===e.code)return k.PNNetworkIssuesCategory;if("ECONNREFUSED"===e.code)return k.PNNetworkIssuesCategory;if("ECONNRESET"===e.code)return k.PNNetworkIssuesCategory;if("EAI_AGAIN"===e.code)return k.PNNetworkIssuesCategory;if(0===e.status||e.hasOwnProperty("status")&&void 0===e.status)return k.PNNetworkIssuesCategory;if(e.timeout)return k.PNTimeoutCategory;if("ETIMEDOUT"===e.code)return k.PNNetworkIssuesCategory;if(e.response){if(e.response.badRequest)return k.PNBadRequestCategory;if(e.response.forbidden)return k.PNAccessDeniedCategory}return k.PNUnknownCategory},e}();function Sn(e){var t=function(e){return e&&"object"==typeof e&&e.constructor===Object};if(!t(e))return e;var n={};return Object.keys(e).forEach((function(r){var o=function(e){return"string"==typeof e||e instanceof String}(r),i=r,s=e[r];Array.isArray(r)||o&&r.indexOf(",")>=0?i=(o?r.split(","):r).reduce((function(e,t){return e+=String.fromCharCode(t)}),""):(function(e){return"number"==typeof e&&isFinite(e)}(r)||o&&!isNaN(r))&&(i=String.fromCharCode(o?parseInt(r,10):10));n[i]=t(s)?Sn(s):s})),n}var wn=function(){function e(e,t){this._base64ToBinary=t,this._decode=e}return e.prototype.decodeToken=function(e){var t="";e.length%4==3?t="=":e.length%4==2&&(t="==");var n=e.replace(/-/gi,"+").replace(/_/gi,"/")+t,r=this._decode(this._base64ToBinary(n));if("object"==typeof r)return r},e}(),On={exports:{}},Pn={exports:{}};!function(e){function t(e){if(e)return function(e){for(var n in t.prototype)e[n]=t.prototype[n];return e}(e)}e.exports=t,t.prototype.on=t.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},t.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var o=0;o<r.length;o++)if((n=r[o])===t||n.fn===t){r.splice(o,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},t.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var o=(n=n.slice(0)).length;r<o;++r)n[r].apply(this,t)}return this},t.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},t.prototype.hasListeners=function(e){return!!this.listeners(e).length}}(Pn);var En=Mn;Mn.default=Mn,Mn.stable=Un,Mn.stableStringify=Un;var An="[...]",Tn="[Circular]",Nn=[],Cn=[];function kn(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function Mn(e,t,n,r){var o;void 0===r&&(r=kn()),Rn(e,"",0,[],void 0,0,r);try{o=0===Cn.length?JSON.stringify(e,t,n):JSON.stringify(e,Dn(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==Nn.length;){var i=Nn.pop();4===i.length?Object.defineProperty(i[0],i[1],i[3]):i[0][i[1]]=i[2]}}return o}function jn(e,t,n,r){var o=Object.getOwnPropertyDescriptor(r,n);void 0!==o.get?o.configurable?(Object.defineProperty(r,n,{value:e}),Nn.push([r,n,t,o])):Cn.push([t,n,e]):(r[n]=e,Nn.push([r,n,t]))}function Rn(e,t,n,r,o,i,s){var a;if(i+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void jn(Tn,e,t,o);if(void 0!==s.depthLimit&&i>s.depthLimit)return void jn(An,e,t,o);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void jn(An,e,t,o);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)Rn(e[a],a,a,r,e,i,s);else{var u=Object.keys(e);for(a=0;a<u.length;a++){var c=u[a];Rn(e[c],c,a,r,e,i,s)}}r.pop()}}function xn(e,t){return e<t?-1:e>t?1:0}function Un(e,t,n,r){void 0===r&&(r=kn());var o,i=In(e,"",0,[],void 0,0,r)||e;try{o=0===Cn.length?JSON.stringify(i,t,n):JSON.stringify(i,Dn(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==Nn.length;){var s=Nn.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return o}function In(e,t,n,r,o,i,s){var a;if(i+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void jn(Tn,e,t,o);try{if("function"==typeof e.toJSON)return}catch(e){return}if(void 0!==s.depthLimit&&i>s.depthLimit)return void jn(An,e,t,o);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void jn(An,e,t,o);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)In(e[a],a,a,r,e,i,s);else{var u={},c=Object.keys(e).sort(xn);for(a=0;a<c.length;a++){var l=c[a];In(e[l],l,a,r,e,i,s),u[l]=e[l]}if(void 0===o)return u;Nn.push([o,t,e]),o[t]=u}r.pop()}}function Dn(e){return e=void 0!==e?e:function(e,t){return t},function(t,n){if(Cn.length>0)for(var r=0;r<Cn.length;r++){var o=Cn[r];if(o[1]===t&&o[0]===n){n=o[2],Cn.splice(r,1);break}}return e.call(this,t,n)}}var Fn,Ln="undefined"!=typeof Symbol&&Symbol,Gn=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),n=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var r=Object.getOwnPropertySymbols(e);if(1!==r.length||r[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0},Kn="Function.prototype.bind called on incompatible ",Bn=Array.prototype.slice,Hn=Object.prototype.toString,qn="[object Function]",zn=function(e){var t=this;if("function"!=typeof t||Hn.call(t)!==qn)throw new TypeError(Kn+t);for(var n,r=Bn.call(arguments,1),o=function(){if(this instanceof n){var o=t.apply(this,r.concat(Bn.call(arguments)));return Object(o)===o?o:this}return t.apply(e,r.concat(Bn.call(arguments)))},i=Math.max(0,t.length-r.length),s=[],a=0;a<i;a++)s.push("$"+a);if(n=Function("binder","return function ("+s.join(",")+"){ return binder.apply(this,arguments); }")(o),t.prototype){var u=function(){};u.prototype=t.prototype,n.prototype=new u,u.prototype=null}return n},Vn=Function.prototype.bind||zn,Wn=Vn.call(Function.call,Object.prototype.hasOwnProperty),Jn=SyntaxError,$n=Function,Qn=TypeError,Xn=function(e){try{return $n('"use strict"; return ('+e+").constructor;")()}catch(e){}},Yn=Object.getOwnPropertyDescriptor;if(Yn)try{Yn({},"")}catch(e){Yn=null}var Zn=function(){throw new Qn},er=Yn?function(){try{return Zn}catch(e){try{return Yn(arguments,"callee").get}catch(e){return Zn}}}():Zn,tr="function"==typeof Ln&&"function"==typeof Symbol&&"symbol"==typeof Ln("foo")&&"symbol"==typeof Symbol("bar")&&Gn(),nr=Object.getPrototypeOf||function(e){return e.__proto__},rr={},or="undefined"==typeof Uint8Array?Fn:nr(Uint8Array),ir={"%AggregateError%":"undefined"==typeof AggregateError?Fn:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?Fn:ArrayBuffer,"%ArrayIteratorPrototype%":tr?nr([][Symbol.iterator]()):Fn,"%AsyncFromSyncIteratorPrototype%":Fn,"%AsyncFunction%":rr,"%AsyncGenerator%":rr,"%AsyncGeneratorFunction%":rr,"%AsyncIteratorPrototype%":rr,"%Atomics%":"undefined"==typeof Atomics?Fn:Atomics,"%BigInt%":"undefined"==typeof BigInt?Fn:BigInt,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?Fn:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?Fn:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?Fn:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?Fn:FinalizationRegistry,"%Function%":$n,"%GeneratorFunction%":rr,"%Int8Array%":"undefined"==typeof Int8Array?Fn:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?Fn:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?Fn:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":tr?nr(nr([][Symbol.iterator]())):Fn,"%JSON%":"object"==typeof JSON?JSON:Fn,"%Map%":"undefined"==typeof Map?Fn:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&tr?nr((new Map)[Symbol.iterator]()):Fn,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?Fn:Promise,"%Proxy%":"undefined"==typeof Proxy?Fn:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?Fn:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?Fn:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&tr?nr((new Set)[Symbol.iterator]()):Fn,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?Fn:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":tr?nr(""[Symbol.iterator]()):Fn,"%Symbol%":tr?Symbol:Fn,"%SyntaxError%":Jn,"%ThrowTypeError%":er,"%TypedArray%":or,"%TypeError%":Qn,"%Uint8Array%":"undefined"==typeof Uint8Array?Fn:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?Fn:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?Fn:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?Fn:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?Fn:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?Fn:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?Fn:WeakSet},sr=function e(t){var n;if("%AsyncFunction%"===t)n=Xn("async function () {}");else if("%GeneratorFunction%"===t)n=Xn("function* () {}");else if("%AsyncGeneratorFunction%"===t)n=Xn("async function* () {}");else if("%AsyncGenerator%"===t){var r=e("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&(n=nr(o.prototype))}return ir[t]=n,n},ar={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},ur=Vn,cr=Wn,lr=ur.call(Function.call,Array.prototype.concat),pr=ur.call(Function.apply,Array.prototype.splice),hr=ur.call(Function.call,String.prototype.replace),fr=ur.call(Function.call,String.prototype.slice),dr=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,yr=/\\(\\)?/g,gr=function(e){var t=fr(e,0,1),n=fr(e,-1);if("%"===t&&"%"!==n)throw new Jn("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==t)throw new Jn("invalid intrinsic syntax, expected opening `%`");var r=[];return hr(e,dr,(function(e,t,n,o){r[r.length]=n?hr(o,yr,"$1"):t||e})),r},mr=function(e,t){var n,r=e;if(cr(ar,r)&&(r="%"+(n=ar[r])[0]+"%"),cr(ir,r)){var o=ir[r];if(o===rr&&(o=sr(r)),void 0===o&&!t)throw new Qn("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new Jn("intrinsic "+e+" does not exist!")},br=function(e,t){if("string"!=typeof e||0===e.length)throw new Qn("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new Qn('"allowMissing" argument must be a boolean');var n=gr(e),r=n.length>0?n[0]:"",o=mr("%"+r+"%",t),i=o.name,s=o.value,a=!1,u=o.alias;u&&(r=u[0],pr(n,lr([0,1],u)));for(var c=1,l=!0;c<n.length;c+=1){var p=n[c],h=fr(p,0,1),f=fr(p,-1);if(('"'===h||"'"===h||"`"===h||'"'===f||"'"===f||"`"===f)&&h!==f)throw new Jn("property names with quotes must have matching quotes");if("constructor"!==p&&l||(a=!0),cr(ir,i="%"+(r+="."+p)+"%"))s=ir[i];else if(null!=s){if(!(p in s)){if(!t)throw new Qn("base intrinsic for "+e+" exists, but the property is not available.");return}if(Yn&&c+1>=n.length){var d=Yn(s,p);s=(l=!!d)&&"get"in d&&!("originalValue"in d.get)?d.get:s[p]}else l=cr(s,p),s=s[p];l&&!a&&(ir[i]=s)}}return s},vr={exports:{}};!function(e){var t=Vn,n=br,r=n("%Function.prototype.apply%"),o=n("%Function.prototype.call%"),i=n("%Reflect.apply%",!0)||t.call(o,r),s=n("%Object.getOwnPropertyDescriptor%",!0),a=n("%Object.defineProperty%",!0),u=n("%Math.max%");if(a)try{a({},"a",{value:1})}catch(e){a=null}e.exports=function(e){var n=i(t,o,arguments);if(s&&a){var r=s(n,"length");r.configurable&&a(n,"length",{value:1+u(0,e.length-(arguments.length-1))})}return n};var c=function(){return i(t,r,arguments)};a?a(e.exports,"apply",{value:c}):e.exports.apply=c}(vr);var _r=br,Sr=vr.exports,wr=Sr(_r("String.prototype.indexOf")),Or=l(Object.freeze({__proto__:null,default:{}})),Pr="function"==typeof Map&&Map.prototype,Er=Object.getOwnPropertyDescriptor&&Pr?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Ar=Pr&&Er&&"function"==typeof Er.get?Er.get:null,Tr=Pr&&Map.prototype.forEach,Nr="function"==typeof Set&&Set.prototype,Cr=Object.getOwnPropertyDescriptor&&Nr?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,kr=Nr&&Cr&&"function"==typeof Cr.get?Cr.get:null,Mr=Nr&&Set.prototype.forEach,jr="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,Rr="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,xr="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,Ur=Boolean.prototype.valueOf,Ir=Object.prototype.toString,Dr=Function.prototype.toString,Fr=String.prototype.match,Lr=String.prototype.slice,Gr=String.prototype.replace,Kr=String.prototype.toUpperCase,Br=String.prototype.toLowerCase,Hr=RegExp.prototype.test,qr=Array.prototype.concat,zr=Array.prototype.join,Vr=Array.prototype.slice,Wr=Math.floor,Jr="function"==typeof BigInt?BigInt.prototype.valueOf:null,$r=Object.getOwnPropertySymbols,Qr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,Xr="function"==typeof Symbol&&"object"==typeof Symbol.iterator,Yr="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Xr||"symbol")?Symbol.toStringTag:null,Zr=Object.prototype.propertyIsEnumerable,eo=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function to(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||Hr.call(/e/,t))return t;var n=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var r=e<0?-Wr(-e):Wr(e);if(r!==e){var o=String(r),i=Lr.call(t,o.length+1);return Gr.call(o,n,"$&_")+"."+Gr.call(Gr.call(i,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Gr.call(t,n,"$&_")}var no=Or,ro=no.custom,oo=co(ro)?ro:null;function io(e,t,n){var r="double"===(n.quoteStyle||t)?'"':"'";return r+e+r}function so(e){return Gr.call(String(e),/"/g,"&quot;")}function ao(e){return!("[object Array]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}function uo(e){return!("[object RegExp]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}function co(e){if(Xr)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!Qr)return!1;try{return Qr.call(e),!0}catch(e){}return!1}var lo=Object.prototype.hasOwnProperty||function(e){return e in this};function po(e,t){return lo.call(e,t)}function ho(e){return Ir.call(e)}function fo(e,t){if(e.indexOf)return e.indexOf(t);for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}function yo(e,t){if(e.length>t.maxStringLength){var n=e.length-t.maxStringLength,r="... "+n+" more character"+(n>1?"s":"");return yo(Lr.call(e,0,t.maxStringLength),t)+r}return io(Gr.call(Gr.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,go),"single",t)}function go(e){var t=e.charCodeAt(0),n={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return n?"\\"+n:"\\x"+(t<16?"0":"")+Kr.call(t.toString(16))}function mo(e){return"Object("+e+")"}function bo(e){return e+" { ? }"}function vo(e,t,n,r){return e+" ("+t+") {"+(r?_o(n,r):zr.call(n,", "))+"}"}function _o(e,t){if(0===e.length)return"";var n="\n"+t.prev+t.base;return n+zr.call(e,","+n)+"\n"+t.prev}function So(e,t){var n=ao(e),r=[];if(n){r.length=e.length;for(var o=0;o<e.length;o++)r[o]=po(e,o)?t(e[o],e):""}var i,s="function"==typeof $r?$r(e):[];if(Xr){i={};for(var a=0;a<s.length;a++)i["$"+s[a]]=s[a]}for(var u in e)po(e,u)&&(n&&String(Number(u))===u&&u<e.length||Xr&&i["$"+u]instanceof Symbol||(Hr.call(/[^\w$]/,u)?r.push(t(u,e)+": "+t(e[u],e)):r.push(u+": "+t(e[u],e))));if("function"==typeof $r)for(var c=0;c<s.length;c++)Zr.call(e,s[c])&&r.push("["+t(s[c])+"]: "+t(e[s[c]],e));return r}var wo=br,Oo=function(e,t){var n=_r(e,!!t);return"function"==typeof n&&wr(e,".prototype.")>-1?Sr(n):n},Po=function e(t,n,r,o){var i=n||{};if(po(i,"quoteStyle")&&"single"!==i.quoteStyle&&"double"!==i.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(po(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var s=!po(i,"customInspect")||i.customInspect;if("boolean"!=typeof s&&"symbol"!==s)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(po(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(po(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var a=i.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return yo(t,i);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var u=String(t);return a?to(t,u):u}if("bigint"==typeof t){var l=String(t)+"n";return a?to(t,l):l}var p=void 0===i.depth?5:i.depth;if(void 0===r&&(r=0),r>=p&&p>0&&"object"==typeof t)return ao(t)?"[Array]":"[Object]";var h=function(e,t){var n;if("\t"===e.indent)n="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;n=zr.call(Array(e.indent+1)," ")}return{base:n,prev:zr.call(Array(t+1),n)}}(i,r);if(void 0===o)o=[];else if(fo(o,t)>=0)return"[Circular]";function f(t,n,s){if(n&&(o=Vr.call(o)).push(n),s){var a={depth:i.depth};return po(i,"quoteStyle")&&(a.quoteStyle=i.quoteStyle),e(t,a,r+1,o)}return e(t,i,r+1,o)}if("function"==typeof t&&!uo(t)){var d=function(e){if(e.name)return e.name;var t=Fr.call(Dr.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),y=So(t,f);return"[Function"+(d?": "+d:" (anonymous)")+"]"+(y.length>0?" { "+zr.call(y,", ")+" }":"")}if(co(t)){var g=Xr?Gr.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):Qr.call(t);return"object"!=typeof t||Xr?g:mo(g)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var m="<"+Br.call(String(t.nodeName)),b=t.attributes||[],v=0;v<b.length;v++)m+=" "+b[v].name+"="+io(so(b[v].value),"double",i);return m+=">",t.childNodes&&t.childNodes.length&&(m+="..."),m+="</"+Br.call(String(t.nodeName))+">"}if(ao(t)){if(0===t.length)return"[]";var _=So(t,f);return h&&!function(e){for(var t=0;t<e.length;t++)if(fo(e[t],"\n")>=0)return!1;return!0}(_)?"["+_o(_,h)+"]":"[ "+zr.call(_,", ")+" ]"}if(function(e){return!("[object Error]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}(t)){var S=So(t,f);return"cause"in Error.prototype||!("cause"in t)||Zr.call(t,"cause")?0===S.length?"["+String(t)+"]":"{ ["+String(t)+"] "+zr.call(S,", ")+" }":"{ ["+String(t)+"] "+zr.call(qr.call("[cause]: "+f(t.cause),S),", ")+" }"}if("object"==typeof t&&s){if(oo&&"function"==typeof t[oo]&&no)return no(t,{depth:p-r});if("symbol"!==s&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!Ar||!e||"object"!=typeof e)return!1;try{Ar.call(e);try{kr.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var w=[];return Tr&&Tr.call(t,(function(e,n){w.push(f(n,t,!0)+" => "+f(e,t))})),vo("Map",Ar.call(t),w,h)}if(function(e){if(!kr||!e||"object"!=typeof e)return!1;try{kr.call(e);try{Ar.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var O=[];return Mr&&Mr.call(t,(function(e){O.push(f(e,t))})),vo("Set",kr.call(t),O,h)}if(function(e){if(!jr||!e||"object"!=typeof e)return!1;try{jr.call(e,jr);try{Rr.call(e,Rr)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return bo("WeakMap");if(function(e){if(!Rr||!e||"object"!=typeof e)return!1;try{Rr.call(e,Rr);try{jr.call(e,jr)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return bo("WeakSet");if(function(e){if(!xr||!e||"object"!=typeof e)return!1;try{return xr.call(e),!0}catch(e){}return!1}(t))return bo("WeakRef");if(function(e){return!("[object Number]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}(t))return mo(f(Number(t)));if(function(e){if(!e||"object"!=typeof e||!Jr)return!1;try{return Jr.call(e),!0}catch(e){}return!1}(t))return mo(f(Jr.call(t)));if(function(e){return!("[object Boolean]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}(t))return mo(Ur.call(t));if(function(e){return!("[object String]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}(t))return mo(f(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===c)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==ho(e)||Yr&&"object"==typeof e&&Yr in e)}(t)&&!uo(t)){var P=So(t,f),E=eo?eo(t)===Object.prototype:t instanceof Object||t.constructor===Object,A=t instanceof Object?"":"null prototype",T=!E&&Yr&&Object(t)===t&&Yr in t?Lr.call(ho(t),8,-1):A?"Object":"",N=(E||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(T||A?"["+zr.call(qr.call([],T||[],A||[]),": ")+"] ":"");return 0===P.length?N+"{}":h?N+"{"+_o(P,h)+"}":N+"{ "+zr.call(P,", ")+" }"}return String(t)},Eo=wo("%TypeError%"),Ao=wo("%WeakMap%",!0),To=wo("%Map%",!0),No=Oo("WeakMap.prototype.get",!0),Co=Oo("WeakMap.prototype.set",!0),ko=Oo("WeakMap.prototype.has",!0),Mo=Oo("Map.prototype.get",!0),jo=Oo("Map.prototype.set",!0),Ro=Oo("Map.prototype.has",!0),xo=function(e,t){for(var n,r=e;null!==(n=r.next);r=n)if(n.key===t)return r.next=n.next,n.next=e.next,e.next=n,n},Uo=String.prototype.replace,Io=/%20/g,Do="RFC3986",Fo={default:Do,formatters:{RFC1738:function(e){return Uo.call(e,Io,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:Do},Lo=Fo,Go=Object.prototype.hasOwnProperty,Ko=Array.isArray,Bo=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),Ho=function(e,t){for(var n=t&&t.plainObjects?Object.create(null):{},r=0;r<e.length;++r)void 0!==e[r]&&(n[r]=e[r]);return n},qo={arrayToObject:Ho,assign:function(e,t){return Object.keys(t).reduce((function(e,n){return e[n]=t[n],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],n=[],r=0;r<t.length;++r)for(var o=t[r],i=o.obj[o.prop],s=Object.keys(i),a=0;a<s.length;++a){var u=s[a],c=i[u];"object"==typeof c&&null!==c&&-1===n.indexOf(c)&&(t.push({obj:i,prop:u}),n.push(c))}return function(e){for(;e.length>1;){var t=e.pop(),n=t.obj[t.prop];if(Ko(n)){for(var r=[],o=0;o<n.length;++o)void 0!==n[o]&&r.push(n[o]);t.obj[t.prop]=r}}}(t),e},decode:function(e,t,n){var r=e.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(e){return r}},encode:function(e,t,n,r,o){if(0===e.length)return e;var i=e;if("symbol"==typeof e?i=Symbol.prototype.toString.call(e):"string"!=typeof e&&(i=String(e)),"iso-8859-1"===n)return escape(i).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var s="",a=0;a<i.length;++a){var u=i.charCodeAt(a);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||o===Lo.RFC1738&&(40===u||41===u)?s+=i.charAt(a):u<128?s+=Bo[u]:u<2048?s+=Bo[192|u>>6]+Bo[128|63&u]:u<55296||u>=57344?s+=Bo[224|u>>12]+Bo[128|u>>6&63]+Bo[128|63&u]:(a+=1,u=65536+((1023&u)<<10|1023&i.charCodeAt(a)),s+=Bo[240|u>>18]+Bo[128|u>>12&63]+Bo[128|u>>6&63]+Bo[128|63&u])}return s},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(Ko(e)){for(var n=[],r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)},merge:function e(t,n,r){if(!n)return t;if("object"!=typeof n){if(Ko(t))t.push(n);else{if(!t||"object"!=typeof t)return[t,n];(r&&(r.plainObjects||r.allowPrototypes)||!Go.call(Object.prototype,n))&&(t[n]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(n);var o=t;return Ko(t)&&!Ko(n)&&(o=Ho(t,r)),Ko(t)&&Ko(n)?(n.forEach((function(n,o){if(Go.call(t,o)){var i=t[o];i&&"object"==typeof i&&n&&"object"==typeof n?t[o]=e(i,n,r):t.push(n)}else t[o]=n})),t):Object.keys(n).reduce((function(t,o){var i=n[o];return Go.call(t,o)?t[o]=e(t[o],i,r):t[o]=i,t}),o)}},zo=function(){var e,t,n,r={assert:function(e){if(!r.has(e))throw new Eo("Side channel does not contain "+Po(e))},get:function(r){if(Ao&&r&&("object"==typeof r||"function"==typeof r)){if(e)return No(e,r)}else if(To){if(t)return Mo(t,r)}else if(n)return function(e,t){var n=xo(e,t);return n&&n.value}(n,r)},has:function(r){if(Ao&&r&&("object"==typeof r||"function"==typeof r)){if(e)return ko(e,r)}else if(To){if(t)return Ro(t,r)}else if(n)return function(e,t){return!!xo(e,t)}(n,r);return!1},set:function(r,o){Ao&&r&&("object"==typeof r||"function"==typeof r)?(e||(e=new Ao),Co(e,r,o)):To?(t||(t=new To),jo(t,r,o)):(n||(n={key:{},next:null}),function(e,t,n){var r=xo(e,t);r?r.value=n:e.next={key:t,next:e.next,value:n}}(n,r,o))}};return r},Vo=qo,Wo=Fo,Jo=Object.prototype.hasOwnProperty,$o={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},Qo=Array.isArray,Xo=Array.prototype.push,Yo=function(e,t){Xo.apply(e,Qo(t)?t:[t])},Zo=Date.prototype.toISOString,ei=Wo.default,ti={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:Vo.encode,encodeValuesOnly:!1,format:ei,formatter:Wo.formatters[ei],indices:!1,serializeDate:function(e){return Zo.call(e)},skipNulls:!1,strictNullHandling:!1},ni={},ri=function e(t,n,r,o,i,s,a,u,c,l,p,h,f,d,y,g){for(var m,b=t,v=g,_=0,S=!1;void 0!==(v=v.get(ni))&&!S;){var w=v.get(t);if(_+=1,void 0!==w){if(w===_)throw new RangeError("Cyclic object value");S=!0}void 0===v.get(ni)&&(_=0)}if("function"==typeof u?b=u(n,b):b instanceof Date?b=p(b):"comma"===r&&Qo(b)&&(b=Vo.maybeMap(b,(function(e){return e instanceof Date?p(e):e}))),null===b){if(i)return a&&!d?a(n,ti.encoder,y,"key",h):n;b=""}if("string"==typeof(m=b)||"number"==typeof m||"boolean"==typeof m||"symbol"==typeof m||"bigint"==typeof m||Vo.isBuffer(b))return a?[f(d?n:a(n,ti.encoder,y,"key",h))+"="+f(a(b,ti.encoder,y,"value",h))]:[f(n)+"="+f(String(b))];var O,P=[];if(void 0===b)return P;if("comma"===r&&Qo(b))d&&a&&(b=Vo.maybeMap(b,a)),O=[{value:b.length>0?b.join(",")||null:void 0}];else if(Qo(u))O=u;else{var E=Object.keys(b);O=c?E.sort(c):E}for(var A=o&&Qo(b)&&1===b.length?n+"[]":n,T=0;T<O.length;++T){var N=O[T],C="object"==typeof N&&void 0!==N.value?N.value:b[N];if(!s||null!==C){var k=Qo(b)?"function"==typeof r?r(A,N):A:A+(l?"."+N:"["+N+"]");g.set(t,_);var M=zo();M.set(ni,g),Yo(P,e(C,k,r,o,i,s,"comma"===r&&d&&Qo(b)?null:a,u,c,l,p,h,f,d,y,M))}}return P},oi=qo,ii=Object.prototype.hasOwnProperty,si=Array.isArray,ai={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:oi.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},ui=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},ci=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},li=function(e,t,n,r){if(e){var o=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,i=/(\[[^[\]]*])/g,s=n.depth>0&&/(\[[^[\]]*])/.exec(o),a=s?o.slice(0,s.index):o,u=[];if(a){if(!n.plainObjects&&ii.call(Object.prototype,a)&&!n.allowPrototypes)return;u.push(a)}for(var c=0;n.depth>0&&null!==(s=i.exec(o))&&c<n.depth;){if(c+=1,!n.plainObjects&&ii.call(Object.prototype,s[1].slice(1,-1))&&!n.allowPrototypes)return;u.push(s[1])}return s&&u.push("["+o.slice(s.index)+"]"),function(e,t,n,r){for(var o=r?t:ci(t,n),i=e.length-1;i>=0;--i){var s,a=e[i];if("[]"===a&&n.parseArrays)s=[].concat(o);else{s=n.plainObjects?Object.create(null):{};var u="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,c=parseInt(u,10);n.parseArrays||""!==u?!isNaN(c)&&a!==u&&String(c)===u&&c>=0&&n.parseArrays&&c<=n.arrayLimit?(s=[])[c]=o:"__proto__"!==u&&(s[u]=o):s={0:o}}o=s}return o}(u,t,n,r)}},pi=function(e,t){var n,r=e,o=function(e){if(!e)return ti;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||ti.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=Wo.default;if(void 0!==e.format){if(!Jo.call(Wo.formatters,e.format))throw new TypeError("Unknown format option provided.");n=e.format}var r=Wo.formatters[n],o=ti.filter;return("function"==typeof e.filter||Qo(e.filter))&&(o=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:ti.addQueryPrefix,allowDots:void 0===e.allowDots?ti.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:ti.charsetSentinel,delimiter:void 0===e.delimiter?ti.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:ti.encode,encoder:"function"==typeof e.encoder?e.encoder:ti.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:ti.encodeValuesOnly,filter:o,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:ti.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:ti.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:ti.strictNullHandling}}(t);"function"==typeof o.filter?r=(0,o.filter)("",r):Qo(o.filter)&&(n=o.filter);var i,s=[];if("object"!=typeof r||null===r)return"";i=t&&t.arrayFormat in $o?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var a=$o[i];if(t&&"commaRoundTrip"in t&&"boolean"!=typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var u="comma"===a&&t&&t.commaRoundTrip;n||(n=Object.keys(r)),o.sort&&n.sort(o.sort);for(var c=zo(),l=0;l<n.length;++l){var p=n[l];o.skipNulls&&null===r[p]||Yo(s,ri(r[p],p,a,u,o.strictNullHandling,o.skipNulls,o.encode?o.encoder:null,o.filter,o.sort,o.allowDots,o.serializeDate,o.format,o.formatter,o.encodeValuesOnly,o.charset,c))}var h=s.join(o.delimiter),f=!0===o.addQueryPrefix?"?":"";return o.charsetSentinel&&("iso-8859-1"===o.charset?f+="utf8=%26%2310003%3B&":f+="utf8=%E2%9C%93&"),h.length>0?f+h:""},hi={formats:Fo,parse:function(e,t){var n=function(e){if(!e)return ai;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?ai.charset:e.charset;return{allowDots:void 0===e.allowDots?ai.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:ai.allowPrototypes,allowSparse:"boolean"==typeof e.allowSparse?e.allowSparse:ai.allowSparse,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:ai.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:ai.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:ai.comma,decoder:"function"==typeof e.decoder?e.decoder:ai.decoder,delimiter:"string"==typeof e.delimiter||oi.isRegExp(e.delimiter)?e.delimiter:ai.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:ai.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:ai.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:ai.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:ai.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:ai.strictNullHandling}}(t);if(""===e||null==e)return n.plainObjects?Object.create(null):{};for(var r="string"==typeof e?function(e,t){var n,r={__proto__:null},o=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,i=t.parameterLimit===1/0?void 0:t.parameterLimit,s=o.split(t.delimiter,i),a=-1,u=t.charset;if(t.charsetSentinel)for(n=0;n<s.length;++n)0===s[n].indexOf("utf8=")&&("utf8=%E2%9C%93"===s[n]?u="utf-8":"utf8=%26%2310003%3B"===s[n]&&(u="iso-8859-1"),a=n,n=s.length);for(n=0;n<s.length;++n)if(n!==a){var c,l,p=s[n],h=p.indexOf("]="),f=-1===h?p.indexOf("="):h+1;-1===f?(c=t.decoder(p,ai.decoder,u,"key"),l=t.strictNullHandling?null:""):(c=t.decoder(p.slice(0,f),ai.decoder,u,"key"),l=oi.maybeMap(ci(p.slice(f+1),t),(function(e){return t.decoder(e,ai.decoder,u,"value")}))),l&&t.interpretNumericEntities&&"iso-8859-1"===u&&(l=ui(l)),p.indexOf("[]=")>-1&&(l=si(l)?[l]:l),ii.call(r,c)?r[c]=oi.combine(r[c],l):r[c]=l}return r}(e,n):e,o=n.plainObjects?Object.create(null):{},i=Object.keys(r),s=0;s<i.length;++s){var a=i[s],u=li(a,r[a],n,"string"==typeof e);o=oi.merge(o,u,n)}return!0===n.allowSparse?o:oi.compact(o)},stringify:pi},fi={};!function(e){function t(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return n(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,u=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return a=e.done,e},e:function(e){u=!0,s=e},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw s}}}}function n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}e.type=e=>e.split(/ *; */).shift(),e.params=e=>{const n={};var r,o=t(e.split(/ *; */));try{for(o.s();!(r=o.n()).done;){const e=r.value.split(/ *= */),t=e.shift(),o=e.shift();t&&o&&(n[t]=o)}}catch(e){o.e(e)}finally{o.f()}return n},e.parseLinks=e=>{const n={};var r,o=t(e.split(/ *, */));try{for(o.s();!(r=o.n()).done;){const e=r.value.split(/ *; */),t=e[0].slice(1,-1);n[e[1].split(/ *= */)[1].slice(1,-1)]=t}}catch(e){o.e(e)}finally{o.f()}return n},e.cleanHeader=(e,t)=>(delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,t&&(delete e.authorization,delete e.cookie),e),e.isObject=e=>null!==e&&"object"==typeof e,e.hasOwn=Object.hasOwn||function(e,t){if(null==e)throw new TypeError("Cannot convert undefined or null to object");return Object.prototype.hasOwnProperty.call(new Object(e),t)},e.mixin=(t,n)=>{for(const r in n)e.hasOwn(n,r)&&(t[r]=n[r])}}(fi);const di=Or,yi=fi.isObject,gi=fi.hasOwn;var mi=bi;function bi(){}bi.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},bi.prototype.parse=function(e){return this._parser=e,this},bi.prototype.responseType=function(e){return this._responseType=e,this},bi.prototype.serialize=function(e){return this._serializer=e,this},bi.prototype.timeout=function(e){if(!e||"object"!=typeof e)return this._timeout=e,this._responseTimeout=0,this._uploadTimeout=0,this;for(const t in e)if(gi(e,t))switch(t){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;case"upload":this._uploadTimeout=e.upload;break;default:console.warn("Unknown timeout option",t)}return this},bi.prototype.retry=function(e,t){return 0!==arguments.length&&!0!==e||(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};const vi=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),_i=new Set([408,413,429,500,502,503,504,521,522,524]);bi.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{const n=this._retryCallback(e,t);if(!0===n)return!0;if(!1===n)return!1}catch(e){console.error(e)}if(t&&t.status&&_i.has(t.status))return!0;if(e){if(e.code&&vi.has(e.code))return!0;if(e.timeout&&"ECONNABORTED"===e.code)return!0;if(e.crossDomain)return!0}return!1},bi.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},bi.prototype.then=function(e,t){if(!this._fullfilledPromise){const e=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise(((t,n)=>{e.on("abort",(()=>{if(this._maxRetries&&this._maxRetries>this._retries)return;if(this.timedout&&this.timedoutError)return void n(this.timedoutError);const e=new Error("Aborted");e.code="ABORTED",e.status=this.status,e.method=this.method,e.url=this.url,n(e)})),e.end(((e,r)=>{e?n(e):t(r)}))}))}return this._fullfilledPromise.then(e,t)},bi.prototype.catch=function(e){return this.then(void 0,e)},bi.prototype.use=function(e){return e(this),this},bi.prototype.ok=function(e){if("function"!=typeof e)throw new Error("Callback required");return this._okCallback=e,this},bi.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},bi.prototype.get=function(e){return this._header[e.toLowerCase()]},bi.prototype.getHeader=bi.prototype.get,bi.prototype.set=function(e,t){if(yi(e)){for(const t in e)gi(e,t)&&this.set(t,e[t]);return this}return this._header[e.toLowerCase()]=t,this.header[e]=t,this},bi.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},bi.prototype.field=function(e,t,n){if(null==e)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(yi(e)){for(const t in e)gi(e,t)&&this.field(t,e[t]);return this}if(Array.isArray(t)){for(const n in t)gi(t,n)&&this.field(e,t[n]);return this}if(null==t)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof t&&(t=String(t)),n?this._getFormData().append(e,t,n):this._getFormData().append(e,t),this},bi.prototype.abort=function(){if(this._aborted)return this;if(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req){if(di.gte(process.version,"v13.0.0")&&di.lt(process.version,"v14.0.0"))throw new Error("Superagent does not work in v13 properly with abort() due to Node.js core changes");this.req.abort()}return this.clearTimeout(),this.emit("abort"),this},bi.prototype._auth=function(e,t,n,r){switch(n.type){case"basic":this.set("Authorization",`Basic ${r(`${e}:${t}`)}`);break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization",`Bearer ${e}`)}return this},bi.prototype.withCredentials=function(e){return void 0===e&&(e=!0),this._withCredentials=e,this},bi.prototype.redirects=function(e){return this._maxRedirects=e,this},bi.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw new TypeError("Invalid argument");return this._maxResponseSize=e,this},bi.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},bi.prototype.send=function(e){const t=yi(e);let n=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(t&&!this._data)Array.isArray(e)?this._data=[]:this._isHost(e)||(this._data={});else if(e&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(t&&yi(this._data))for(const t in e){if("bigint"==typeof e[t]&&!e[t].toJSON)throw new Error("Cannot serialize BigInt value to json");gi(e,t)&&(this._data[t]=e[t])}else{if("bigint"==typeof e)throw new Error("Cannot send value of type BigInt");"string"==typeof e?(n||this.type("form"),n=this._header["content-type"],n&&(n=n.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===n?this._data?`${this._data}&${e}`:e:(this._data||"")+e):this._data=e}return!t||this._isHost(e)||n||this.type("json"),this},bi.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},bi.prototype._finalizeQueryString=function(){const e=this._query.join("&");if(e&&(this.url+=(this.url.includes("?")?"&":"?")+e),this._query.length=0,this._sort){const e=this.url.indexOf("?");if(e>=0){const t=this.url.slice(e+1).split("&");"function"==typeof this._sort?t.sort(this._sort):t.sort(),this.url=this.url.slice(0,e)+"?"+t.join("&")}}},bi.prototype._appendQueryString=()=>{console.warn("Unsupported")},bi.prototype._timeoutError=function(e,t,n){if(this._aborted)return;const r=new Error(`${e+t}ms exceeded`);r.timeout=t,r.code="ECONNABORTED",r.errno=n,this.timedout=!0,this.timedoutError=r,this.abort(),this.callback(r)},bi.prototype._setTimeouts=function(){const e=this;this._timeout&&!this._timer&&(this._timer=setTimeout((()=>{e._timeoutError("Timeout of ",e._timeout,"ETIME")}),this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout((()=>{e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")}),this._responseTimeout))};const Si=fi;var wi=Oi;function Oi(){}function Pi(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return Ei(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ei(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,s=!0,a=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return s=e.done,e},e:function(e){a=!0,i=e},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw i}}}}function Ei(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ai(){this._defaults=[]}Oi.prototype.get=function(e){return this.header[e.toLowerCase()]},Oi.prototype._setHeaderProperties=function(e){const t=e["content-type"]||"";this.type=Si.type(t);const n=Si.params(t);for(const e in n)Object.prototype.hasOwnProperty.call(n,e)&&(this[e]=n[e]);this.links={};try{e.link&&(this.links=Si.parseLinks(e.link))}catch(e){}},Oi.prototype._setStatusProperties=function(e){const t=Math.trunc(e/100);this.statusCode=e,this.status=this.statusCode,this.statusType=t,this.info=1===t,this.ok=2===t,this.redirect=3===t,this.clientError=4===t,this.serverError=5===t,this.error=(4===t||5===t)&&this.toError(),this.created=201===e,this.accepted=202===e,this.noContent=204===e,this.badRequest=400===e,this.unauthorized=401===e,this.notAcceptable=406===e,this.forbidden=403===e,this.notFound=404===e,this.unprocessableEntity=422===e};for(var Ti=0,Ni=["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"];Ti<Ni.length;Ti++){const e=Ni[Ti];Ai.prototype[e]=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this._defaults.push({fn:e,args:n}),this}}Ai.prototype._setDefaults=function(e){var t,n=Pi(this._defaults);try{for(n.s();!(t=n.n()).done;){const n=t.value;e[n.fn](...n.args)}}catch(e){n.e(e)}finally{n.f()}};var Ci=Ai;!function(e,t){function n(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return r(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return r(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var o=0,i=function(){};return{s:i,n:function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,u=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){u=!0,s=e},f:function(){try{a||null==n.return||n.return()}finally{if(u)throw s}}}}function r(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}let o;"undefined"!=typeof window?o=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),o=void 0):o=self;const i=Pn.exports,s=En,a=hi,u=mi,c=fi.isObject,l=fi.mixin,p=fi.hasOwn,h=wi,f=Ci;function d(){}e.exports=function(e,n){return"function"==typeof n?new t.Request("GET",e).end(n):1===arguments.length?new t.Request("GET",e):new t.Request(e,n)};const y=t=e.exports;t.Request=w,y.getXHR=()=>{if(o.XMLHttpRequest)return new o.XMLHttpRequest;throw new Error("Browser-only version of superagent could not find XHR")};const g="".trim?e=>e.trim():e=>e.replace(/(^\s*|\s*$)/g,"");function m(e){if(!c(e))return e;const t=[];for(const n in e)p(e,n)&&b(t,n,e[n]);return t.join("&")}function b(e,t,r){if(void 0!==r)if(null!==r)if(Array.isArray(r)){var o,i=n(r);try{for(i.s();!(o=i.n()).done;){b(e,t,o.value)}}catch(e){i.e(e)}finally{i.f()}}else if(c(r))for(const n in r)p(r,n)&&b(e,`${t}[${n}]`,r[n]);else e.push(encodeURI(t)+"="+encodeURIComponent(r));else e.push(encodeURI(t))}function v(e){const t={},n=e.split("&");let r,o;for(let e=0,i=n.length;e<i;++e)r=n[e],o=r.indexOf("="),-1===o?t[decodeURIComponent(r)]="":t[decodeURIComponent(r.slice(0,o))]=decodeURIComponent(r.slice(o+1));return t}function _(e){return/[/+]json($|[^-\w])/i.test(e)}function S(e){this.req=e,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;let t=this.xhr.status;1223===t&&(t=204),this._setStatusProperties(t),this.headers=function(e){const t=e.split(/\r?\n/),n={};let r,o,i,s;for(let e=0,a=t.length;e<a;++e)o=t[e],r=o.indexOf(":"),-1!==r&&(i=o.slice(0,r).toLowerCase(),s=g(o.slice(r+1)),n[i]=s);return n}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&e._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function w(e,t){const n=this;this._query=this._query||[],this.method=e,this.url=t,this.header={},this._header={},this.on("end",(()=>{let e,t=null,r=null;try{r=new S(n)}catch(e){return t=new Error("Parser is unable to parse the response"),t.parse=!0,t.original=e,n.xhr?(t.rawResponse=void 0===n.xhr.responseType?n.xhr.responseText:n.xhr.response,t.status=n.xhr.status?n.xhr.status:null,t.statusCode=t.status):(t.rawResponse=null,t.status=null),n.callback(t)}n.emit("response",r);try{n._isResponseOK(r)||(e=new Error(r.statusText||r.text||"Unsuccessful HTTP response"))}catch(t){e=t}e?(e.original=t,e.response=r,e.status=e.status||r.status,n.callback(e,r)):n.callback(null,r)}))}y.serializeObject=m,y.parseString=v,y.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},y.serialize={"application/x-www-form-urlencoded":a.stringify,"application/json":s},y.parse={"application/x-www-form-urlencoded":v,"application/json":JSON.parse},l(S.prototype,h.prototype),S.prototype._parseBody=function(e){let t=y.parse[this.type];return this.req._parser?this.req._parser(this,e):(!t&&_(this.type)&&(t=y.parse["application/json"]),t&&e&&(e.length>0||e instanceof Object)?t(e):null)},S.prototype.toError=function(){const e=this.req,t=e.method,n=e.url,r=`cannot ${t} ${n} (${this.status})`,o=new Error(r);return o.status=this.status,o.method=t,o.url=n,o},y.Response=S,i(w.prototype),l(w.prototype,u.prototype),w.prototype.type=function(e){return this.set("Content-Type",y.types[e]||e),this},w.prototype.accept=function(e){return this.set("Accept",y.types[e]||e),this},w.prototype.auth=function(e,t,n){1===arguments.length&&(t=""),"object"==typeof t&&null!==t&&(n=t,t=""),n||(n={type:"function"==typeof btoa?"basic":"auto"});const r=n.encoder?n.encoder:e=>{if("function"==typeof btoa)return btoa(e);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(e,t,n,r)},w.prototype.query=function(e){return"string"!=typeof e&&(e=m(e)),e&&this._query.push(e),this},w.prototype.attach=function(e,t,n){if(t){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(e,t,n||t.name)}return this},w.prototype._getFormData=function(){return this._formData||(this._formData=new o.FormData),this._formData},w.prototype.callback=function(e,t){if(this._shouldRetry(e,t))return this._retry();const n=this._callback;this.clearTimeout(),e&&(this._maxRetries&&(e.retries=this._retries-1),this.emit("error",e)),n(e,t)},w.prototype.crossDomainError=function(){const e=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");e.crossDomain=!0,e.status=this.status,e.method=this.method,e.url=this.url,this.callback(e)},w.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},w.prototype.ca=w.prototype.agent,w.prototype.buffer=w.prototype.ca,w.prototype.write=()=>{throw new Error("Streaming is not supported in browser version of superagent")},w.prototype.pipe=w.prototype.write,w.prototype._isHost=function(e){return e&&"object"==typeof e&&!Array.isArray(e)&&"[object Object]"!==Object.prototype.toString.call(e)},w.prototype.end=function(e){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=e||d,this._finalizeQueryString(),this._end()},w.prototype._setUploadTimeout=function(){const e=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout((()=>{e._timeoutError("Upload timeout of ",e._uploadTimeout,"ETIMEDOUT")}),this._uploadTimeout))},w.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));const e=this;this.xhr=y.getXHR();const t=this.xhr;let n=this._formData||this._data;this._setTimeouts(),t.addEventListener("readystatechange",(()=>{const n=t.readyState;if(n>=2&&e._responseTimeoutTimer&&clearTimeout(e._responseTimeoutTimer),4!==n)return;let r;try{r=t.status}catch(e){r=0}if(!r){if(e.timedout||e._aborted)return;return e.crossDomainError()}e.emit("end")}));const r=(t,n)=>{n.total>0&&(n.percent=n.loaded/n.total*100,100===n.percent&&clearTimeout(e._uploadTimeoutTimer)),n.direction=t,e.emit("progress",n)};if(this.hasListeners("progress"))try{t.addEventListener("progress",r.bind(null,"download")),t.upload&&t.upload.addEventListener("progress",r.bind(null,"upload"))}catch(e){}t.upload&&this._setUploadTimeout();try{this.username&&this.password?t.open(this.method,this.url,!0,this.username,this.password):t.open(this.method,this.url,!0)}catch(e){return this.callback(e)}if(this._withCredentials&&(t.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof n&&!this._isHost(n)){const e=this._header["content-type"];let t=this._serializer||y.serialize[e?e.split(";")[0]:""];!t&&_(e)&&(t=y.serialize["application/json"]),t&&(n=t(n))}for(const e in this.header)null!==this.header[e]&&p(this.header,e)&&t.setRequestHeader(e,this.header[e]);this._responseType&&(t.responseType=this._responseType),this.emit("request",this),t.send(void 0===n?null:n)},y.agent=()=>new f;for(var O=0,P=["GET","POST","OPTIONS","PATCH","PUT","DELETE"];O<P.length;O++){const e=P[O];f.prototype[e.toLowerCase()]=function(t,n){const r=new y.Request(e,t);return this._setDefaults(r),n&&r.end(n),r}}function E(e,t,n){const r=y("DELETE",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r}f.prototype.del=f.prototype.delete,y.get=(e,t,n)=>{const r=y("GET",e);return"function"==typeof t&&(n=t,t=null),t&&r.query(t),n&&r.end(n),r},y.head=(e,t,n)=>{const r=y("HEAD",e);return"function"==typeof t&&(n=t,t=null),t&&r.query(t),n&&r.end(n),r},y.options=(e,t,n)=>{const r=y("OPTIONS",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},y.del=E,y.delete=E,y.patch=(e,t,n)=>{const r=y("PATCH",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},y.post=(e,t,n)=>{const r=y("POST",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},y.put=(e,t,n)=>{const r=y("PUT",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r}}(On,On.exports);var ki=On.exports;function Mi(e){var t=(new Date).getTime(),n=(new Date).toISOString(),r=console&&console.log?console:window&&window.console&&window.console.log?window.console:console;r.log("<<<<<"),r.log("[".concat(n,"]"),"\n",e.url,"\n",e.qs),r.log("-----"),e.on("response",(function(n){var o=(new Date).getTime()-t,i=(new Date).toISOString();r.log(">>>>>>"),r.log("[".concat(i," / ").concat(o,"]"),"\n",e.url,"\n",e.qs,"\n",n.text),r.log("-----")}))}function ji(e,t,n){var r=this;this._config.logVerbosity&&(e=e.use(Mi)),this._config.proxy&&this._modules.proxy&&(e=this._modules.proxy.call(this,e)),this._config.keepAlive&&this._modules.keepAlive&&(e=this._modules.keepAlive(e));var o=e;if(t.abortSignal)var i=t.abortSignal.subscribe((function(){o.abort(),i()}));return!0===t.forceBuffered?o="undefined"==typeof Blob?o.buffer().responseType("arraybuffer"):o.responseType("arraybuffer"):!1===t.forceBuffered&&(o=o.buffer(!1)),(o=o.timeout(t.timeout)).on("abort",(function(){return n({category:k.PNUnknownCategory,error:!0,operation:t.operation,errorData:new Error("Aborted")},null)})),o.end((function(e,o){var i,s={};if(s.error=null!==e,s.operation=t.operation,o&&o.status&&(s.statusCode=o.status),e){if(e.response&&e.response.text&&!r._config.logVerbosity)try{s.errorData=JSON.parse(e.response.text)}catch(t){s.errorData=e}else s.errorData=e;return s.category=r._detectErrorCategory(e),n(s,null)}if(t.ignoreBody)i={headers:o.headers,redirects:o.redirects,response:o};else try{i=JSON.parse(o.text)}catch(e){return s.errorData=o,s.error=!0,n(s,null)}return i.error&&1===i.error&&i.status&&i.message&&i.service?(s.errorData=i,s.statusCode=i.status,s.error=!0,s.category=r._detectErrorCategory(s),n(s,null)):(i.error&&i.error.message&&(s.errorData=i.error),n(s,i))})),o}function Ri(e,t,n){return o(this,void 0,void 0,(function(){var r;return i(this,(function(o){switch(o.label){case 0:return r=ki.post(e),t.forEach((function(e){var t=e.key,n=e.value;r=r.field(t,n)})),r.attach("file",n,{contentType:"application/octet-stream"}),[4,r];case 1:return[2,o.sent()]}}))}))}function xi(e,t,n){var r=ki.get(this.getStandardOrigin()+t.url).set(t.headers).query(e);return ji.call(this,r,t,n)}function Ui(e,t,n){var r=ki.get(this.getStandardOrigin()+t.url).set(t.headers).query(e);return ji.call(this,r,t,n)}function Ii(e,t,n,r){var o=ki.post(this.getStandardOrigin()+n.url).query(e).set(n.headers).send(t);return ji.call(this,o,n,r)}function Di(e,t,n,r){var o=ki.patch(this.getStandardOrigin()+n.url).query(e).set(n.headers).send(t);return ji.call(this,o,n,r)}function Fi(e,t,n){var r=ki.delete(this.getStandardOrigin()+t.url).set(t.headers).query(e);return ji.call(this,r,t,n)}function Li(e,t){var n=new Uint8Array(e.byteLength+t.byteLength);return n.set(new Uint8Array(e),0),n.set(new Uint8Array(t),e.byteLength),n.buffer}var Gi,Ki=function(){function e(){}return Object.defineProperty(e.prototype,"algo",{get:function(){return"aes-256-cbc"},enumerable:!1,configurable:!0}),e.prototype.encrypt=function(e,t){return o(this,void 0,void 0,(function(){var n;return i(this,(function(r){switch(r.label){case 0:return[4,this.getKey(e)];case 1:if(n=r.sent(),t instanceof ArrayBuffer)return[2,this.encryptArrayBuffer(n,t)];if("string"==typeof t)return[2,this.encryptString(n,t)];throw new Error("Cannot encrypt this file. In browsers file encryption supports only string or ArrayBuffer")}}))}))},e.prototype.decrypt=function(e,t){return o(this,void 0,void 0,(function(){var n;return i(this,(function(r){switch(r.label){case 0:return[4,this.getKey(e)];case 1:if(n=r.sent(),t instanceof ArrayBuffer)return[2,this.decryptArrayBuffer(n,t)];if("string"==typeof t)return[2,this.decryptString(n,t)];throw new Error("Cannot decrypt this file. In browsers file decryption supports only string or ArrayBuffer")}}))}))},e.prototype.encryptFile=function(e,t,n){return o(this,void 0,void 0,(function(){var r,o,s;return i(this,(function(i){switch(i.label){case 0:if(t.data.byteLength<=0)throw new Error("encryption error. empty content");return[4,this.getKey(e)];case 1:return r=i.sent(),[4,t.data.arrayBuffer()];case 2:return o=i.sent(),[4,this.encryptArrayBuffer(r,o)];case 3:return s=i.sent(),[2,n.create({name:t.name,mimeType:"application/octet-stream",data:s})]}}))}))},e.prototype.decryptFile=function(e,t,n){return o(this,void 0,void 0,(function(){var r,o,s;return i(this,(function(i){switch(i.label){case 0:return[4,this.getKey(e)];case 1:return r=i.sent(),[4,t.data.arrayBuffer()];case 2:return o=i.sent(),[4,this.decryptArrayBuffer(r,o)];case 3:return s=i.sent(),[2,n.create({name:t.name,data:s})]}}))}))},e.prototype.getKey=function(t){return o(this,void 0,void 0,(function(){var n,r,o;return i(this,(function(i){switch(i.label){case 0:return[4,crypto.subtle.digest("SHA-256",e.encoder.encode(t))];case 1:return n=i.sent(),r=Array.from(new Uint8Array(n)).map((function(e){return e.toString(16).padStart(2,"0")})).join(""),o=e.encoder.encode(r.slice(0,32)).buffer,[2,crypto.subtle.importKey("raw",o,"AES-CBC",!0,["encrypt","decrypt"])]}}))}))},e.prototype.encryptArrayBuffer=function(e,t){return o(this,void 0,void 0,(function(){var n,r,o;return i(this,(function(i){switch(i.label){case 0:return n=crypto.getRandomValues(new Uint8Array(16)),r=Li,o=[n.buffer],[4,crypto.subtle.encrypt({name:"AES-CBC",iv:n},e,t)];case 1:return[2,r.apply(void 0,o.concat([i.sent()]))]}}))}))},e.prototype.decryptArrayBuffer=function(t,n){return o(this,void 0,void 0,(function(){var r;return i(this,(function(o){switch(o.label){case 0:if(r=n.slice(0,16),n.slice(e.IV_LENGTH).byteLength<=0)throw new Error("decryption error: empty content");return[4,crypto.subtle.decrypt({name:"AES-CBC",iv:r},t,n.slice(e.IV_LENGTH))];case 1:return[2,o.sent()]}}))}))},e.prototype.encryptString=function(t,n){return o(this,void 0,void 0,(function(){var r,o,s,a;return i(this,(function(i){switch(i.label){case 0:return r=crypto.getRandomValues(new Uint8Array(16)),o=e.encoder.encode(n).buffer,[4,crypto.subtle.encrypt({name:"AES-CBC",iv:r},t,o)];case 1:return s=i.sent(),a=Li(r.buffer,s),[2,e.decoder.decode(a)]}}))}))},e.prototype.decryptString=function(t,n){return o(this,void 0,void 0,(function(){var r,o,s,a;return i(this,(function(i){switch(i.label){case 0:return r=e.encoder.encode(n).buffer,o=r.slice(0,16),s=r.slice(16),[4,crypto.subtle.decrypt({name:"AES-CBC",iv:o},t,s)];case 1:return a=i.sent(),[2,e.decoder.decode(a)]}}))}))},e.IV_LENGTH=16,e.encoder=new TextEncoder,e.decoder=new TextDecoder,e}(),Bi=(Gi=function(){function e(e){if(e instanceof File)this.data=e,this.name=this.data.name,this.mimeType=this.data.type;else if(e.data){var t=e.data;this.data=new File([t],e.name,{type:e.mimeType}),this.name=e.name,e.mimeType&&(this.mimeType=e.mimeType)}if(void 0===this.data)throw new Error("Couldn't construct a file out of supplied options.");if(void 0===this.name)throw new Error("Couldn't guess filename out of the options. Please provide one.")}return e.create=function(e){return new this(e)},e.prototype.toBuffer=function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){throw new Error("This feature is only supported in Node.js environments.")}))}))},e.prototype.toStream=function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){throw new Error("This feature is only supported in Node.js environments.")}))}))},e.prototype.toFileUri=function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){throw new Error("This feature is only supported in react native environments.")}))}))},e.prototype.toBlob=function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){return[2,this.data]}))}))},e.prototype.toArrayBuffer=function(){return o(this,void 0,void 0,(function(){var e=this;return i(this,(function(t){return[2,new Promise((function(t,n){var r=new FileReader;r.addEventListener("load",(function(){if(r.result instanceof ArrayBuffer)return t(r.result)})),r.addEventListener("error",(function(){n(r.error)})),r.readAsArrayBuffer(e.data)}))]}))}))},e.prototype.toString=function(){return o(this,void 0,void 0,(function(){var e=this;return i(this,(function(t){return[2,new Promise((function(t,n){var r=new FileReader;r.addEventListener("load",(function(){if("string"==typeof r.result)return t(r.result)})),r.addEventListener("error",(function(){n(r.error)})),r.readAsBinaryString(e.data)}))]}))}))},e.prototype.toFile=function(){return o(this,void 0,void 0,(function(){return i(this,(function(e){return[2,this.data]}))}))},e}(),Gi.supportsFile="undefined"!=typeof File,Gi.supportsBlob="undefined"!=typeof Blob,Gi.supportsArrayBuffer="undefined"!=typeof ArrayBuffer,Gi.supportsBuffer=!1,Gi.supportsStream=!1,Gi.supportsString=!0,Gi.supportsEncryptFile=!0,Gi.supportsFileUri=!1,Gi),Hi=function(){function e(e){this.config=e,this.cryptor=new T({config:e}),this.fileCryptor=new Ki}return Object.defineProperty(e.prototype,"identifier",{get:function(){return""},enumerable:!1,configurable:!0}),e.prototype.encrypt=function(e){var t="string"==typeof e?e:(new TextDecoder).decode(e);return{data:this.cryptor.encrypt(t),metadata:null}},e.prototype.decrypt=function(e){var t="string"==typeof e.data?e.data:b(e.data);return this.cryptor.decrypt(t)},e.prototype.encryptFile=function(e,t){var n;return o(this,void 0,void 0,(function(){return i(this,(function(r){return[2,this.fileCryptor.encryptFile(null===(n=this.config)||void 0===n?void 0:n.cipherKey,e,t)]}))}))},e.prototype.decryptFile=function(e,t){return o(this,void 0,void 0,(function(){return i(this,(function(n){return[2,this.fileCryptor.decryptFile(this.config.cipherKey,e,t)]}))}))},e}(),qi=function(){function e(e){this.cipherKey=e.cipherKey,this.CryptoJS=E,this.encryptedKey=this.CryptoJS.SHA256(this.cipherKey)}return Object.defineProperty(e.prototype,"algo",{get:function(){return"AES-CBC"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"identifier",{get:function(){return"ACRH"},enumerable:!1,configurable:!0}),e.prototype.getIv=function(){return crypto.getRandomValues(new Uint8Array(e.BLOCK_SIZE))},e.prototype.getKey=function(){return o(this,void 0,void 0,(function(){var t,n;return i(this,(function(r){switch(r.label){case 0:return t=e.encoder.encode(this.cipherKey),[4,crypto.subtle.digest("SHA-256",t.buffer)];case 1:return n=r.sent(),[2,crypto.subtle.importKey("raw",n,this.algo,!0,["encrypt","decrypt"])]}}))}))},e.prototype.encrypt=function(t){if(0===("string"==typeof t?t:e.decoder.decode(t)).length)throw new Error("encryption error. empty content");var n=this.getIv();return{metadata:n,data:m(this.CryptoJS.AES.encrypt(t,this.encryptedKey,{iv:this.bufferToWordArray(n),mode:this.CryptoJS.mode.CBC}).ciphertext.toString(this.CryptoJS.enc.Base64))}},e.prototype.decrypt=function(t){var n=this.bufferToWordArray(new Uint8ClampedArray(t.metadata)),r=this.bufferToWordArray(new Uint8ClampedArray(t.data));return e.encoder.encode(this.CryptoJS.AES.decrypt({ciphertext:r},this.encryptedKey,{iv:n,mode:this.CryptoJS.mode.CBC}).toString(this.CryptoJS.enc.Utf8)).buffer},e.prototype.encryptFileData=function(e){return o(this,void 0,void 0,(function(){var t,n,r;return i(this,(function(o){switch(o.label){case 0:return[4,this.getKey()];case 1:return t=o.sent(),n=this.getIv(),r={},[4,crypto.subtle.encrypt({name:this.algo,iv:n},t,e)];case 2:return[2,(r.data=o.sent(),r.metadata=n,r)]}}))}))},e.prototype.decryptFileData=function(e){return o(this,void 0,void 0,(function(){var t;return i(this,(function(n){switch(n.label){case 0:return[4,this.getKey()];case 1:return t=n.sent(),[2,crypto.subtle.decrypt({name:this.algo,iv:e.metadata},t,e.data)]}}))}))},e.prototype.bufferToWordArray=function(e){var t,n=[];for(t=0;t<e.length;t+=1)n[t/4|0]|=e[t]<<24-8*t;return this.CryptoJS.lib.WordArray.create(n,e.length)},e.BLOCK_SIZE=16,e.encoder=new TextEncoder,e.decoder=new TextDecoder,e}(),zi=function(){function e(e){var t;this.defaultCryptor=e.default,this.cryptors=null!==(t=e.cryptors)&&void 0!==t?t:[]}return e.legacyCryptoModule=function(e){var t;return new this({default:new Hi({cipherKey:e.cipherKey,useRandomIVs:null===(t=e.useRandomIVs)||void 0===t||t}),cryptors:[new qi({cipherKey:e.cipherKey})]})},e.aesCbcCryptoModule=function(e){var t;return new this({default:new qi({cipherKey:e.cipherKey}),cryptors:[new Hi({cipherKey:e.cipherKey,useRandomIVs:null===(t=e.useRandomIVs)||void 0===t||t})]})},e.withDefaultCryptor=function(e){return new this({default:e})},e.prototype.getAllCryptors=function(){return u([this.defaultCryptor],a(this.cryptors),!1)},e.prototype.encrypt=function(e){var t=this.defaultCryptor.encrypt(e);if(!t.metadata)return t.data;var n=this.getHeaderData(t);return this.concatArrayBuffer(n,t.data)},e.prototype.decrypt=function(e){var t="string"==typeof e?m(e):e,n=Vi.tryParse(t),r=this.getCryptor(n),o=n.length>0?t.slice(n.length-n.metadataLength,n.length):null;if(t.slice(n.length).byteLength<=0)throw new Error("decryption error. empty content");return r.decrypt({data:t.slice(n.length),metadata:o})},e.prototype.encryptFile=function(e,t){return o(this,void 0,void 0,(function(){var n,r;return i(this,(function(o){switch(o.label){case 0:return this.defaultCryptor.identifier===Vi.LEGACY_IDENTIFIER?[2,this.defaultCryptor.encryptFile(e,t)]:[4,this.getFileData(e.data)];case 1:return n=o.sent(),[4,this.defaultCryptor.encryptFileData(n)];case 2:return r=o.sent(),[2,t.create({name:e.name,mimeType:"application/octet-stream",data:this.concatArrayBuffer(this.getHeaderData(r),r.data)})]}}))}))},e.prototype.decryptFile=function(t,n){return o(this,void 0,void 0,(function(){var r,o,s,a,u,c,l,p;return i(this,(function(i){switch(i.label){case 0:return[4,t.data.arrayBuffer()];case 1:return r=i.sent(),o=Vi.tryParse(r),(null==(s=this.getCryptor(o))?void 0:s.identifier)===e.LEGACY_IDENTIFIER?[2,s.decryptFile(t,n)]:[4,this.getFileData(r)];case 2:return a=i.sent(),u=a.slice(o.length-o.metadataLength,o.length),l=(c=n).create,p={name:t.name},[4,this.defaultCryptor.decryptFileData({data:r.slice(o.length),metadata:u})];case 3:return[2,l.apply(c,[(p.data=i.sent(),p)])]}}))}))},e.prototype.getCryptor=function(e){if(""===e){var t=this.getAllCryptors().find((function(e){return""===e.identifier}));if(t)return t;throw new Error("unknown cryptor error")}if(e instanceof Wi)return this.getCryptorFromId(e.identifier)},e.prototype.getCryptorFromId=function(e){var t=this.getAllCryptors().find((function(t){return e===t.identifier}));if(t)return t;throw Error("unknown cryptor error")},e.prototype.concatArrayBuffer=function(e,t){var n=new Uint8Array(e.byteLength+t.byteLength);return n.set(new Uint8Array(e),0),n.set(new Uint8Array(t),e.byteLength),n.buffer},e.prototype.getHeaderData=function(e){if(e.metadata){var t=Vi.from(this.defaultCryptor.identifier,e.metadata),n=new Uint8Array(t.length),r=0;return n.set(t.data,r),r+=t.length-e.metadata.byteLength,n.set(new Uint8Array(e.metadata),r),n.buffer}},e.prototype.getFileData=function(t){return o(this,void 0,void 0,(function(){return i(this,(function(n){switch(n.label){case 0:return t instanceof Blob?[4,t.arrayBuffer()]:[3,2];case 1:return[2,n.sent()];case 2:if(t instanceof ArrayBuffer)return[2,t];if("string"==typeof t)return[2,e.encoder.encode(t)];throw new Error("Cannot decrypt/encrypt file. In browsers file encrypt/decrypt supported for string, ArrayBuffer or Blob")}}))}))},e.LEGACY_IDENTIFIER="",e.encoder=new TextEncoder,e.decoder=new TextDecoder,e}(),Vi=function(){function e(){}return e.from=function(t,n){if(t!==e.LEGACY_IDENTIFIER)return new Wi(t,n.byteLength)},e.tryParse=function(t){var n=new Uint8Array(t),r="";if(n.byteLength>=4&&(r=n.slice(0,4),this.decoder.decode(r)!==e.SENTINEL))return"";if(!(n.byteLength>=5))throw new Error("decryption error. invalid header version");if(n[4]>e.MAX_VERSION)throw new Error("unknown cryptor error");var o="",i=5+e.IDENTIFIER_LENGTH;if(!(n.byteLength>=i))throw new Error("decryption error. invalid crypto identifier");o=n.slice(5,i);var s=null;if(!(n.byteLength>=i+1))throw new Error("decryption error. invalid metadata length");return s=n[i],i+=1,255===s&&n.byteLength>=i+2&&(s=new Uint16Array(n.slice(i,i+2)).reduce((function(e,t){return(e<<8)+t}),0),i+=2),new Wi(this.decoder.decode(o),s)},e.SENTINEL="PNED",e.LEGACY_IDENTIFIER="",e.IDENTIFIER_LENGTH=4,e.VERSION=1,e.MAX_VERSION=1,e.decoder=new TextDecoder,e}(),Wi=function(){function e(e,t){this._identifier=e,this._metadataLength=t}return Object.defineProperty(e.prototype,"identifier",{get:function(){return this._identifier},set:function(e){this._identifier=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"metadataLength",{get:function(){return this._metadataLength},set:function(e){this._metadataLength=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return Vi.VERSION},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"length",{get:function(){return Vi.SENTINEL.length+1+Vi.IDENTIFIER_LENGTH+(this.metadataLength<255?1:3)+this.metadataLength},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"data",{get:function(){var e=0,t=new Uint8Array(this.length),n=new TextEncoder;t.set(n.encode(Vi.SENTINEL)),t[e+=Vi.SENTINEL.length]=this.version,e++,this.identifier&&t.set(n.encode(this.identifier),e),e+=Vi.IDENTIFIER_LENGTH;var r=this.metadataLength;return r<255?t[e]=r:t.set([255,r>>8,255&r],e),t},enumerable:!1,configurable:!0}),e.IDENTIFIER_LENGTH=4,e.SENTINEL="PNED",e}();function Ji(e){if(!navigator||!navigator.sendBeacon)return!1;navigator.sendBeacon(e)}var $i=function(e){function n(t){var n=this,r=t.listenToBrowserNetworkEvents,o=void 0===r||r;return t.sdkFamily="Web",t.networking=new _n({del:Fi,get:Ui,post:Ii,patch:Di,sendBeacon:Ji,getfile:xi,postfile:Ri}),t.cbor=new wn((function(e){return Sn(h.decode(e))}),m),t.PubNubFile=Bi,t.cryptography=new Ki,t.initCryptoModule=function(e){return new zi({default:new Hi({cipherKey:e.cipherKey,useRandomIVs:e.useRandomIVs}),cryptors:[new qi({cipherKey:e.cipherKey})]})},n=e.call(this,t)||this,o&&(window.addEventListener("offline",(function(){n.networkDownDetected()})),window.addEventListener("online",(function(){n.networkUpDetected()}))),n}return t(n,e),n.CryptoModule=zi,n}(vn);return $i}));
