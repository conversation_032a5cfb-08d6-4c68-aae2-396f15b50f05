---
changelog:
  - date: 2024-04-18
    version: v7.6.3
    changes:
      - type: bug
        text: "Fixes issue of add or remove listener of subscription to/from subscriptionSet."
  - date: 2024-03-28
    version: v7.6.2
    changes:
      - type: feature
        text: "Added support for pagination params for listChannels API of push notification devices."
  - date: 2024-02-26
    version: v7.6.1
    changes:
      - type: bug
        text: "Fixes issue of App context event handling for channel and membership."
  - date: 2024-02-21
    version: v7.6.0
    changes:
      - type: feature
        text: "Adding channel, channelGroup, channelMetadata and userMetadata entities to be first-class citizens to access APIs related to them. Currently, access is provided only for subscription API."
  - date: 2024-01-16
    version: v7.5.0
    changes:
      - type: feature
        text: "Added `enableEventEngine`, `maintainPresenceState` flags and `retryConfiguration` for retry policy configuration."
      - type: bug
        text: "Fixes issue of allowing duplicate listener registration."
      - type: bug
        text: "Fixes file name conflict in lib directory."
  - date: 2023-11-28
    version: v7.4.5
    changes:
      - type: bug
        text: "Handle unencrypted messages in subscribe with cryptoModule configured."
      - type: bug
        text: "Fixe for missing parameters to request or filter optional fields for App Context memberships api."
  - date: 2023-11-14
    version: v7.4.4
    changes:
      - type: bug
        text: "Fixes issue of getChannelMembers call not returning status field."
  - date: 2023-11-08
    version: v7.4.3
    changes:
      - type: bug
        text: "Fixes issue of not able to encrypt Blob file content in web."
  - date: 2023-10-30
    version: v7.4.2
    changes:
      - type: improvement
        text: "Changed license type from MIT to PubNub Software Development Kit License."
  - date: 2023-10-17
    version: v7.4.1
    changes:
      - type: bug
        text: "Fixes issue of `pubnub.decrypt()` returning wrong data format."
  - date: 2023-10-16
    version: v7.4.0
    changes:
      - type: feature
        text: "Add crypto module that allows configure SDK to encrypt and decrypt messages."
      - type: bug
        text: "Improved security of crypto implementation by adding enhanced AES-CBC cryptor."
  - date: 2023-09-11
    version: v7.3.3
    changes:
      - type: bug
        text: "Fixes issue of getting misleading error message when sendFile fails."
  - date: 2023-08-31
    version: v7.3.2
    changes:
      - type: bug
        text: "Fixes issue of having deprecated superagent version."
  - date: 2023-08-21
    version: v7.3.1
    changes:
      - type: bug
        text: "Fixes issue of missing get and set methods for userId field of PubNub configuration."
  - date: 2023-07-26
    version: v7.3.0
    changes:
      - type: bug
        text: "Fixes issue of severe vulnerability warnings for vm2 usage."
  - date: 2023-06-19
    version: v7.2.3
    changes:
      - type: feature
        text: "Added optional param `withHeartbeat` to set state through heartbeat endpoint."
  - date: 2022-12-12
    version: v7.2.2
    changes:
      - type: bug
        text: "Fixes a case in React Native with using an error interface in superagent."
      - type: bug
        text: "Fixes issue of getFileUrl not setting auth value as token string when token is set."
  - date: 2022-11-10
    version: v7.2.1
    changes:
      - type: bug
        text: "Removes remains of Buffer from the crypto module."
  - date: 2022-07-01
    version: v7.2.0
    changes:
      - type: feature
        text: 'Allows to specify users and spaces in grantToken method.'
      - type: feature
        text: 'Allows to use userId instead of uuid in configuration.'
  - date: 2022-06-22
    version: v7.1.2
    changes:
      - type: bug
        text: 'Fixes parseToken issues on Web and React Native.'
  - date: 2022-06-14
    version: v7.1.1
    changes:
      - type: feature
        text: 'Added user and space memberships related methods.'
      - type: feature
        text: 'Added `type` and `status` fields in `User` and `Space`. `status` field in memberships.'
  - date: 2022-05-24
    version: v7.0.1
    changes:
      - type: bug
        text: 'Fixes export issue for Node and update config for es module compatibility.'
  - date: 2022-05-24
    version: v7.0.0
    changes:
      - type: improvement
        text: 'BREAKING CHANGES: Removed objects v1 methods support.'
  - date: 2022-04-21
    version: v6.0.0
    changes:
      - type: feature
        text: 'Added a TypeScript build chain and moved from webpack to rollup.'
      - type: feature
        text: 'Added an initial implementation of Event Engine.'
  - date: 2022-03-02
    version: v5.0.1
    changes:
      - type: bug
        text: 'Unsubscribe fix unsubscribe from channel group presence'
  - date: 2022-01-12
    version: v5.0.0
    changes:
      - type: improvement
        text: 'BREAKING CHANGES: `uuid` is required parameter in PubNub constructor.'
  - date: 2021-12-16
    version: v4.37.0
    changes:
      - type: feature
        text: 'Add revoke token feature.'
  - date: 2021-12-09
    version: v4.36.0
    changes:
      - type: bug
        text: 'Remove isomorphic-webcrypto polyfill for web Add buffer polyfill to react native.'
  - date: 2021-12-02
    version: v4.35.0
    changes:
      - type: feature
        text: 'Allows to specify multiple origins in the config, which enables domain sharding for custom origins.'
  - date: 2021-12-01
    version: v4.34.2
    changes:
      - type: bug
        text: 'Fix listener callback is invoked multiple times.'
  - date: 2021-11-19
    version: v4.34.1
    changes:
      - type: bug
        text: 'Update `.npmignore` and excluded resources from from NPM package.'
  - date: 2021-11-19
    version: v4.34.0
    changes:
      - type: feature
        text: 'Upgrade superagent.'
  - changes:
      - text: 'Fixes issue of performing file publish message retry according to `fileUploadPublishRetryLimit` setting of PubNub instance.'
        type: bug
    date: 2021-10-18
    version: v4.33.1
  - changes:
      - text: 'Added support for Objects v2 in PAM v3 api.'
        type: feature
      - text: 'Fixes issue related to file decryption when cipherkey is provided in method.'
        type: bug
    date: 2021-08-31
    version: v4.33.0
  - changes:
      - text: 'Fixes issue of signature does not match error with `getAllUUIDMetadata` call.'
        type: bug
      - text: 'Error handling with global hereNow call to provide detailed error message when feature not enabled.'
        type: bug
    date: 2021-05-26
    version: v4.32.1
  - changes:
      - text: 'Add grantToken support for channel and group resources.'
        type: feature
    date: 2021-04-28
    version: v4.32.0
  - changes:
      - text: 'BREAKING CHANGE - Set `true` for `useRandomIVs` by default.'
        type: improvement
      - text: 'Fix `channel` and `uuid` which is used with: files API, Objects and presence.'
        type: bug
    date: 2021-04-22
    version: v4.31.0
  - changes:
      - text: 'Revert v4.300.'
        type: bug
    date: 2021-03-30
    version: v4.30.1
  - changes:
      - text: 'Set default increased limit for message count of History v3 api single call.'
        type: improvement
    date: 2021-01-11
    version: v4.29.11
  - changes:
      - text: 'Fixes issue of missing more field in fetch messages response.'
        type: bug
    date: 2020-11-30
    version: v4.29.10
  - changes:
      - text: 'Adds timetoken of file publish in the sendFile response.'
        type: feature
      - text: 'Fixes getFileUrl so that it includes auth and signature query params.'
        type: bug
      - text: 'Fixes downloadFile method to generate correct signature.'
        type: bug
    date: 2020-10-05
    version: v4.29.9
  - changes:
      - text: 'Fixes compatibility with @sentry/react-native library.'
        type: bug
    date: 2020-09-21
    version: v4.29.8
  - changes:
      - text: 'Added support for managing permissions of objects v2 while applying PAM v2.'
        type: feature
      - text: 'Fix uncaught promise exception in subscription manager caused by error in user code inside of subscription handlers. Error will be handled and returned to status handler with PNUnknownCategory category where errorData can be examined.'
        type: bug
    date: 2020-09-14
    version: v4.29.7
  - changes:
      - text: 'Add file download to Blob in React Native.'
        type: feature
    date: 2020-09-08
    version: v4.29.6
  - changes:
      - text: 'Add support for file upload via file URI in React Native.'
        type: feature
      - text: 'Fix file download to ArrayBuffer in React Native.'
        type: bug
    date: 2020-09-01
    version: v4.29.5
  - changes:
      - text: 'Fixes an artifact where ract-native entrypoint didnt use ssl.'
        type: bug
    date: 2020-08-14
    version: v4.29.4
  - changes:
      - text: 'Fixes an issue with react-native entrypoint where interfaces to File and Crypto are not included in the build.'
        type: bug
      - text: 'Fixes the ability to sendByPost in publish.'
        type: bug
      - text: 'Fixes an issue where getFileUrl returned and URI without a protocol.'
        type: bug
      - text: 'Fixes an issue where storeInHistory false would not include the param.'
        type: bug
      - text: 'Removes mime types dependency since that will be handled by the server.'
        type: bug
      - text: 'Adds userMetadata to file event listener.'
        type: bug
    date: 2020-08-14
    version: v4.29.3
  - changes:
      - text: 'Move @babel/runtime to runtime dependency.'
        type: bug
    date: 2020-08-05
    version: v4.29.2
  - changes:
      - text: 'Release 4.291.'
        type: bug
    date: 2020-08-04
    version: v4.29.1
  - changes:
      - text: 'Allows to upload files to channels, download them with optional encryption support.'
        type: feature
      - text: 'Allows to enable random IVs when encrypting messages.'
        type: feature
      - text: 'Fixes a bug with PAM and Objects v2.'
        type: bug
    date: 2020-08-04
    version: v4.29.0
  - changes:
      - text: 'Fixes issue of high cpu usage when heartbeat interval is not set.'
        type: bug
    date: 2020-07-15
    version: v4.28.4
  - changes:
      - text: 'getAllChannelMetadata no longer includes customFields by default.'
        type: bug
      - text: 'removeChannelMetadata no longer hits wrong endpoint.'
        type: bug
      - text: 'getMemberships and getChannelMembers now includes customFields by default.'
        type: bug
      - text: 'getAllUUIDMetadata now includes totalCount by default.'
        type: bug
      - text: 'getAllUUIDMetadata no longer includes limit by default.'
        type: bug
      - text: 'all membership and channel members methods now accept a callback.'
        type: bug
      - text: 'all objects v2 methods are properly typed now to include an optional callback.'
        type: bug
      - text: 'getMemberships and getChannelMembers now include totalCount, prev, and next in the response.'
        type: bug
    date: 2020-07-15
    version: v4.28.3
  - changes:
      - text: 'Fixes a bug in removeChannelMembers and removeMemberships.'
        type: bug
    date: 2020-06-29
    version: v4.28.2
  - changes:
      - text: 'Ensure proper bytes padding in ArrayBuffer prepared for `cbor-js` library.'
        type: bug
    date: 2020-06-19
    version: v4.28.1
  - changes:
      - text: 'Added Objects v2 API and deprecated Objects v1 API.'
        type: feature
    date: 2020-06-03
    version: v4.28.0
  - changes:
      - text: 'Added support for delete permission in the grant method of accesses manager.'
        type: feature
    date: 2020-04-24
    version: v4.27.6
  - changes:
      - text: 'Update READMEmd CDN links during deployment.'
        type: bug
      - text: 'Fix pre-compiled scripts update.'
        type: bug
    date: 2020-04-21
    version: v4.27.5
  - changes:
      - text: 'Add telemetry (latency) for all existing operation types.'
        type: feature
      - text: 'Replace `cbor-sync` module with `cbor-js` for client implementation for web to fix compatibility with Salesforce Lightning Web Components.'
        type: bug
    date: 2020-03-18
    version: v4.27.4
  - changes:
      - text: 'Support for APNS2 Push API'
        type: improvement
      - text: 'Restore functionality to set heartbeat interval when presence timeout is set below the default'
        type: bug
    date: 2020-01-06
    version: v4.27.3
  - changes:
      - text: 'disable presence heartbeats by default'
        type: bug
    date: 2019-12-05
    version: v4.27.2
  - changes:
      - text: "Make changes in fetch_messages endpoint to move message actions (if any) for message from 'data' to 'actions' property (old 'data' will be in place for few updates to not break existing clients)."
        type: bug
      - text: 'fix PAMv3 tests mocked signature'
        type: improvement
      - text: 'fix lint warnings for tests and code'
        type: improvement
      - text: 'fix gulp build so that failures in test and lint will trigger failure in travis'
        type: improvement
    date: 2019-11-20
    version: v4.27.1
  - changes:
      - text: 'Add Message Actions API support which allow to: add, remove and fetch previously added actions'
        type: feature
      - text: 'Add new arguments to fetch messages function which allow to fetch previously added actions and message metadata'
        type: feature
      - text: 'Add new handler which can be used to track message actions addition / removal events'
        type: feature
    date: 2019-10-08
    version: v4.27.0
  - changes:
      - text: 'Ensures history response is an array before iterating it'
        type: bug
    date: 2019-09-27
    version: v4.26.1
  - changes:
      - text: 'Add support for auth tokens with Objects for Users, Spaces and Memberships'
        type: bug
    date: 2019-09-20
    version: v4.26.0
  - changes:
      - text: "Fix issue with subdomains ending in 'ps'"
        type: bug
    date: 2019-09-03
    version: v4.25.2
  - changes:
      - text: 'Fix titanium build to support recent version'
        type: bug
    date: 2019-08-23
    version: v4.25.1
  - changes:
      - text: 'Add Objects support for Users, Spaces and Memberships'
        type: improvement
    date: 2019-08-16
    version: v4.25.0
  - changes:
      - text: "Fix regression: 'PubNub is not a constructor' in Node.js"
        type: bug
    date: 2019-08-09
    version: v4.24.6
  - changes:
      - text: 'Add Signals support'
        type: improvement
    date: 2019-08-07
    version: v4.24.5
  - changes:
      - text: 'Add minimum presence timeout'
        type: improvement
    date: 2019-07-26
    version: v4.24.4
  - changes:
      - text: 'Added support to enable heartbeat requests while subscribe when heartbeat interval is provided'
        type: improvement
    date: 2019-06-19
    version: v4.24.3
  - changes:
      - text: 'Added try catch block to handle exception for JSON.parse function'
        type: improvement
      - text: 'Updated default origin to ps.pndsn.com'
        type: improvement
    date: 2019-06-13
    version: v4.24.2
  - changes:
      - text: 'Maintains the state when the presence heartbeat is explicitly disabled'
        type: improvement
    date: 2019-06-06
    version: v4.24.1
  - changes:
      - text: 'Disables the presence heartbeat by default when a subscribe is called. Presence heartbeat can still be enabled explicitly.'
        type: improvement
    date: 2019-05-09
    version: v4.24.0
  - changes:
      - text: "The `timetoken` parameter is deprecated in the `message-counts` function. Use 'channelTimetokens' instead, pass one value in 'channelTimetokens' to achieve the same results."
        type: improvement
    date: 2019-03-14
    version: v4.23.0
  - changes:
      - text: 'message counts'
        type: feature
      - text: "use null instead of '' for NativeScript networking module"
        type: improvement
    date: 2019-03-04
    version: v4.22.0
  - changes:
      - text: 'update dependencies'
        type: improvement
      - text: 'fix flow process on nativescript'
        type: improvement
    date: 2018-12-20
    version: v4.21.7
  - changes:
      - text: 'fix POST for nativescript adapter over android'
        type: bug
    date: 2018-10-04
    version: v4.21.6
  - changes:
      - text: 'update dependencies'
        type: improvement
    date: 2018-08-06
    version: v4.21.5
  - changes:
      - text: 'return error parameter into errorData when logVerbosity = true'
        type: improvement
    date: 2018-08-04
    version: v4.21.4
  - changes:
      - text: 'update dependencies'
        type: improvement
    date: 2018-07-10
    version: v4.21.3
  - changes:
      - text: 'add stringifiedTimeToken into the fetch endpoint'
        type: improvement
    date: 2018-06-12
    version: v4.21.2
  - changes:
      - text: 'avoid security vulnerability in growl < 1.10.0'
        type: bug
    date: 2018-06-08
    version: v4.21.1
  - changes:
      - text: 'subscribe without using the heartbeat loop with flag withHeartbeats = false'
        type: feature
    date: 2018-06-06
    version: v4.21.0
  - changes:
      - text: 'fix timetoken announces'
        type: bug
      - text: 'categorize ETIMEDOUT errors as PNNetworkIssuesCategory'
        type: improvement
    date: 2018-04-24
    version: v4.20.3
  - changes:
      - text: 'fix signature to delete message'
        type: bug
    date: 2018-02-28
    version: v4.20.2
  - changes:
      - text: 'allow set ssl to false for nodejs'
        type: improvement
    date: 2018-01-29
    version: v4.20.1
  - changes:
      - text: 'add support for heartbeat sending without subscription via .presence()'
        type: feature
      - text: 'add method setProxy for Nodejs'
        type: feature
      - text: 'set ssl to true for nodejs by default'
        type: feature
    date: 2018-01-04
    version: v4.20.0
  - changes:
      - text: 'add support for Native Script'
        type: feature
      - text: 'add missing flow types'
        type: improvement
      - text: 'upgrade superagent to ^3.8.1'
        type: improvement
    date: 2017-12-05
    version: v4.19.0
  - changes:
      - text: 'keepAlive is now initialized globally instead of per-call, allowing better connection reuse'
        type: improvement
      - text: 'added sdkName configuration parameter which allow completely override pnsdk in request query'
        type: feature
    date: 2017-11-20
    version: v4.18.0
  - changes:
      - text: 'allow disabling of heartbeats by passing 0 during initialization.'
        type: improvement
    date: 2017-10-19
    version: v4.17.0
  - changes:
      - text: 'fix UUID library to work in browsers.'
        type: bug
    date: 2017-10-19
    version: v4.16.2
  - changes:
      - text: 'fix incorrect packaging of lil-uuid and uuid'
        type: bug
    date: 2017-10-12
    version: v4.16.1
  - changes:
      - text: 'support delete messages from history'
        type: feature
      - text: 'swap uuid generator with support for IE9 and IE10'
        type: improvement
    date: 2017-10-10
    version: v4.16.0
  - changes:
      - text: 'fix typo to enable http keep alive support'
        type: improvement
    date: 2017-08-21
    version: v4.15.1
  - changes:
      - text: 'Support optional message deduping via the dedupeOnSubscribe config'
        type: improvement
      - text: 'Do not issue leave events if the channel mix is empty.'
        type: improvement
    date: 2017-08-21
    version: v4.15.0
  - changes:
      - text: 'Allow disable of heartbeats by passing heartbeatInterval = 0'
        type: improvement
    date: 2017-08-14
    version: v4.14.0
  - changes:
      - text: 'patch up 503 reporting'
        type: improvement
      - text: 'fix issue with where now and invalid server response'
        type: improvement
      - text: 'fix issue with here now and invalid server response'
        type: improvement
    date: 2017-07-27
    version: v4.13.0
  - changes:
      - text: 'fix issue of net with android for titanium'
        type: improvement
      - text: 'add additional hooks for connectivity'
        type: feature
      - text: 'add auto network detection'
        type: feature
    date: 2017-06-19
    version: v4.12.0
  - changes:
      - text: 'fix issue of net with android for react-native'
        type: improvement
    date: 2017-05-23
    version: v4.10.0
  - changes:
      - text: 'metadata is now passed on message envelope'
        type: feature
    date: ~
    version: v4.9.2
  - changes:
      - text: 'add support custom encryption and decryption'
        type: feature
    date: 2017-05-18
    version: v4.9.1
  - changes:
      - text: 'integrate fetch for react-native SDK'
        type: feature
      - text: 'announce when subscription get reactivated'
        type: improvement
      - text: 'stop heartbeats for responses with status PNBadRequestCategory'
        type: improvement
    date: ~
    version: v4.9.0
  - changes:
      - text: 'allow manual control over network state via listenToBrowserNetworkEvents'
        type: feature
    date: 2017-04-06
    version: v4.8.0
  - changes:
      - text: 'add support for titanium SDK'
        type: feature
      - text: 'fix support for react-native SDK'
        type: improvement
      - text: 'add validation for web distribution'
        type: improvement
    date: 2017-03-30
    version: v4.7.0
  - changes:
      - text: 'add support for presence deltas.'
        type: feature
      - text: 'keep track of new and upcoming timetokens on status messages'
        type: feature
    date: 2017-03-27
    version: v4.6.0
  - changes:
      - text: 'add optional support for keepAlive by passing the keepAlive config into the init logic'
        type: feature
    date: 2017-03-08
    version: v4.5.0
  - changes:
      - text: 'add guard to check for channel or channel group on state setting'
        type: improvement
      - text: 'add guard to check for publish, secret keys when performing a grant'
        type: improvement
    date: 2017-02-14
    version: v4.4.4
  - changes:
      - text: 'downgrade superagent to v2; add a new entry point for react native.'
        type: improvement
    date: 2017-02-07
    version: v4.4.3
  - changes:
      - text: 'adjust compilation for webpack based compilations'
        type: improvement
    date: 2017-01-31
    version: v4.4.2
  - changes:
      - text: 'proxy support for node'
        type: improvement
    date: 2017-01-31
    version: v4.4.1
  - changes:
      - text: 'upgrade dependencies; fix up linting.'
        type: improvement
      - text: 'handle network outage cases for correct reporting.'
        type: improvement
    date: 2017-01-23
    version: v4.4.0
  - changes:
      - text: 'bump version after v3 release.'
        type: improvement
    date: 2016-12-16
    version: v4.3.3
  - changes:
      - text: 'removes bundling of package.json into the dist file'
        type: improvement
    date: 2016-11-28
    version: v4.3.2
  - changes:
      - text: 'SDK now supports the restore config to allow message catch-up'
        type: improvement
    date: 2016-11-22
    version: v4.3.1
  - changes:
      - text: 'bulk history exposed via pubnub.fetchMessages'
        type: improvement
      - text: 'publish supports custom ttl interval'
        type: improvement
      - text: 'v2 for audit and grant; no consumer facing changes.'
        type: improvement
      - text: 'fixes for param validation on usage of promises'
        type: improvement
    date: 2016-11-18
    version: v4.3.0
  - changes:
      - text: 'SDK reports on the id of the publisher in the message'
        type: improvement
    date: 2016-11-04
    version: v4.2.5
  - changes:
      - text: 'Detection of support of promises improved.'
        type: improvement
    date: 2016-11-01
    version: v4.2.4
  - changes:
      - text: 'Fixes on encoding of apostrophes.'
        type: improvement
    date: 2016-11-01
    version: v4.2.3
  - changes:
      - text: 'Add promise support on setState operation (@jskrzypek)'
        type: improvement
      - text: 'Add hooks to stop polling time when the number of subscriptions drops to 0 (@jasonpoe)'
        type: improvement
    date: 2016-10-31
    version: v4.2.2
  - changes:
      - text: 'Encode signatures to avoid sending restricted characters'
        type: improvement
    date: 2016-10-30
    version: v4.2.1
  - changes:
      - text: 'Add optional support for promises on all endpoints.'
        type: improvement
      - text: 'History always returns timetokens in the payloads.'
        type: improvement
      - text: 'Optionally, if queue size is set, send status on queue size threshold'
        type: improvement
    date: 2016-10-26
    version: v4.2.0
  - changes:
      - text: 'Fix state setting for channels with reserved tags.'
        type: improvement
    date: 2016-10-17
    version: v4.1.1
  - changes:
      - text: 'Reset timetoken when all unsubscribes happen'
        type: improvement
      - text: 'Sign requests when a a secret key is passed'
        type: improvement
    date: 2016-10-13
    version: v4.1.0
  - changes:
      - text: 'Propagate status events to the status callback on subscribe operations.'
        type: improvement
    date: 2016-10-05
    version: v4.0.13
  - changes:
      - text: 'affectedChannels and affectedChannelGroups are now populated on subscribe / unsubscribe events'
        type: improvement
    date: 2016-10-03
    version: v4.0.12
  - changes:
      - text: 'Dependency upgrades'
        type: improvement
    date: 2016-09-27
    version: v4.0.11
  - changes:
      - text: 'Expose decryption and encryption as a global'
        type: improvement
    date: 2016-09-14
    version: v4.0.10
  - changes:
      - text: 'Channel / subscription items are populated in'
        type: improvement
      - text: 'Constants for operation and category are exposed on global object'
        type: improvement
    date: 2016-09-09
    version: v4.0.9
  - changes:
      - text: 'Re-publish of v4.0.7'
        type: improvement
    date: 2016-08-25
    version: v4.0.8
  - changes:
      - text: 'Dependency upgrades'
        type: improvement
      - text: 'Try..catch wrapped around localStorage for iframe compliance'
        type: improvement
    date: 2016-08-25
    version: v4.0.7
  - changes:
      - text: 'Adjustment of reconnection policies for web distributions.'
        type: improvement
      - text: 'PNSDK support for partner identification'
        type: improvement
    date: 2016-08-18
    version: v4.0.6
  - changes:
      - text: 'Stop exposing .babelrc which causes unpredictable behavior on react native.'
        type: improvement
    date: 2016-08-10
    version: v4.0.5
  - changes:
      - text: 'Adjust handling of presence payloads for state settings.'
        type: improvement
      - text: 'Exposing generateUUID method to create uuids.'
        type: feature
      - text: 'Triggering disconnect, reconnect events on Web distributions.'
        type: improvement
      - text: 'React Native adjustments to package.json information.'
        type: improvement
    date: 2016-08-09
    version: v4.0.4
  - changes:
      - text: 'Global Here Now parsing adjustments.'
        type: improvement
    date: 2016-08-07
    version: v4.0.3
  - changes:
      - text: 'Adjustments to internet disconnects on node.'
        type: improvement
    date: 2016-08-03
    version: v4.0.2
  - changes:
      - text: 'Fixes to avoid double encoding on JSON payloads.'
        type: bug
    date: 2016-08-01
    version: v4.0.1
  - changes:
      - text: 'New iteration of JS / Node SDK family'
        type: feature
    date: 2016-08-01
    version: v4.0.0
features:
  access:
    - ACCESS-GRANT-TOKEN
    - ACCESS-PARSE-TOKEN
    - ACCESS-SET-TOKEN
  channel-groups:
    - CHANNEL-GROUPS-ADD-CHANNELS
    - CHANNEL-GROUPS-REMOVE-CHANNELS
    - CHANNEL-GROUPS-REMOVE-GROUPS
    - CHANNEL-GROUPS-LIST-CHANNELS-IN-GROUP
  notify:
    - REQUEST-MESSAGE-COUNT-EXCEEDED
  presence:
    - PRESENCE-HERE-NOW
    - PRESENCE-WHERE-NOW
    - PRESENCE-SET-STATE
    - PRESENCE-GET-STATE
    - PRESENCE-HEARTBEAT
  publish:
    - PUBLISH-STORE-FLAG
    - PUBLISH-RAW-JSON
    - PUBLISH-WITH-METADATA
    - PUBLISH-GET
    - PUBLISH-POST
    - PUBLISH-ASYNC
    - PUBLISH-FIRE
    - PUBLISH-REPLICATION-FLAG
    - PUBLISH-MESSAGE-TTL
  push:
    - PUSH-ADD-DEVICE-TO-CHANNELS
    - PUSH-REMOVE-DEVICE-FROM-CHANNELS
    - PUSH-LIST-CHANNELS-FROM-DEVICE
    - PUSH-REMOVE-DEVICE
    - PUSH-TYPE-APNS
    - PUSH-TYPE-APNS2
    - PUSH-TYPE-FCM
  storage:
    - STORAGE-REVERSE
    - STORAGE-INCLUDE-TIMETOKEN
    - STORAGE-START-END
    - STORAGE-COUNT
    - STORAGE-DELETE-MESSAGES
    - STORAGE-FETCH-MESSAGES
    - STORAGE-MESSAGE-COUNT
    - STORAGE-HISTORY-WITH-META
    - STORAGE-FETCH-WITH-META
    - STORAGE-FETCH-WITH-MESSAGE-ACTIONS
  subscribe:
    - SUBSCRIBE-CHANNELS
    - SUBSCRIBE-CHANNEL-GROUPS
    - SUBSCRIBE-PRESENCE-CHANNELS
    - SUBSCRIBE-PRESENCE-CHANNELS-GROUPS
    - SUBSCRIBE-WITH-TIMETOKEN
    - SUBSCRIBE-WILDCARD
    - SUBSCRIBE-FILTER-EXPRESSION
    - SUBSCRIBE-PUBLISHER-UUID
    - SUBSCRIBE-WITH-USERSTATE
    - SUBSCRIBE-PUBSUB-V2
    - SUBSCRIBE-SIGNAL-LISTENER
    - SUBSCRIBE-USER-LISTENER
    - SUBSCRIBE-SPACE-LISTENER
    - SUBSCRIBE-MEMBERSHIP-LISTENER
    - SUBSCRIBE-MESSAGE-ACTIONS-LISTENER
  unsubscribe:
    - UNSUBSCRIBE-ALL
    - UNSUBSCRIBE-SUPPRESS-LEAVE-EVENTS
  signal:
    - SIGNAL-SEND
  objects:
    - OBJECTS-GET-ALL-UUID-METADATA
    - OBJECTS-GET-UUID-METADATA
    - OBJECTS-SET-UUID-METADATA
    - OBJECTS-REMOVE-UUID-METADATA
    - OBJECTS-GET-ALL-CHANNEL-METADATA
    - OBJECTS-GET-CHANNEL-METADATA
    - OBJECTS-SET-CHANNEL-METADATA
    - OBJECTS-REMOVE-CHANNEL-METADATA
    - OBJECTS-GET-MEMBERSHIPS-V2
    - OBJECTS-SET-MEMBERSHIPS-V2
    - OBJECTS-REMOVE-MEMBERSHIPS-V2
    - OBJECTS-GET-CHANNEL-MEMBERS-V2
    - OBJECTS-SET-CHANNEL-MEMBERS-V2
    - OBJECTS-REMOVE-CHANNEL-MEMBERS-V2
    - OBJECTS-MANAGE-MEMBERSHIPS-V2
    - OBJECTS-MANAGE-CHANNEL-MEMBERS-V2
  message-actions:
    - MESSAGE-ACTIONS-GET
    - MESSAGE-ACTIONS-ADD
    - MESSAGE-ACTIONS-REMOVE
  files:
    - FILES-SEND-FILE
    - FILES-LIST-FILES
    - FILES-GET-FILE-URL
    - FILES-DELETE-FILE
    - FILES-DOWNLOAD-FILE
  time:
    - TIME-TIME
  others:
    - TELEMETRY
    - CREATE-PUSH-PAYLOAD
files:
  - dist/web/pubnub.js
  - dist/web/pubnub.min.js
name: javascript
schema: 1
scm: github.com/pubnub/javascript
supported-platforms:
  - frameworks:
      - 'Angular JS'
      - 'Angular 2 and up'
    platforms:
      - 'Safari 10 and up'
      - 'Mozilla Firefox 51 and up'
      - 'Google Chrome 56 and up'
      - 'IE 9 and up (include a Promise polyfill for pubnub >= 4.29.0)'
      - 'Microsoft Edge 38 and up'
    version: 'Pubnub Javascript for Web'
  - editors:
      - '4 and up'
    platforms:
      - 'OSX 10.12 and up'
      - 'Ubuntu 14.04 and up'
      - 'Windows 7 and up'
    version: 'Pubnub Javascript for Node'
version: '7.6.3'
sdks:
  - full-name: PubNub Javascript SDK
    short-name: Javascript
    artifacts:
      - artifact-type: api-client
        language: Javascript
        tier: 1
        tags:
          - Web
        source-repository: https://github.com/pubnub/javascript
        documentation: https://www.pubnub.com/docs/sdks/javascript
        distributions:
          - distribution-type: source
            distribution-repository: GitHub release
            package-name: pubnub.js
            location: https://github.com/pubnub/javascript/archive/refs/tags/v7.6.3.zip
            requires:
              - name: 'agentkeepalive'
                min-version: '3.5.2'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/agentkeepalive/blob/HEAD/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/agentkeepalive'
                is-required: 'Required'
              - name: 'agent-base'
                min-version: '6.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-agent-base#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/agent-base'
                is-required: 'Required'
              - name: 'ast-types'
                min-version: '0.13.4'
                license: 'MIT'
                license-url: 'https://github.com/benjamn/ast-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ast-types'
                is-required: 'Required'
              - name: 'asynckit'
                min-version: '0.4.0'
                license: 'MIT'
                license-url: 'https://github.com/alexindigo/asynckit/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asynckit'
                is-required: 'Required'
              - name: 'bytes'
                min-version: '3.1.0'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/bytes.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/bytes'
                is-required: 'Required'
              - name: 'call-bind'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/call-bind/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/call-bind'
                is-required: 'Required'
              - name: 'cbor-js'
                min-version: '0.1.0'
                license: 'MIT'
                license-url: 'https://github.com/paroga/cbor-js/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-js'
                is-required: 'Required'
              - name: 'cbor-sync'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/PelionIoT/cbor-sync/blob/master/LICENSE.txt'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-sync'
                is-required: 'Required'
              - name: 'combined-stream'
                min-version: '1.0.8'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-combined-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/combined-stream'
                is-required: 'Required'
              - name: 'component-emitter'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/component/emitter/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/component-emitter'
                is-required: 'Required'
              - name: 'cookiejar'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/bmeck/node-cookiejar/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/cookiejar'
                is-required: 'Required'
              - name: 'core-util-is'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/isaacs/core-util-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/core-util-is'
                is-required: 'Required'
              - name: 'data-uri-to-buffer'
                min-version: '3.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-data-uri-to-buffer#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/data-uri-to-buffer'
                is-required: 'Required'
              - name: 'debug'
                min-version: '3.2.7'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/debug/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/debug'
                is-required: 'Required'
              - name: 'deep-is'
                min-version: '0.1.3'
                license: 'MIT'
                license-url: 'https://github.com/thlorenz/deep-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/deep-is'
                is-required: 'Required'
              - name: 'degenerator'
                min-version: '2.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-degenerator#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/degenerator'
                is-required: 'Required'
              - name: 'delayed-stream'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-delayed-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/delayed-stream'
                is-required: 'Required'
              - name: 'depd'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/dougwilson/nodejs-depd/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/depd'
                is-required: 'Required'
              - name: 'escodegen'
                min-version: '1.14.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/escodegen/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/escodegen'
                is-required: 'Required'
              - name: 'esprima'
                min-version: '4.0.1'
                license: 'BSD2'
                license-url: 'https://github.com/jquery/esprima/blob/main/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esprima'
                is-required: 'Required'
              - name: 'estraverse'
                min-version: '4.3.0'
                license: 'BSD2'
                license-url: 'https://github.com/estools/estraverse/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/estraverse'
                is-required: 'Required'
              - name: 'esutils'
                min-version: '2.0.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/esutils/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esutils'
                is-required: 'Required'
              - name: 'extend'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/justmoon/node-extend/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/extend'
                is-required: 'Required'
              - name: 'fast-levenshtein'
                min-version: '2.0.6'
                license: 'MIT'
                license-url: 'https://github.com/hiddentao/fast-levenshtein/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fast-levenshtein'
                is-required: 'Required'
              - name: 'file-uri-to-path'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/file-uri-to-path/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/file-uri-to-path'
                is-required: 'Required'
              - name: 'form-data'
                min-version: '2.5.1'
                license: 'MIT'
                license-url: 'https://github.com/form-data/form-data/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/form-data'
                is-required: 'Required'
              - name: 'formidable'
                min-version: '1.2.2'
                license: 'MIT'
                license-url: 'https://github.com/node-formidable/formidable/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/formidable'
                is-required: 'Required'
              - name: 'fs-extra'
                min-version: '8.1.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-fs-extra/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fs-extra'
                is-required: 'Required'
              - name: 'ftp'
                min-version: '0.3.10'
                license: 'MIT'
                license-url: 'https://github.com/mscdex/node-ftp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ftp'
                is-required: 'Required'
              - name: 'function-bind'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/Raynos/function-bind/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/function-bind'
                is-required: 'Required'
              - name: 'get-intrinsic'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/get-intrinsic/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-intrinsic'
                is-required: 'Required'
              - name: 'get-uri'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-get-uri#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-uri'
                is-required: 'Required'
              - name: 'graceful-fs'
                min-version: '4.2.6'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-graceful-fs/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/graceful-fs'
                is-required: 'Required'
              - name: 'has'
                min-version: '1.0.3'
                license: 'MIT'
                license-url: 'https://github.com/tarruda/has/blob/master/LICENSE-MIT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has'
                is-required: 'Required'
              - name: 'has-symbols'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/has-symbols/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has-symbols'
                is-required: 'Required'
              - name: 'http-errors'
                min-version: '1.7.3'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/http-errors/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-errors'
                is-required: 'Required'
              - name: 'http-proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-http-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-proxy-agent'
                is-required: 'Required'
              - name: 'humanize-ms'
                min-version: '1.2.1'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/humanize-ms/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/humanize-ms'
                is-required: 'Required'
              - name: 'iconv-lite'
                min-version: '0.4.24'
                license: 'MIT'
                license-url: 'https://github.com/ashtuchkin/iconv-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/iconv-lite'
                is-required: 'Required'
              - name: 'inherits'
                min-version: '2.0.4'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/inherits/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/inherits'
                is-required: 'Required'
              - name: 'ip'
                min-version: '1.1.5'
                license: 'MIT'
                license-url: 'https://github.com/indutny/node-ip#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ip'
                is-required: 'Required'
              - name: 'isarray'
                min-version: '0.0.1'
                license: 'MIT'
                license-url: 'https://github.com/juliangruber/isarray/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/isarray'
                is-required: 'Required'
              - name: 'jsonfile'
                min-version: '4.0.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-jsonfile/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/jsonfile'
                is-required: 'Required'
              - name: 'levn'
                min-version: '0.3.0'
                license: 'MIT'
                license-url: 'https://github.com/gkz/levn/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/levn'
                is-required: 'Required'
              - name: 'lil-uuid'
                min-version: '0.1.1'
                license: 'MIT'
                license-url: 'https://github.com/lil-js/uuid/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/lil-uuid'
                is-required: 'Required'
              - name: 'lru-cache'
                min-version: '5.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-lru-cache/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/lru-cache'
                is-required: 'Required'
              - name: 'methods'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/methods/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/methods'
                is-required: 'Required'
              - name: 'mime'
                min-version: '1.6.0'
                license: 'MIT'
                license-url: 'https://github.com/broofa/mime/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime'
                is-required: 'Required'
              - name: 'mime-db'
                min-version: '1.47.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-db/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-db'
                is-required: 'Required'
              - name: 'mime-types'
                min-version: '2.1.30'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-types'
                is-required: 'Required'
              - name: 'ms'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/vercel/ms/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ms'
                is-required: 'Required'
              - name: 'netmask'
                min-version: '2.0.2'
                license: 'MIT'
                license-url: 'https://github.com/rs/node-netmask#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/netmask'
                is-required: 'Required'
              - name: 'object-inspect'
                min-version: '1.9.0'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/object-inspect/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/object-inspect'
                is-required: 'Required'
              - name: 'optionator'
                min-version: '0.8.3'
                license: 'MIT'
                license-url: 'https://github.com/gkz/optionator/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/optionator'
                is-required: 'Required'
              - name: 'pac-proxy-agent'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-proxy-agent'
                is-required: 'Required'
              - name: 'pac-resolver'
                min-version: '4.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-resolver#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-resolver'
                is-required: 'Required'
              - name: 'prelude-ls'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/prelude-ls/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/prelude-ls'
                is-required: 'Required'
              - name: 'process-nextick-args'
                min-version: '2.0.1'
                license: 'MIT'
                license-url: 'https://github.com/calvinmetcalf/process-nextick-args/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/process-nextick-args'
                is-required: 'Required'
              - name: 'proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-agent'
                is-required: 'Required'
              - name: 'proxy-from-env'
                min-version: '1.1.0'
                license: 'MIT'
                license-url: 'https://github.com/Rob--W/proxy-from-env/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-from-env'
                is-required: 'Required'
              - name: 'qs'
                min-version: '6.10.1'
                license: 'BSD3'
                license-url: 'https://github.com/ljharb/qs/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/qs'
                is-required: 'Required'
              - name: 'raw-body'
                min-version: '2.4.1'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/raw-body/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/raw-body'
                is-required: 'Required'
              - name: 'readable-stream'
                min-version: '1.1.14'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/readable-stream/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/readable-stream'
                is-required: 'Required'
              - name: 'safer-buffer'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/ChALkeR/safer-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/safer-buffer'
                is-required: 'Required'
              - name: 'setprototypeof'
                min-version: '1.1.1'
                license: 'ISC'
                license-url: 'https://github.com/wesleytodd/setprototypeof/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/setprototypeof'
                is-required: 'Required'
              - name: 'side-channel'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/side-channel/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/side-channel'
                is-required: 'Required'
              - name: 'smart-buffer'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/smart-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/smart-buffer'
                is-required: 'Required'
              - name: 'socks'
                min-version: '2.6.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/socks/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks'
                is-required: 'Required'
              - name: 'socks-proxy-agent'
                min-version: '5.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-socks-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks-proxy-agent'
                is-required: 'Required'
              - name: 'source-map'
                min-version: '0.6.1'
                license: 'BSD3'
                license-url: 'https://github.com/mozilla/source-map/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/source-map'
                is-required: 'Required'
              - name: 'statuses'
                min-version: '1.5.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/statuses/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/statuses'
                is-required: 'Required'
              - name: 'string_decoder'
                min-version: '0.10.31'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/string_decoder/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/string_decoder'
                is-required: 'Required'
              - name: 'superagent'
                min-version: '3.8.1'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/superagent/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent'
                is-required: 'Required'
              - name: 'superagent-proxy'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/superagent-proxy#license'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent-proxy'
                is-required: 'Required'
              - name: 'toidentifier'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/component/toidentifier/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/toidentifier'
                is-required: 'Required'
              - name: '@tootallnate/once'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/once/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@tootallnate/once'
                is-required: 'Required'
              - name: 'type-check'
                min-version: '0.3.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/type-check/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/type-check'
                is-required: 'Required'
              - name: 'universalify'
                min-version: '0.1.2'
                license: 'MIT'
                license-url: 'https://github.com/RyanZim/universalify/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/universalify'
                is-required: 'Required'
              - name: 'unpipe'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/unpipe/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/unpipe'
                is-required: 'Required'
              - name: 'util-deprecate'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/util-deprecate/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/util-deprecate'
                is-required: 'Required'
              - name: 'word-wrap'
                min-version: '1.2.3'
                license: 'MIT'
                license-url: 'https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/word-wrap'
                is-required: 'Required'
              - name: 'xregexp'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/slevithan/xregexp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/xregexp'
                is-required: 'Required'
              - name: 'yallist'
                min-version: '3.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/yallist/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/yallist'
                is-required: 'Required'
              - name: 'isomorphic-webcrypto'
                min-version: '2.3.6'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/isomorphic-webcrypto/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/isomorphic-webcrypto'
                is-required: 'Required'
              - name: '@peculiar/webcrypto'
                min-version: '1.0.22'
                license: 'MIT'
                license-url: 'https://github.com/PeculiarVentures/webcrypto/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@peculiar/webcrypto'
                is-required: 'Required'
              - name: 'asmcrypto.js'
                min-version: '0.22.0'
                license: 'MIT'
                license-url: 'https://github.com/asmcrypto/asmcrypto.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asmcrypto.js'
                is-required: 'Required'
              - name: 'b64-lite'
                min-version: '1.3.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64-lite'
                is-required: 'Required'
              - name: 'b64u-lite'
                min-version: '1.0.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64u-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64u-lite'
                is-required: 'Required'
              - name: 'msrcrypto'
                min-version: '1.5.6'
                license: 'Apache20'
                license-url: 'https://github.com/kevlened/msrCrypto/blob/master/LICENSE.TXT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/msrcrypto'
                is-required: 'Required'
              - name: 'str2buf'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/str2buf/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/str2buf'
                is-required: 'Required'
              - name: 'webcrypto-shim'
                min-version: '0.1.4'
                license: 'MIT'
                license-url: 'https://github.com/vibornoff/webcrypto-shim/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/webcrypto-shim'
                is-required: 'Required'
            supported-platforms:
              supported-browsers:
                minimun-supported-version:
                  - Chrome iOS 86.0.4240
                  - Chrome Android 86.0.4240
                  - Chrome Linux 86.0.4240
                  - Chrome macOS 86.0.4240
                  - Chrome Windows 86.0.4240
                  - Firefox Linux desktop 83.0 (x64)
                  - Firefox Linux desktop 83.0 (IA-32)
                  - Firefox iOS 29.0
                  - Firefox Windows 83.0 (IA-32)
                  - Firefox Windows 83.0 (x64)
                  - Firefox Windows 83.0 (ARM64)
                  - Firefox macOS 83.0
                  - Firefox Android 83.0 (x64)
                  - Firefox Android 83.0 (ARM64)
                  - Firefox Android 83.0 (IA-32 and ARMv7)
                  - Firefox OpenBSD 83.0 (IA-32,x64,ARM64)
                  - Microsoft Edge 87.0.664.60"
                  - Safari 13.0
      - artifact-type: library
        language: Javascript
        tier: 1
        tags:
          - Web
        source-repository: https://github.com/pubnub/javascript
        documentation: https://www.pubnub.com/docs/sdks/javascript
        distributions:
          - distribution-type: library
            distribution-repository: GitHub release
            package-name: pubnub.js
            location: https://github.com/pubnub/javascript/releases/download/v7.6.3/pubnub.7.6.3.js
            requires:
              - name: 'agentkeepalive'
                min-version: '3.5.2'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/agentkeepalive/blob/HEAD/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/agentkeepalive'
                is-required: 'Required'
              - name: 'agent-base'
                min-version: '6.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-agent-base#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/agent-base'
                is-required: 'Required'
              - name: 'ast-types'
                min-version: '0.13.4'
                license: 'MIT'
                license-url: 'https://github.com/benjamn/ast-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ast-types'
                is-required: 'Required'
              - name: 'asynckit'
                min-version: '0.4.0'
                license: 'MIT'
                license-url: 'https://github.com/alexindigo/asynckit/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asynckit'
                is-required: 'Required'
              - name: 'bytes'
                min-version: '3.1.0'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/bytes.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/bytes'
                is-required: 'Required'
              - name: 'call-bind'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/call-bind/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/call-bind'
                is-required: 'Required'
              - name: 'cbor-js'
                min-version: '0.1.0'
                license: 'MIT'
                license-url: 'https://github.com/paroga/cbor-js/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-js'
                is-required: 'Required'
              - name: 'cbor-sync'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/PelionIoT/cbor-sync/blob/master/LICENSE.txt'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-sync'
                is-required: 'Required'
              - name: 'combined-stream'
                min-version: '1.0.8'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-combined-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/combined-stream'
                is-required: 'Required'
              - name: 'component-emitter'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/component/emitter/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/component-emitter'
                is-required: 'Required'
              - name: 'cookiejar'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/bmeck/node-cookiejar/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/cookiejar'
                is-required: 'Required'
              - name: 'core-util-is'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/isaacs/core-util-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/core-util-is'
                is-required: 'Required'
              - name: 'data-uri-to-buffer'
                min-version: '3.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-data-uri-to-buffer#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/data-uri-to-buffer'
                is-required: 'Required'
              - name: 'debug'
                min-version: '3.2.7'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/debug/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/debug'
                is-required: 'Required'
              - name: 'deep-is'
                min-version: '0.1.3'
                license: 'MIT'
                license-url: 'https://github.com/thlorenz/deep-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/deep-is'
                is-required: 'Required'
              - name: 'degenerator'
                min-version: '2.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-degenerator#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/degenerator'
                is-required: 'Required'
              - name: 'delayed-stream'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-delayed-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/delayed-stream'
                is-required: 'Required'
              - name: 'depd'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/dougwilson/nodejs-depd/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/depd'
                is-required: 'Required'
              - name: 'escodegen'
                min-version: '1.14.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/escodegen/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/escodegen'
                is-required: 'Required'
              - name: 'esprima'
                min-version: '4.0.1'
                license: 'BSD2'
                license-url: 'https://github.com/jquery/esprima/blob/main/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esprima'
                is-required: 'Required'
              - name: 'estraverse'
                min-version: '4.3.0'
                license: 'BSD2'
                license-url: 'https://github.com/estools/estraverse/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/estraverse'
                is-required: 'Required'
              - name: 'esutils'
                min-version: '2.0.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/esutils/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esutils'
                is-required: 'Required'
              - name: 'extend'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/justmoon/node-extend/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/extend'
                is-required: 'Required'
              - name: 'fast-levenshtein'
                min-version: '2.0.6'
                license: 'MIT'
                license-url: 'https://github.com/hiddentao/fast-levenshtein/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fast-levenshtein'
                is-required: 'Required'
              - name: 'file-uri-to-path'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/file-uri-to-path/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/file-uri-to-path'
                is-required: 'Required'
              - name: 'form-data'
                min-version: '2.5.1'
                license: 'MIT'
                license-url: 'https://github.com/form-data/form-data/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/form-data'
                is-required: 'Required'
              - name: 'formidable'
                min-version: '1.2.2'
                license: 'MIT'
                license-url: 'https://github.com/node-formidable/formidable/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/formidable'
                is-required: 'Required'
              - name: 'fs-extra'
                min-version: '8.1.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-fs-extra/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fs-extra'
                is-required: 'Required'
              - name: 'ftp'
                min-version: '0.3.10'
                license: 'MIT'
                license-url: 'https://github.com/mscdex/node-ftp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ftp'
                is-required: 'Required'
              - name: 'function-bind'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/Raynos/function-bind/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/function-bind'
                is-required: 'Required'
              - name: 'get-intrinsic'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/get-intrinsic/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-intrinsic'
                is-required: 'Required'
              - name: 'get-uri'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-get-uri#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-uri'
                is-required: 'Required'
              - name: 'graceful-fs'
                min-version: '4.2.6'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-graceful-fs/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/graceful-fs'
                is-required: 'Required'
              - name: 'has'
                min-version: '1.0.3'
                license: 'MIT'
                license-url: 'https://github.com/tarruda/has/blob/master/LICENSE-MIT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has'
                is-required: 'Required'
              - name: 'has-symbols'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/has-symbols/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has-symbols'
                is-required: 'Required'
              - name: 'http-errors'
                min-version: '1.7.3'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/http-errors/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-errors'
                is-required: 'Required'
              - name: 'http-proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-http-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-proxy-agent'
                is-required: 'Required'
              - name: 'humanize-ms'
                min-version: '1.2.1'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/humanize-ms/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/humanize-ms'
                is-required: 'Required'
              - name: 'iconv-lite'
                min-version: '0.4.24'
                license: 'MIT'
                license-url: 'https://github.com/ashtuchkin/iconv-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/iconv-lite'
                is-required: 'Required'
              - name: 'inherits'
                min-version: '2.0.4'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/inherits/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/inherits'
                is-required: 'Required'
              - name: 'ip'
                min-version: '1.1.5'
                license: 'MIT'
                license-url: 'https://github.com/indutny/node-ip#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ip'
                is-required: 'Required'
              - name: 'isarray'
                min-version: '0.0.1'
                license: 'MIT'
                license-url: 'https://github.com/juliangruber/isarray/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/isarray'
                is-required: 'Required'
              - name: 'jsonfile'
                min-version: '4.0.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-jsonfile/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/jsonfile'
                is-required: 'Required'
              - name: 'levn'
                min-version: '0.3.0'
                license: 'MIT'
                license-url: 'https://github.com/gkz/levn/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/levn'
                is-required: 'Required'
              - name: 'lil-uuid'
                min-version: '0.1.1'
                license: 'MIT'
                license-url: 'https://github.com/lil-js/uuid/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/lil-uuid'
                is-required: 'Required'
              - name: 'lru-cache'
                min-version: '5.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-lru-cache/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/lru-cache'
                is-required: 'Required'
              - name: 'methods'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/methods/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/methods'
                is-required: 'Required'
              - name: 'mime'
                min-version: '1.6.0'
                license: 'MIT'
                license-url: 'https://github.com/broofa/mime/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime'
                is-required: 'Required'
              - name: 'mime-db'
                min-version: '1.47.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-db/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-db'
                is-required: 'Required'
              - name: 'mime-types'
                min-version: '2.1.30'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-types'
                is-required: 'Required'
              - name: 'ms'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/vercel/ms/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ms'
                is-required: 'Required'
              - name: 'netmask'
                min-version: '2.0.2'
                license: 'MIT'
                license-url: 'https://github.com/rs/node-netmask#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/netmask'
                is-required: 'Required'
              - name: 'object-inspect'
                min-version: '1.9.0'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/object-inspect/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/object-inspect'
                is-required: 'Required'
              - name: 'optionator'
                min-version: '0.8.3'
                license: 'MIT'
                license-url: 'https://github.com/gkz/optionator/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/optionator'
                is-required: 'Required'
              - name: 'pac-proxy-agent'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-proxy-agent'
                is-required: 'Required'
              - name: 'pac-resolver'
                min-version: '4.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-resolver#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-resolver'
                is-required: 'Required'
              - name: 'prelude-ls'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/prelude-ls/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/prelude-ls'
                is-required: 'Required'
              - name: 'process-nextick-args'
                min-version: '2.0.1'
                license: 'MIT'
                license-url: 'https://github.com/calvinmetcalf/process-nextick-args/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/process-nextick-args'
                is-required: 'Required'
              - name: 'proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-agent'
                is-required: 'Required'
              - name: 'proxy-from-env'
                min-version: '1.1.0'
                license: 'MIT'
                license-url: 'https://github.com/Rob--W/proxy-from-env/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-from-env'
                is-required: 'Required'
              - name: 'qs'
                min-version: '6.10.1'
                license: 'BSD3'
                license-url: 'https://github.com/ljharb/qs/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/qs'
                is-required: 'Required'
              - name: 'raw-body'
                min-version: '2.4.1'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/raw-body/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/raw-body'
                is-required: 'Required'
              - name: 'readable-stream'
                min-version: '1.1.14'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/readable-stream/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/readable-stream'
                is-required: 'Required'
              - name: 'safer-buffer'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/ChALkeR/safer-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/safer-buffer'
                is-required: 'Required'
              - name: 'setprototypeof'
                min-version: '1.1.1'
                license: 'ISC'
                license-url: 'https://github.com/wesleytodd/setprototypeof/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/setprototypeof'
                is-required: 'Required'
              - name: 'side-channel'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/side-channel/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/side-channel'
                is-required: 'Required'
              - name: 'smart-buffer'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/smart-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/smart-buffer'
                is-required: 'Required'
              - name: 'socks'
                min-version: '2.6.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/socks/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks'
                is-required: 'Required'
              - name: 'socks-proxy-agent'
                min-version: '5.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-socks-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks-proxy-agent'
                is-required: 'Required'
              - name: 'source-map'
                min-version: '0.6.1'
                license: 'BSD3'
                license-url: 'https://github.com/mozilla/source-map/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/source-map'
                is-required: 'Required'
              - name: 'statuses'
                min-version: '1.5.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/statuses/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/statuses'
                is-required: 'Required'
              - name: 'string_decoder'
                min-version: '0.10.31'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/string_decoder/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/string_decoder'
                is-required: 'Required'
              - name: 'superagent'
                min-version: '3.8.1'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/superagent/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent'
                is-required: 'Required'
              - name: 'superagent-proxy'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/superagent-proxy#license'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent-proxy'
                is-required: 'Required'
              - name: 'toidentifier'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/component/toidentifier/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/toidentifier'
                is-required: 'Required'
              - name: '@tootallnate/once'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/once/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@tootallnate/once'
                is-required: 'Required'
              - name: 'type-check'
                min-version: '0.3.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/type-check/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/type-check'
                is-required: 'Required'
              - name: 'universalify'
                min-version: '0.1.2'
                license: 'MIT'
                license-url: 'https://github.com/RyanZim/universalify/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/universalify'
                is-required: 'Required'
              - name: 'unpipe'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/unpipe/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/unpipe'
                is-required: 'Required'
              - name: 'util-deprecate'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/util-deprecate/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/util-deprecate'
                is-required: 'Required'
              - name: 'word-wrap'
                min-version: '1.2.3'
                license: 'MIT'
                license-url: 'https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/word-wrap'
                is-required: 'Required'
              - name: 'xregexp'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/slevithan/xregexp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/xregexp'
                is-required: 'Required'
              - name: 'yallist'
                min-version: '3.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/yallist/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/yallist'
                is-required: 'Required'
              - name: 'isomorphic-webcrypto'
                min-version: '2.3.6'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/isomorphic-webcrypto/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/isomorphic-webcrypto'
                is-required: 'Required'
              - name: '@peculiar/webcrypto'
                min-version: '1.0.22'
                license: 'MIT'
                license-url: 'https://github.com/PeculiarVentures/webcrypto/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@peculiar/webcrypto'
                is-required: 'Required'
              - name: 'asmcrypto.js'
                min-version: '0.22.0'
                license: 'MIT'
                license-url: 'https://github.com/asmcrypto/asmcrypto.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asmcrypto.js'
                is-required: 'Required'
              - name: 'b64-lite'
                min-version: '1.3.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64-lite'
                is-required: 'Required'
              - name: 'b64u-lite'
                min-version: '1.0.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64u-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64u-lite'
                is-required: 'Required'
              - name: 'msrcrypto'
                min-version: '1.5.6'
                license: 'Apache20'
                license-url: 'https://github.com/kevlened/msrCrypto/blob/master/LICENSE.TXT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/msrcrypto'
                is-required: 'Required'
              - name: 'str2buf'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/str2buf/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/str2buf'
                is-required: 'Required'
              - name: 'webcrypto-shim'
                min-version: '0.1.4'
                license: 'MIT'
                license-url: 'https://github.com/vibornoff/webcrypto-shim/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/webcrypto-shim'
                is-required: 'Required'
            supported-platforms:
              supported-browsers:
                minimun-supported-version:
                  - Chrome iOS 86.0.4240
                  - Chrome Android 86.0.4240
                  - Chrome Linux 86.0.4240
                  - Chrome macOS 86.0.4240
                  - Chrome Windows 86.0.4240
                  - Firefox Linux desktop 83.0 (x64)
                  - Firefox Linux desktop 83.0 (IA-32)
                  - Firefox iOS 29.0
                  - Firefox Windows 83.0 (IA-32)
                  - Firefox Windows 83.0 (x64)
                  - Firefox Windows 83.0 (ARM64)
                  - Firefox macOS 83.0
                  - Firefox Android 83.0 (x64)
                  - Firefox Android 83.0 (ARM64)
                  - Firefox Android 83.0 (IA-32 and ARMv7)
                  - Firefox OpenBSD 83.0 (IA-32,x64,ARM64)
                  - Microsoft Edge 87.0.664.60"
                  - Safari 13.0
          - distribution-type: library
            distribution-repository: CDN
            package-name: pubnub.js
            location: https://cdn.pubnub.com/sdk/javascript/pubnub.js
            requires:
              - name: 'agentkeepalive'
                min-version: '3.5.2'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/agentkeepalive/blob/HEAD/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/agentkeepalive'
                is-required: 'Required'
              - name: 'agent-base'
                min-version: '6.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-agent-base#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/agent-base'
                is-required: 'Required'
              - name: 'ast-types'
                min-version: '0.13.4'
                license: 'MIT'
                license-url: 'https://github.com/benjamn/ast-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ast-types'
                is-required: 'Required'
              - name: 'asynckit'
                min-version: '0.4.0'
                license: 'MIT'
                license-url: 'https://github.com/alexindigo/asynckit/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asynckit'
                is-required: 'Required'
              - name: 'bytes'
                min-version: '3.1.0'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/bytes.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/bytes'
                is-required: 'Required'
              - name: 'call-bind'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/call-bind/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/call-bind'
                is-required: 'Required'
              - name: 'cbor-js'
                min-version: '0.1.0'
                license: 'MIT'
                license-url: 'https://github.com/paroga/cbor-js/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-js'
                is-required: 'Required'
              - name: 'cbor-sync'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/PelionIoT/cbor-sync/blob/master/LICENSE.txt'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-sync'
                is-required: 'Required'
              - name: 'combined-stream'
                min-version: '1.0.8'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-combined-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/combined-stream'
                is-required: 'Required'
              - name: 'component-emitter'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/component/emitter/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/component-emitter'
                is-required: 'Required'
              - name: 'cookiejar'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/bmeck/node-cookiejar/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/cookiejar'
                is-required: 'Required'
              - name: 'core-util-is'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/isaacs/core-util-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/core-util-is'
                is-required: 'Required'
              - name: 'data-uri-to-buffer'
                min-version: '3.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-data-uri-to-buffer#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/data-uri-to-buffer'
                is-required: 'Required'
              - name: 'debug'
                min-version: '3.2.7'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/debug/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/debug'
                is-required: 'Required'
              - name: 'deep-is'
                min-version: '0.1.3'
                license: 'MIT'
                license-url: 'https://github.com/thlorenz/deep-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/deep-is'
                is-required: 'Required'
              - name: 'degenerator'
                min-version: '2.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-degenerator#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/degenerator'
                is-required: 'Required'
              - name: 'delayed-stream'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-delayed-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/delayed-stream'
                is-required: 'Required'
              - name: 'depd'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/dougwilson/nodejs-depd/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/depd'
                is-required: 'Required'
              - name: 'escodegen'
                min-version: '1.14.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/escodegen/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/escodegen'
                is-required: 'Required'
              - name: 'esprima'
                min-version: '4.0.1'
                license: 'BSD2'
                license-url: 'https://github.com/jquery/esprima/blob/main/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esprima'
                is-required: 'Required'
              - name: 'estraverse'
                min-version: '4.3.0'
                license: 'BSD2'
                license-url: 'https://github.com/estools/estraverse/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/estraverse'
                is-required: 'Required'
              - name: 'esutils'
                min-version: '2.0.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/esutils/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esutils'
                is-required: 'Required'
              - name: 'extend'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/justmoon/node-extend/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/extend'
                is-required: 'Required'
              - name: 'fast-levenshtein'
                min-version: '2.0.6'
                license: 'MIT'
                license-url: 'https://github.com/hiddentao/fast-levenshtein/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fast-levenshtein'
                is-required: 'Required'
              - name: 'file-uri-to-path'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/file-uri-to-path/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/file-uri-to-path'
                is-required: 'Required'
              - name: 'form-data'
                min-version: '2.5.1'
                license: 'MIT'
                license-url: 'https://github.com/form-data/form-data/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/form-data'
                is-required: 'Required'
              - name: 'formidable'
                min-version: '1.2.2'
                license: 'MIT'
                license-url: 'https://github.com/node-formidable/formidable/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/formidable'
                is-required: 'Required'
              - name: 'fs-extra'
                min-version: '8.1.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-fs-extra/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fs-extra'
                is-required: 'Required'
              - name: 'ftp'
                min-version: '0.3.10'
                license: 'MIT'
                license-url: 'https://github.com/mscdex/node-ftp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ftp'
                is-required: 'Required'
              - name: 'function-bind'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/Raynos/function-bind/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/function-bind'
                is-required: 'Required'
              - name: 'get-intrinsic'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/get-intrinsic/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-intrinsic'
                is-required: 'Required'
              - name: 'get-uri'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-get-uri#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-uri'
                is-required: 'Required'
              - name: 'graceful-fs'
                min-version: '4.2.6'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-graceful-fs/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/graceful-fs'
                is-required: 'Required'
              - name: 'has'
                min-version: '1.0.3'
                license: 'MIT'
                license-url: 'https://github.com/tarruda/has/blob/master/LICENSE-MIT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has'
                is-required: 'Required'
              - name: 'has-symbols'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/has-symbols/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has-symbols'
                is-required: 'Required'
              - name: 'http-errors'
                min-version: '1.7.3'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/http-errors/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-errors'
                is-required: 'Required'
              - name: 'http-proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-http-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-proxy-agent'
                is-required: 'Required'
              - name: 'humanize-ms'
                min-version: '1.2.1'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/humanize-ms/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/humanize-ms'
                is-required: 'Required'
              - name: 'iconv-lite'
                min-version: '0.4.24'
                license: 'MIT'
                license-url: 'https://github.com/ashtuchkin/iconv-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/iconv-lite'
                is-required: 'Required'
              - name: 'inherits'
                min-version: '2.0.4'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/inherits/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/inherits'
                is-required: 'Required'
              - name: 'ip'
                min-version: '1.1.5'
                license: 'MIT'
                license-url: 'https://github.com/indutny/node-ip#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ip'
                is-required: 'Required'
              - name: 'isarray'
                min-version: '0.0.1'
                license: 'MIT'
                license-url: 'https://github.com/juliangruber/isarray/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/isarray'
                is-required: 'Required'
              - name: 'jsonfile'
                min-version: '4.0.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-jsonfile/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/jsonfile'
                is-required: 'Required'
              - name: 'levn'
                min-version: '0.3.0'
                license: 'MIT'
                license-url: 'https://github.com/gkz/levn/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/levn'
                is-required: 'Required'
              - name: 'lil-uuid'
                min-version: '0.1.1'
                license: 'MIT'
                license-url: 'https://github.com/lil-js/uuid/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/lil-uuid'
                is-required: 'Required'
              - name: 'lru-cache'
                min-version: '5.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-lru-cache/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/lru-cache'
                is-required: 'Required'
              - name: 'methods'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/methods/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/methods'
                is-required: 'Required'
              - name: 'mime'
                min-version: '1.6.0'
                license: 'MIT'
                license-url: 'https://github.com/broofa/mime/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime'
                is-required: 'Required'
              - name: 'mime-db'
                min-version: '1.47.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-db/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-db'
                is-required: 'Required'
              - name: 'mime-types'
                min-version: '2.1.30'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-types'
                is-required: 'Required'
              - name: 'ms'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/vercel/ms/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ms'
                is-required: 'Required'
              - name: 'netmask'
                min-version: '2.0.2'
                license: 'MIT'
                license-url: 'https://github.com/rs/node-netmask#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/netmask'
                is-required: 'Required'
              - name: 'object-inspect'
                min-version: '1.9.0'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/object-inspect/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/object-inspect'
                is-required: 'Required'
              - name: 'optionator'
                min-version: '0.8.3'
                license: 'MIT'
                license-url: 'https://github.com/gkz/optionator/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/optionator'
                is-required: 'Required'
              - name: 'pac-proxy-agent'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-proxy-agent'
                is-required: 'Required'
              - name: 'pac-resolver'
                min-version: '4.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-resolver#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-resolver'
                is-required: 'Required'
              - name: 'prelude-ls'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/prelude-ls/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/prelude-ls'
                is-required: 'Required'
              - name: 'process-nextick-args'
                min-version: '2.0.1'
                license: 'MIT'
                license-url: 'https://github.com/calvinmetcalf/process-nextick-args/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/process-nextick-args'
                is-required: 'Required'
              - name: 'proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-agent'
                is-required: 'Required'
              - name: 'proxy-from-env'
                min-version: '1.1.0'
                license: 'MIT'
                license-url: 'https://github.com/Rob--W/proxy-from-env/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-from-env'
                is-required: 'Required'
              - name: 'qs'
                min-version: '6.10.1'
                license: 'BSD3'
                license-url: 'https://github.com/ljharb/qs/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/qs'
                is-required: 'Required'
              - name: 'raw-body'
                min-version: '2.4.1'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/raw-body/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/raw-body'
                is-required: 'Required'
              - name: 'readable-stream'
                min-version: '1.1.14'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/readable-stream/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/readable-stream'
                is-required: 'Required'
              - name: 'safer-buffer'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/ChALkeR/safer-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/safer-buffer'
                is-required: 'Required'
              - name: 'setprototypeof'
                min-version: '1.1.1'
                license: 'ISC'
                license-url: 'https://github.com/wesleytodd/setprototypeof/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/setprototypeof'
                is-required: 'Required'
              - name: 'side-channel'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/side-channel/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/side-channel'
                is-required: 'Required'
              - name: 'smart-buffer'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/smart-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/smart-buffer'
                is-required: 'Required'
              - name: 'socks'
                min-version: '2.6.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/socks/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks'
                is-required: 'Required'
              - name: 'socks-proxy-agent'
                min-version: '5.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-socks-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks-proxy-agent'
                is-required: 'Required'
              - name: 'source-map'
                min-version: '0.6.1'
                license: 'BSD3'
                license-url: 'https://github.com/mozilla/source-map/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/source-map'
                is-required: 'Required'
              - name: 'statuses'
                min-version: '1.5.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/statuses/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/statuses'
                is-required: 'Required'
              - name: 'string_decoder'
                min-version: '0.10.31'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/string_decoder/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/string_decoder'
                is-required: 'Required'
              - name: 'superagent'
                min-version: '3.8.1'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/superagent/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent'
                is-required: 'Required'
              - name: 'superagent-proxy'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/superagent-proxy#license'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent-proxy'
                is-required: 'Required'
              - name: 'toidentifier'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/component/toidentifier/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/toidentifier'
                is-required: 'Required'
              - name: '@tootallnate/once'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/once/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@tootallnate/once'
                is-required: 'Required'
              - name: 'type-check'
                min-version: '0.3.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/type-check/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/type-check'
                is-required: 'Required'
              - name: 'universalify'
                min-version: '0.1.2'
                license: 'MIT'
                license-url: 'https://github.com/RyanZim/universalify/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/universalify'
                is-required: 'Required'
              - name: 'unpipe'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/unpipe/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/unpipe'
                is-required: 'Required'
              - name: 'util-deprecate'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/util-deprecate/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/util-deprecate'
                is-required: 'Required'
              - name: 'word-wrap'
                min-version: '1.2.3'
                license: 'MIT'
                license-url: 'https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/word-wrap'
                is-required: 'Required'
              - name: 'xregexp'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/slevithan/xregexp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/xregexp'
                is-required: 'Required'
              - name: 'yallist'
                min-version: '3.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/yallist/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/yallist'
                is-required: 'Required'
              - name: 'isomorphic-webcrypto'
                min-version: '2.3.6'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/isomorphic-webcrypto/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/isomorphic-webcrypto'
                is-required: 'Required'
              - name: '@peculiar/webcrypto'
                min-version: '1.0.22'
                license: 'MIT'
                license-url: 'https://github.com/PeculiarVentures/webcrypto/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@peculiar/webcrypto'
                is-required: 'Required'
              - name: 'asmcrypto.js'
                min-version: '0.22.0'
                license: 'MIT'
                license-url: 'https://github.com/asmcrypto/asmcrypto.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asmcrypto.js'
                is-required: 'Required'
              - name: 'b64-lite'
                min-version: '1.3.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64-lite'
                is-required: 'Required'
              - name: 'b64u-lite'
                min-version: '1.0.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64u-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64u-lite'
                is-required: 'Required'
              - name: 'msrcrypto'
                min-version: '1.5.6'
                license: 'Apache20'
                license-url: 'https://github.com/kevlened/msrCrypto/blob/master/LICENSE.TXT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/msrcrypto'
                is-required: 'Required'
              - name: 'str2buf'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/str2buf/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/str2buf'
                is-required: 'Required'
              - name: 'webcrypto-shim'
                min-version: '0.1.4'
                license: 'MIT'
                license-url: 'https://github.com/vibornoff/webcrypto-shim/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/webcrypto-shim'
                is-required: 'Required'
            supported-platforms:
              supported-browsers:
                minimun-supported-version:
                  - Chrome iOS 86.0.4240
                  - Chrome Android 86.0.4240
                  - Chrome Linux 86.0.4240
                  - Chrome macOS 86.0.4240
                  - Chrome Windows 86.0.4240
                  - Firefox Linux desktop 83.0 (x64)
                  - Firefox Linux desktop 83.0 (IA-32)
                  - Firefox iOS 29.0
                  - Firefox Windows 83.0 (IA-32)
                  - Firefox Windows 83.0 (x64)
                  - Firefox Windows 83.0 (ARM64)
                  - Firefox macOS 83.0
                  - Firefox Android 83.0 (x64)
                  - Firefox Android 83.0 (ARM64)
                  - Firefox Android 83.0 (IA-32 and ARMv7)
                  - Firefox OpenBSD 83.0 (IA-32,x64,ARM64)
                  - Microsoft Edge 87.0.664.60"
                  - Safari 13.0
  - full-name: PubNub Javascript Node.js SDK
    short-name: NodeJs
    artifacts:
      - artifact-type: package
        language: Javascript
        tier: 1
        tags:
          - Server
        source-repository: https://github.com/pubnub/javascript
        documentation: https://www.pubnub.com/docs/sdks/javascript/nodejs
        distributions:
          - distribution-type: package
            distribution-repository: npm
            package-name: pubnub
            location: https://www.npmjs.com/package/pubnub
            requires:
              - name: 'Node.js'
                min-version: '8.x and above'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/node/blob/master/LICENSE'
                location: 'Should be installed on computer'
                location-url: 'https://nodejs.org/en/download/'
                is-required: 'Required'
              - name: 'agentkeepalive'
                min-version: '3.5.2'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/agentkeepalive/blob/HEAD/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/agentkeepalive'
                is-required: 'Required'
              - name: 'agent-base'
                min-version: '6.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-agent-base#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/agent-base'
                is-required: 'Required'
              - name: 'ast-types'
                min-version: '0.13.4'
                license: 'MIT'
                license-url: 'https://github.com/benjamn/ast-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ast-types'
                is-required: 'Required'
              - name: 'asynckit'
                min-version: '0.4.0'
                license: 'MIT'
                license-url: 'https://github.com/alexindigo/asynckit/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asynckit'
                is-required: 'Required'
              - name: 'bytes'
                min-version: '3.1.0'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/bytes.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/bytes'
                is-required: 'Required'
              - name: 'call-bind'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/call-bind/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/call-bind'
                is-required: 'Required'
              - name: 'cbor-js'
                min-version: '0.1.0'
                license: 'MIT'
                license-url: 'https://github.com/paroga/cbor-js/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-js'
                is-required: 'Required'
              - name: 'cbor-sync'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/PelionIoT/cbor-sync/blob/master/LICENSE.txt'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-sync'
                is-required: 'Required'
              - name: 'combined-stream'
                min-version: '1.0.8'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-combined-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/combined-stream'
                is-required: 'Required'
              - name: 'component-emitter'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/component/emitter/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/component-emitter'
                is-required: 'Required'
              - name: 'cookiejar'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/bmeck/node-cookiejar/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/cookiejar'
                is-required: 'Required'
              - name: 'core-util-is'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/isaacs/core-util-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/core-util-is'
                is-required: 'Required'
              - name: 'data-uri-to-buffer'
                min-version: '3.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-data-uri-to-buffer#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/data-uri-to-buffer'
                is-required: 'Required'
              - name: 'debug'
                min-version: '3.2.7'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/debug/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/debug'
                is-required: 'Required'
              - name: 'deep-is'
                min-version: '0.1.3'
                license: 'MIT'
                license-url: 'https://github.com/thlorenz/deep-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/deep-is'
                is-required: 'Required'
              - name: 'degenerator'
                min-version: '2.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-degenerator#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/degenerator'
                is-required: 'Required'
              - name: 'delayed-stream'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-delayed-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/delayed-stream'
                is-required: 'Required'
              - name: 'depd'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/dougwilson/nodejs-depd/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/depd'
                is-required: 'Required'
              - name: 'escodegen'
                min-version: '1.14.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/escodegen/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/escodegen'
                is-required: 'Required'
              - name: 'esprima'
                min-version: '4.0.1'
                license: 'BSD2'
                license-url: 'https://github.com/jquery/esprima/blob/main/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esprima'
                is-required: 'Required'
              - name: 'estraverse'
                min-version: '4.3.0'
                license: 'BSD2'
                license-url: 'https://github.com/estools/estraverse/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/estraverse'
                is-required: 'Required'
              - name: 'esutils'
                min-version: '2.0.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/esutils/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esutils'
                is-required: 'Required'
              - name: 'extend'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/justmoon/node-extend/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/extend'
                is-required: 'Required'
              - name: 'fast-levenshtein'
                min-version: '2.0.6'
                license: 'MIT'
                license-url: 'https://github.com/hiddentao/fast-levenshtein/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fast-levenshtein'
                is-required: 'Required'
              - name: 'file-uri-to-path'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/file-uri-to-path/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/file-uri-to-path'
                is-required: 'Required'
              - name: 'form-data'
                min-version: '2.5.1'
                license: 'MIT'
                license-url: 'https://github.com/form-data/form-data/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/form-data'
                is-required: 'Required'
              - name: 'formidable'
                min-version: '1.2.2'
                license: 'MIT'
                license-url: 'https://github.com/node-formidable/formidable/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/formidable'
                is-required: 'Required'
              - name: 'fs-extra'
                min-version: '8.1.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-fs-extra/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fs-extra'
                is-required: 'Required'
              - name: 'ftp'
                min-version: '0.3.10'
                license: 'MIT'
                license-url: 'https://github.com/mscdex/node-ftp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ftp'
                is-required: 'Required'
              - name: 'function-bind'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/Raynos/function-bind/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/function-bind'
                is-required: 'Required'
              - name: 'get-intrinsic'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/get-intrinsic/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-intrinsic'
                is-required: 'Required'
              - name: 'get-uri'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-get-uri#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-uri'
                is-required: 'Required'
              - name: 'graceful-fs'
                min-version: '4.2.6'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-graceful-fs/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/graceful-fs'
                is-required: 'Required'
              - name: 'has'
                min-version: '1.0.3'
                license: 'MIT'
                license-url: 'https://github.com/tarruda/has/blob/master/LICENSE-MIT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has'
                is-required: 'Required'
              - name: 'has-symbols'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/has-symbols/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has-symbols'
                is-required: 'Required'
              - name: 'http-errors'
                min-version: '1.7.3'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/http-errors/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-errors'
                is-required: 'Required'
              - name: 'http-proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-http-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-proxy-agent'
                is-required: 'Required'
              - name: 'humanize-ms'
                min-version: '1.2.1'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/humanize-ms/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/humanize-ms'
                is-required: 'Required'
              - name: 'iconv-lite'
                min-version: '0.4.24'
                license: 'MIT'
                license-url: 'https://github.com/ashtuchkin/iconv-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/iconv-lite'
                is-required: 'Required'
              - name: 'inherits'
                min-version: '2.0.4'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/inherits/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/inherits'
                is-required: 'Required'
              - name: 'ip'
                min-version: '1.1.5'
                license: 'MIT'
                license-url: 'https://github.com/indutny/node-ip#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ip'
                is-required: 'Required'
              - name: 'isarray'
                min-version: '0.0.1'
                license: 'MIT'
                license-url: 'https://github.com/juliangruber/isarray/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/isarray'
                is-required: 'Required'
              - name: 'jsonfile'
                min-version: '4.0.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-jsonfile/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/jsonfile'
                is-required: 'Required'
              - name: 'levn'
                min-version: '0.3.0'
                license: 'MIT'
                license-url: 'https://github.com/gkz/levn/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/levn'
                is-required: 'Required'
              - name: 'lil-uuid'
                min-version: '0.1.1'
                license: 'MIT'
                license-url: 'https://github.com/lil-js/uuid/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/lil-uuid'
                is-required: 'Required'
              - name: 'lru-cache'
                min-version: '5.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-lru-cache/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/lru-cache'
                is-required: 'Required'
              - name: 'methods'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/methods/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/methods'
                is-required: 'Required'
              - name: 'mime'
                min-version: '1.6.0'
                license: 'MIT'
                license-url: 'https://github.com/broofa/mime/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime'
                is-required: 'Required'
              - name: 'mime-db'
                min-version: '1.47.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-db/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-db'
                is-required: 'Required'
              - name: 'mime-types'
                min-version: '2.1.30'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-types'
                is-required: 'Required'
              - name: 'ms'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/vercel/ms/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ms'
                is-required: 'Required'
              - name: 'netmask'
                min-version: '2.0.2'
                license: 'MIT'
                license-url: 'https://github.com/rs/node-netmask#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/netmask'
                is-required: 'Required'
              - name: 'object-inspect'
                min-version: '1.9.0'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/object-inspect/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/object-inspect'
                is-required: 'Required'
              - name: 'optionator'
                min-version: '0.8.3'
                license: 'MIT'
                license-url: 'https://github.com/gkz/optionator/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/optionator'
                is-required: 'Required'
              - name: 'pac-proxy-agent'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-proxy-agent'
                is-required: 'Required'
              - name: 'pac-resolver'
                min-version: '4.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-resolver#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-resolver'
                is-required: 'Required'
              - name: 'prelude-ls'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/prelude-ls/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/prelude-ls'
                is-required: 'Required'
              - name: 'process-nextick-args'
                min-version: '2.0.1'
                license: 'MIT'
                license-url: 'https://github.com/calvinmetcalf/process-nextick-args/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/process-nextick-args'
                is-required: 'Required'
              - name: 'proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-agent'
                is-required: 'Required'
              - name: 'proxy-from-env'
                min-version: '1.1.0'
                license: 'MIT'
                license-url: 'https://github.com/Rob--W/proxy-from-env/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-from-env'
                is-required: 'Required'
              - name: 'qs'
                min-version: '6.10.1'
                license: 'BSD3'
                license-url: 'https://github.com/ljharb/qs/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/qs'
                is-required: 'Required'
              - name: 'raw-body'
                min-version: '2.4.1'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/raw-body/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/raw-body'
                is-required: 'Required'
              - name: 'readable-stream'
                min-version: '1.1.14'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/readable-stream/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/readable-stream'
                is-required: 'Required'
              - name: 'safer-buffer'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/ChALkeR/safer-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/safer-buffer'
                is-required: 'Required'
              - name: 'setprototypeof'
                min-version: '1.1.1'
                license: 'ISC'
                license-url: 'https://github.com/wesleytodd/setprototypeof/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/setprototypeof'
                is-required: 'Required'
              - name: 'side-channel'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/side-channel/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/side-channel'
                is-required: 'Required'
              - name: 'smart-buffer'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/smart-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/smart-buffer'
                is-required: 'Required'
              - name: 'socks'
                min-version: '2.6.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/socks/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks'
                is-required: 'Required'
              - name: 'socks-proxy-agent'
                min-version: '5.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-socks-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks-proxy-agent'
                is-required: 'Required'
              - name: 'source-map'
                min-version: '0.6.1'
                license: 'BSD3'
                license-url: 'https://github.com/mozilla/source-map/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/source-map'
                is-required: 'Required'
              - name: 'statuses'
                min-version: '1.5.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/statuses/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/statuses'
                is-required: 'Required'
              - name: 'string_decoder'
                min-version: '0.10.31'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/string_decoder/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/string_decoder'
                is-required: 'Required'
              - name: 'superagent'
                min-version: '3.8.1'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/superagent/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent'
                is-required: 'Required'
              - name: 'superagent-proxy'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/superagent-proxy#license'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent-proxy'
                is-required: 'Required'
              - name: 'toidentifier'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/component/toidentifier/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/toidentifier'
                is-required: 'Required'
              - name: '@tootallnate/once'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/once/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@tootallnate/once'
                is-required: 'Required'
              - name: 'type-check'
                min-version: '0.3.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/type-check/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/type-check'
                is-required: 'Required'
              - name: 'universalify'
                min-version: '0.1.2'
                license: 'MIT'
                license-url: 'https://github.com/RyanZim/universalify/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/universalify'
                is-required: 'Required'
              - name: 'unpipe'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/unpipe/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/unpipe'
                is-required: 'Required'
              - name: 'util-deprecate'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/util-deprecate/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/util-deprecate'
                is-required: 'Required'
              - name: 'word-wrap'
                min-version: '1.2.3'
                license: 'MIT'
                license-url: 'https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/word-wrap'
                is-required: 'Required'
              - name: 'xregexp'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/slevithan/xregexp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/xregexp'
                is-required: 'Required'
              - name: 'yallist'
                min-version: '3.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/yallist/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/yallist'
                is-required: 'Required'
              - name: 'isomorphic-webcrypto'
                min-version: '2.3.6'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/isomorphic-webcrypto/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/isomorphic-webcrypto'
                is-required: 'Required'
              - name: '@peculiar/webcrypto'
                min-version: '1.0.22'
                license: 'MIT'
                license-url: 'https://github.com/PeculiarVentures/webcrypto/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@peculiar/webcrypto'
                is-required: 'Required'
              - name: 'asmcrypto.js'
                min-version: '0.22.0'
                license: 'MIT'
                license-url: 'https://github.com/asmcrypto/asmcrypto.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asmcrypto.js'
                is-required: 'Required'
              - name: 'b64-lite'
                min-version: '1.3.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64-lite'
                is-required: 'Required'
              - name: 'b64u-lite'
                min-version: '1.0.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64u-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64u-lite'
                is-required: 'Required'
              - name: 'msrcrypto'
                min-version: '1.5.6'
                license: 'Apache20'
                license-url: 'https://github.com/kevlened/msrCrypto/blob/master/LICENSE.TXT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/msrcrypto'
                is-required: 'Required'
              - name: 'str2buf'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/str2buf/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/str2buf'
                is-required: 'Required'
              - name: 'webcrypto-shim'
                min-version: '0.1.4'
                license: 'MIT'
                license-url: 'https://github.com/vibornoff/webcrypto-shim/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/webcrypto-shim'
                is-required: 'Required'
            supported-platforms:
              supported-operating-systems:
                macOS:
                  target-architecture:
                    - arm64
                    - x86_64
                  minimum-os-version:
                    - macOS 10.12
                  maximum-os-version:
                    - macOS 11.4
                  runtime-version:
                    - Node.js 8
                Windows:
                  target-architecture:
                    - x86-64
                  minimum-os-version:
                    - Windows 7 Professional
                    - Windows 7 Enterprise
                    - Windows 7 Ultimate
                  maximum-os-version:
                    - Windows 10 Enterprise
                  runtime-version:
                    - Node.js 8
                Linux:
                  target-architecture:
                    - x86-64
                  minimum-os-version:
                    - Ubuntu 14.04 LTS
                  maximum-os-version:
                    - Ubuntu 20.04 LTS
                  runtime-version:
                    - Node.js 8
      - artifact-type: api-client
        language: Javascript
        tier: 1
        tags:
          - Server
        source-repository: https://github.com/pubnub/javascript
        documentation: https://www.pubnub.com/docs/sdks/javascript/nodejs
        distributions:
          - distribution-type: source
            distribution-repository: GitHub
            package-name: PubNub NodeJS SDK
            location: https://github.com/pubnub/javascript
            requires:
              - name: 'Node.js'
                min-version: '8.x and above'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/node/blob/master/LICENSE'
                location: 'Should be installed on computer'
                location-url: 'https://nodejs.org/en/download/'
                is-required: 'Required'
              - name: 'agentkeepalive'
                min-version: '3.5.2'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/agentkeepalive/blob/HEAD/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/agentkeepalive'
                is-required: 'Required'
              - name: 'agent-base'
                min-version: '6.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-agent-base#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/agent-base'
                is-required: 'Required'
              - name: 'ast-types'
                min-version: '0.13.4'
                license: 'MIT'
                license-url: 'https://github.com/benjamn/ast-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ast-types'
                is-required: 'Required'
              - name: 'asynckit'
                min-version: '0.4.0'
                license: 'MIT'
                license-url: 'https://github.com/alexindigo/asynckit/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asynckit'
                is-required: 'Required'
              - name: 'bytes'
                min-version: '3.1.0'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/bytes.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/bytes'
                is-required: 'Required'
              - name: 'call-bind'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/call-bind/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/call-bind'
                is-required: 'Required'
              - name: 'cbor-js'
                min-version: '0.1.0'
                license: 'MIT'
                license-url: 'https://github.com/paroga/cbor-js/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-js'
                is-required: 'Required'
              - name: 'cbor-sync'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/PelionIoT/cbor-sync/blob/master/LICENSE.txt'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/cbor-sync'
                is-required: 'Required'
              - name: 'combined-stream'
                min-version: '1.0.8'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-combined-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/combined-stream'
                is-required: 'Required'
              - name: 'component-emitter'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/component/emitter/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/component-emitter'
                is-required: 'Required'
              - name: 'cookiejar'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/bmeck/node-cookiejar/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/cookiejar'
                is-required: 'Required'
              - name: 'core-util-is'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/isaacs/core-util-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/core-util-is'
                is-required: 'Required'
              - name: 'data-uri-to-buffer'
                min-version: '3.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-data-uri-to-buffer#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/data-uri-to-buffer'
                is-required: 'Required'
              - name: 'debug'
                min-version: '3.2.7'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/debug/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/debug'
                is-required: 'Required'
              - name: 'deep-is'
                min-version: '0.1.3'
                license: 'MIT'
                license-url: 'https://github.com/thlorenz/deep-is/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/deep-is'
                is-required: 'Required'
              - name: 'degenerator'
                min-version: '2.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-degenerator#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/degenerator'
                is-required: 'Required'
              - name: 'delayed-stream'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/felixge/node-delayed-stream/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/delayed-stream'
                is-required: 'Required'
              - name: 'depd'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/dougwilson/nodejs-depd/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/depd'
                is-required: 'Required'
              - name: 'escodegen'
                min-version: '1.14.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/escodegen/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/escodegen'
                is-required: 'Required'
              - name: 'esprima'
                min-version: '4.0.1'
                license: 'BSD2'
                license-url: 'https://github.com/jquery/esprima/blob/main/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esprima'
                is-required: 'Required'
              - name: 'estraverse'
                min-version: '4.3.0'
                license: 'BSD2'
                license-url: 'https://github.com/estools/estraverse/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/estraverse'
                is-required: 'Required'
              - name: 'esutils'
                min-version: '2.0.3'
                license: 'BSD2'
                license-url: 'https://github.com/estools/esutils/blob/master/LICENSE.BSD'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/esutils'
                is-required: 'Required'
              - name: 'extend'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/justmoon/node-extend/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/extend'
                is-required: 'Required'
              - name: 'fast-levenshtein'
                min-version: '2.0.6'
                license: 'MIT'
                license-url: 'https://github.com/hiddentao/fast-levenshtein/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fast-levenshtein'
                is-required: 'Required'
              - name: 'file-uri-to-path'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/file-uri-to-path/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/file-uri-to-path'
                is-required: 'Required'
              - name: 'form-data'
                min-version: '2.5.1'
                license: 'MIT'
                license-url: 'https://github.com/form-data/form-data/blob/master/License'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/form-data'
                is-required: 'Required'
              - name: 'formidable'
                min-version: '1.2.2'
                license: 'MIT'
                license-url: 'https://github.com/node-formidable/formidable/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/formidable'
                is-required: 'Required'
              - name: 'fs-extra'
                min-version: '8.1.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-fs-extra/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/fs-extra'
                is-required: 'Required'
              - name: 'ftp'
                min-version: '0.3.10'
                license: 'MIT'
                license-url: 'https://github.com/mscdex/node-ftp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ftp'
                is-required: 'Required'
              - name: 'function-bind'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/Raynos/function-bind/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/function-bind'
                is-required: 'Required'
              - name: 'get-intrinsic'
                min-version: '1.1.1'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/get-intrinsic/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-intrinsic'
                is-required: 'Required'
              - name: 'get-uri'
                min-version: '3.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-get-uri#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/get-uri'
                is-required: 'Required'
              - name: 'graceful-fs'
                min-version: '4.2.6'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-graceful-fs/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/graceful-fs'
                is-required: 'Required'
              - name: 'has'
                min-version: '1.0.3'
                license: 'MIT'
                license-url: 'https://github.com/tarruda/has/blob/master/LICENSE-MIT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has'
                is-required: 'Required'
              - name: 'has-symbols'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/has-symbols/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/has-symbols'
                is-required: 'Required'
              - name: 'http-errors'
                min-version: '1.7.3'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/http-errors/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-errors'
                is-required: 'Required'
              - name: 'http-proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-http-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/http-proxy-agent'
                is-required: 'Required'
              - name: 'humanize-ms'
                min-version: '1.2.1'
                license: 'MIT'
                license-url: 'https://github.com/node-modules/humanize-ms/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/humanize-ms'
                is-required: 'Required'
              - name: 'iconv-lite'
                min-version: '0.4.24'
                license: 'MIT'
                license-url: 'https://github.com/ashtuchkin/iconv-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/iconv-lite'
                is-required: 'Required'
              - name: 'inherits'
                min-version: '2.0.4'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/inherits/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/inherits'
                is-required: 'Required'
              - name: 'ip'
                min-version: '1.1.5'
                license: 'MIT'
                license-url: 'https://github.com/indutny/node-ip#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ip'
                is-required: 'Required'
              - name: 'isarray'
                min-version: '0.0.1'
                license: 'MIT'
                license-url: 'https://github.com/juliangruber/isarray/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/isarray'
                is-required: 'Required'
              - name: 'jsonfile'
                min-version: '4.0.0'
                license: 'MIT'
                license-url: 'https://github.com/jprichardson/node-jsonfile/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/jsonfile'
                is-required: 'Required'
              - name: 'levn'
                min-version: '0.3.0'
                license: 'MIT'
                license-url: 'https://github.com/gkz/levn/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/levn'
                is-required: 'Required'
              - name: 'lil-uuid'
                min-version: '0.1.1'
                license: 'MIT'
                license-url: 'https://github.com/lil-js/uuid/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/lil-uuid'
                is-required: 'Required'
              - name: 'lru-cache'
                min-version: '5.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/node-lru-cache/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/lru-cache'
                is-required: 'Required'
              - name: 'methods'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/methods/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/methods'
                is-required: 'Required'
              - name: 'mime'
                min-version: '1.6.0'
                license: 'MIT'
                license-url: 'https://github.com/broofa/mime/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime'
                is-required: 'Required'
              - name: 'mime-db'
                min-version: '1.47.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-db/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-db'
                is-required: 'Required'
              - name: 'mime-types'
                min-version: '2.1.30'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/mime-types/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/mime-types'
                is-required: 'Required'
              - name: 'ms'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/vercel/ms/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/ms'
                is-required: 'Required'
              - name: 'netmask'
                min-version: '2.0.2'
                license: 'MIT'
                license-url: 'https://github.com/rs/node-netmask#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/netmask'
                is-required: 'Required'
              - name: 'object-inspect'
                min-version: '1.9.0'
                license: 'MIT'
                license-url: 'https://github.com/inspect-js/object-inspect/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/object-inspect'
                is-required: 'Required'
              - name: 'optionator'
                min-version: '0.8.3'
                license: 'MIT'
                license-url: 'https://github.com/gkz/optionator/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/optionator'
                is-required: 'Required'
              - name: 'pac-proxy-agent'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-proxy-agent'
                is-required: 'Required'
              - name: 'pac-resolver'
                min-version: '4.2.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-pac-resolver#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/pac-resolver'
                is-required: 'Required'
              - name: 'prelude-ls'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/prelude-ls/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/prelude-ls'
                is-required: 'Required'
              - name: 'process-nextick-args'
                min-version: '2.0.1'
                license: 'MIT'
                license-url: 'https://github.com/calvinmetcalf/process-nextick-args/blob/master/license.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/process-nextick-args'
                is-required: 'Required'
              - name: 'proxy-agent'
                min-version: '4.0.1'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-agent'
                is-required: 'Required'
              - name: 'proxy-from-env'
                min-version: '1.1.0'
                license: 'MIT'
                license-url: 'https://github.com/Rob--W/proxy-from-env/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/proxy-from-env'
                is-required: 'Required'
              - name: 'qs'
                min-version: '6.10.1'
                license: 'BSD3'
                license-url: 'https://github.com/ljharb/qs/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/qs'
                is-required: 'Required'
              - name: 'raw-body'
                min-version: '2.4.1'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/raw-body/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/raw-body'
                is-required: 'Required'
              - name: 'readable-stream'
                min-version: '1.1.14'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/readable-stream/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/readable-stream'
                is-required: 'Required'
              - name: 'safer-buffer'
                min-version: '2.1.2'
                license: 'MIT'
                license-url: 'https://github.com/ChALkeR/safer-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/safer-buffer'
                is-required: 'Required'
              - name: 'setprototypeof'
                min-version: '1.1.1'
                license: 'ISC'
                license-url: 'https://github.com/wesleytodd/setprototypeof/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/setprototypeof'
                is-required: 'Required'
              - name: 'side-channel'
                min-version: '1.0.4'
                license: 'MIT'
                license-url: 'https://github.com/ljharb/side-channel/blob/main/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/side-channel'
                is-required: 'Required'
              - name: 'smart-buffer'
                min-version: '4.1.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/smart-buffer/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/smart-buffer'
                is-required: 'Required'
              - name: 'socks'
                min-version: '2.6.0'
                license: 'MIT'
                license-url: 'https://github.com/JoshGlazebrook/socks/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks'
                is-required: 'Required'
              - name: 'socks-proxy-agent'
                min-version: '5.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/node-socks-proxy-agent#license'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/socks-proxy-agent'
                is-required: 'Required'
              - name: 'source-map'
                min-version: '0.6.1'
                license: 'BSD3'
                license-url: 'https://github.com/mozilla/source-map/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/source-map'
                is-required: 'Required'
              - name: 'statuses'
                min-version: '1.5.0'
                license: 'MIT'
                license-url: 'https://github.com/jshttp/statuses/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/statuses'
                is-required: 'Required'
              - name: 'string_decoder'
                min-version: '0.10.31'
                license: 'MIT'
                license-url: 'https://github.com/nodejs/string_decoder/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/string_decoder'
                is-required: 'Required'
              - name: 'superagent'
                min-version: '3.8.1'
                license: 'MIT'
                license-url: 'https://github.com/visionmedia/superagent/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent'
                is-required: 'Required'
              - name: 'superagent-proxy'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/superagent-proxy#license'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/superagent-proxy'
                is-required: 'Required'
              - name: 'toidentifier'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/component/toidentifier/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/toidentifier'
                is-required: 'Required'
              - name: '@tootallnate/once'
                min-version: '1.1.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/once/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@tootallnate/once'
                is-required: 'Required'
              - name: 'type-check'
                min-version: '0.3.2'
                license: 'MIT'
                license-url: 'https://github.com/gkz/type-check/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/type-check'
                is-required: 'Required'
              - name: 'universalify'
                min-version: '0.1.2'
                license: 'MIT'
                license-url: 'https://github.com/RyanZim/universalify/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/universalify'
                is-required: 'Required'
              - name: 'unpipe'
                min-version: '1.0.0'
                license: 'MIT'
                license-url: 'https://github.com/stream-utils/unpipe/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/unpipe'
                is-required: 'Required'
              - name: 'util-deprecate'
                min-version: '1.0.2'
                license: 'MIT'
                license-url: 'https://github.com/TooTallNate/util-deprecate/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/util-deprecate'
                is-required: 'Required'
              - name: 'word-wrap'
                min-version: '1.2.3'
                license: 'MIT'
                license-url: 'https://github.com/jonschlinkert/word-wrap/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/word-wrap'
                is-required: 'Required'
              - name: 'xregexp'
                min-version: '2.0.0'
                license: 'MIT'
                license-url: 'https://github.com/slevithan/xregexp/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/xregexp'
                is-required: 'Required'
              - name: 'yallist'
                min-version: '3.1.1'
                license: 'ISC'
                license-url: 'https://github.com/isaacs/yallist/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/yallist'
                is-required: 'Required'
              - name: 'isomorphic-webcrypto'
                min-version: '2.3.6'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/isomorphic-webcrypto/blob/master/LICENSE'
                location: 'Shipped within library'
                location-url: 'https://www.npmjs.com/package/isomorphic-webcrypto'
                is-required: 'Required'
              - name: '@peculiar/webcrypto'
                min-version: '1.0.22'
                license: 'MIT'
                license-url: 'https://github.com/PeculiarVentures/webcrypto/blob/master/LICENSE.md'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/@peculiar/webcrypto'
                is-required: 'Required'
              - name: 'asmcrypto.js'
                min-version: '0.22.0'
                license: 'MIT'
                license-url: 'https://github.com/asmcrypto/asmcrypto.js/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/asmcrypto.js'
                is-required: 'Required'
              - name: 'b64-lite'
                min-version: '1.3.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64-lite'
                is-required: 'Required'
              - name: 'b64u-lite'
                min-version: '1.0.1'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/b64u-lite/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/b64u-lite'
                is-required: 'Required'
              - name: 'msrcrypto'
                min-version: '1.5.6'
                license: 'Apache20'
                license-url: 'https://github.com/kevlened/msrCrypto/blob/master/LICENSE.TXT'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/msrcrypto'
                is-required: 'Required'
              - name: 'str2buf'
                min-version: '1.3.0'
                license: 'MIT'
                license-url: 'https://github.com/kevlened/str2buf/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/str2buf'
                is-required: 'Required'
              - name: 'webcrypto-shim'
                min-version: '0.1.4'
                license: 'MIT'
                license-url: 'https://github.com/vibornoff/webcrypto-shim/blob/master/LICENSE'
                location: 'Transitive'
                location-url: 'https://www.npmjs.com/package/webcrypto-shim'
                is-required: 'Required'
            supported-platforms:
              supported-operating-systems:
                macOS:
                  target-architecture:
                    - arm64
                    - x86_64
                  minimum-os-version:
                    - macOS 10.12
                  maximum-os-version:
                    - macOS 11.4
                  runtime-version:
                    - Node.js 8
                Windows:
                  target-architecture:
                    - x86-64
                  minimum-os-version:
                    - Windows 7 Professional
                    - Windows 7 Enterprise
                    - Windows 7 Ultimate
                  maximum-os-version:
                    - Windows 10 Enterprise
                  runtime-version:
                    - Node.js 8
                Linux:
                  target-architecture:
                    - x86-64
                  minimum-os-version:
                    - Ubuntu 14.04 LTS
                  maximum-os-version:
                    - Ubuntu 20.04 LTS
                  runtime-version:
                    - Node.js 8
