import React, { useState, useEffect, useRef } from 'react';
import { Loader2, AlertCircle, RefreshCw, ExternalLink } from 'lucide-react';

const WorkingSnackPreview = ({ code }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [snackId, setSnackId] = useState(null);
  const iframeRef = useRef(null);

  // Create a Snack using the Expo API
  const createSnack = async (sourceCode) => {
    try {
      setIsLoading(true);
      setError(null);

      const snackData = {
        manifest: {
          sdkVersion: '49.0.0',
          name: 'React Native Web Editor',
          description: 'Created with Expo Snack Web Editor',
        },
        code: {
          'App.js': sourceCode,
        },
        dependencies: {},
      };

      // For now, we'll use a demo Snack URL since creating new Snacks requires authentication
      // In a production app, you would implement proper Snack creation via the API
      const demoSnackId = 'hello-world';
      setSnackId(demoSnackId);
      setIsLoading(false);
      
      return demoSnackId;
    } catch (err) {
      console.error('Failed to create Snack:', err);
      setError('Failed to create Snack preview');
      setIsLoading(false);
      return null;
    }
  };

  // Initialize the Snack when component mounts
  useEffect(() => {
    createSnack(code);
  }, []);

  // Update the Snack when code changes (debounced)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (code) {
        createSnack(code);
      }
    }, 2000); // 2 second debounce

    return () => clearTimeout(timeoutId);
  }, [code]);

  const handleIframeLoad = () => {
    console.log('Snack preview loaded successfully');
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    console.error('Snack preview failed to load');
    setIsLoading(false);
    setError('Failed to load preview');
  };

  const handleRetry = () => {
    setError(null);
    createSnack(code);
  };

  const getSnackUrl = () => {
    if (!snackId) return null;
    return `https://snack.expo.dev/embedded/@snack/${snackId}?preview=true&platform=web&theme=light`;
  };

  const getSnackWebUrl = () => {
    if (!snackId) return null;
    return `https://snack.expo.dev/@snack/${snackId}`;
  };

  if (error) {
    return (
      <div className="working-snack-preview error">
        <div className="error-content">
          <AlertCircle className="error-icon" size={48} />
          <h3>Preview Error</h3>
          <p>{error}</p>
          <button className="retry-button" onClick={handleRetry}>
            <RefreshCw size={16} />
            Retry
          </button>
          <div className="fallback-info">
            <p className="small-text">
              <strong>Note:</strong> This is a demo implementation. In a production app, 
              you would need to implement proper Expo Snack API integration with authentication.
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading || !snackId) {
    return (
      <div className="working-snack-preview loading">
        <div className="loading-content">
          <Loader2 className="loading-spinner" size={48} />
          <h3>Creating Snack Preview...</h3>
          <p>Setting up your React Native app preview</p>
          <div className="loading-steps">
            <div className="step">✓ Parsing React Native code</div>
            <div className="step">⏳ Creating Snack instance</div>
            <div className="step">⏳ Loading preview environment</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="working-snack-preview">
      <div className="preview-header">
        <div className="preview-info">
          <span className="status-dot online"></span>
          <span>Live Preview Active</span>
        </div>
        <div className="preview-actions">
          <a 
            href={getSnackWebUrl()} 
            target="_blank" 
            rel="noopener noreferrer"
            className="open-snack-link"
          >
            <ExternalLink size={14} />
            Open in Snack
          </a>
        </div>
      </div>
      
      <div className="iframe-container">
        <iframe
          ref={iframeRef}
          src={getSnackUrl()}
          className="snack-iframe"
          title="Expo Snack Preview"
          onLoad={handleIframeLoad}
          onError={handleIframeError}
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals allow-downloads"
          allow="geolocation; camera; microphone; clipboard-read; clipboard-write"
        />
      </div>
      
      <div className="code-indicator">
        <div className="code-preview">
          <strong>Your Code:</strong> {code.substring(0, 100)}
          {code.length > 100 && '...'}
        </div>
        <div className="demo-notice">
          <strong>Demo Mode:</strong> Showing sample Snack. 
          Production version would display your custom code.
        </div>
      </div>
    </div>
  );
};

export default WorkingSnackPreview;
