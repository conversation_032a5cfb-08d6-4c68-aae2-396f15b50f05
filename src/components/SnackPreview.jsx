import React, { useState } from 'react';
import { Loader2, Smartphone, AlertCircle, Monitor, QrCode } from 'lucide-react';

const SnackPreview = ({ previewUrl, webPreviewUrl, webPreviewRef, isLoading, error }) => {
  const [previewMode, setPreviewMode] = useState('web'); // 'web' or 'mobile'
  if (isLoading) {
    return (
      <div className="preview-container loading">
        <div className="loading-content">
          <Loader2 className="loading-spinner" size={48} />
          <h3>Initializing Expo Snack...</h3>
          <p>Setting up your React Native preview environment</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="preview-container error">
        <div className="error-content">
          <AlertCircle className="error-icon" size={48} />
          <h3>Preview Error</h3>
          <p>{error}</p>
          <button 
            className="retry-button"
            onClick={() => window.location.reload()}
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (!previewUrl && !webPreviewUrl) {
    return (
      <div className="preview-container waiting">
        <div className="waiting-content">
          <Smartphone className="phone-icon" size={48} />
          <h3>Waiting for Preview</h3>
          <p>Your React Native app will appear here once it's ready</p>
        </div>
      </div>
    );
  }

  const renderWebPreview = () => {
    if (!webPreviewUrl) {
      return (
        <div className="preview-container waiting">
          <div className="waiting-content">
            <Monitor className="phone-icon" size={48} />
            <h3>Web Preview Loading</h3>
            <p>Setting up web preview environment...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="preview-frame-container">
        <iframe
          ref={(c) => (webPreviewRef.current = c?.contentWindow ?? null)}
          src={webPreviewUrl}
          className="preview-frame"
          title="Expo Snack Web Preview"
          sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
          allow="geolocation; camera; microphone"
          loading="lazy"
        />
      </div>
    );
  };

  const renderMobilePreview = () => {
    if (!previewUrl) {
      return (
        <div className="preview-container waiting">
          <div className="waiting-content">
            <QrCode className="phone-icon" size={48} />
            <h3>Mobile Preview Loading</h3>
            <p>Generating QR code for mobile preview...</p>
          </div>
        </div>
      );
    }

    return (
      <div className="mobile-preview-container">
        <div className="qr-code-section">
          <h3>Scan with Expo Go</h3>
          <div className="qr-code-placeholder">
            <QrCode size={200} />
            <p>QR Code would be generated here</p>
            <p className="preview-url">{previewUrl}</p>
          </div>
        </div>
        <div className="mobile-instructions">
          <h4>How to preview on your phone:</h4>
          <ol>
            <li>Install the Expo Go app from your app store</li>
            <li>Scan the QR code above with your camera</li>
            <li>Your app will open in Expo Go</li>
          </ol>
        </div>
      </div>
    );
  };

  return (
    <div className="preview-container">
      <div className="preview-header">
        <div className="preview-title">
          {previewMode === 'web' ? <Monitor size={20} /> : <Smartphone size={20} />}
          <span>Live Preview</span>
        </div>
        <div className="preview-controls">
          <div className="preview-mode-toggle">
            <button
              className={`mode-button ${previewMode === 'web' ? 'active' : ''}`}
              onClick={() => setPreviewMode('web')}
              disabled={!webPreviewUrl}
            >
              <Monitor size={16} />
              Web
            </button>
            <button
              className={`mode-button ${previewMode === 'mobile' ? 'active' : ''}`}
              onClick={() => setPreviewMode('mobile')}
              disabled={!previewUrl}
            >
              <Smartphone size={16} />
              Mobile
            </button>
          </div>
          <div className="preview-actions">
            {previewMode === 'web' && webPreviewUrl && (
              <a
                href={webPreviewUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="open-link"
              >
                Open in New Tab
              </a>
            )}
            {previewMode === 'mobile' && previewUrl && (
              <a
                href={previewUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="open-link"
              >
                Open URL
              </a>
            )}
          </div>
        </div>
      </div>
      {previewMode === 'web' ? renderWebPreview() : renderMobilePreview()}
    </div>
  );
};

export default SnackPreview;
