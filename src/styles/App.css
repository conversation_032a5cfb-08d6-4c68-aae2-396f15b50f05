/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #1e1e1e;
  color: #ffffff;
  overflow: hidden;
}

/* App layout */
.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  padding: 0 16px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo h1 {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background-color: #0e639c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
}

.action-button:hover:not(:disabled) {
  background-color: #1177bb;
}

.action-button:disabled {
  background-color: #555;
  cursor: not-allowed;
  opacity: 0.6;
}

.theme-toggle {
  background-color: #444;
  min-width: 40px;
  justify-content: center;
}

/* Error banner */
.error-banner {
  background-color: #f14c4c;
  color: white;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.error-message {
  flex: 1;
  font-size: 14px;
}

.error-dismiss {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
}

.error-dismiss:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Upload panel */
.upload-panel {
  background-color: #252526;
  border-bottom: 1px solid #3e3e42;
  padding: 16px;
  flex-shrink: 0;
}

/* Main content */
.app-main {
  flex: 1;
  overflow: hidden;
}

/* Split pane styles */
.split-container {
  height: 100%;
  display: flex;
}

.split-container > .gutter {
  background-color: #3e3e42;
  background-repeat: no-repeat;
  background-position: 50%;
}

.split-container > .gutter.gutter-horizontal {
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAeCAYAAADkftS9AAAAIklEQVQoU2M4c+YMAzYwKjGqNKo0qjSqNKo0qjSqNKo0qjQqAQAA//8AAP//AwDVaQAAAABJRU5ErkJggg==');
  cursor: col-resize;
}

/* Panel headers */
.panel-header {
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  height: 40px;
  flex-shrink: 0;
}

.panel-info {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #cccccc;
}

.file-name {
  background-color: #3e3e42;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

.status-indicator {
  font-size: 8px;
}

.status-indicator.online {
  color: #4caf50;
}

/* Editor panel */
.editor-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.editor-container {
  flex: 1;
  overflow: hidden;
}

/* Preview panel */
.preview-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.preview-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e0e0e0;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  font-size: 14px;
}

.preview-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-mode-toggle {
  display: flex;
  background-color: #f5f5f5;
  border-radius: 6px;
  padding: 2px;
}

.mode-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  background: none;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s;
}

.mode-button:hover:not(:disabled) {
  background-color: #e0e0e0;
  color: #333;
}

.mode-button.active {
  background-color: #0e639c;
  color: white;
}

.mode-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.open-link {
  color: #0e639c;
  text-decoration: none;
  font-size: 12px;
}

.open-link:hover {
  text-decoration: underline;
}

.preview-frame-container {
  flex: 1;
  position: relative;
}

.preview-frame {
  width: 100%;
  height: 100%;
  border: none;
  background-color: white;
}

/* Loading, error, and waiting states */
.preview-container.loading,
.preview-container.error,
.preview-container.waiting {
  justify-content: center;
  align-items: center;
  background-color: #f9f9f9;
}

.loading-content,
.error-content,
.waiting-content {
  text-align: center;
  color: #666;
  max-width: 300px;
}

.loading-content h3,
.error-content h3,
.waiting-content h3 {
  margin: 16px 0 8px;
  color: #333;
}

.loading-content p,
.error-content p,
.waiting-content p {
  margin-bottom: 16px;
  line-height: 1.5;
}

.loading-spinner {
  animation: spin 1s linear infinite;
  color: #0e639c;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon {
  color: #f44336;
}

.phone-icon {
  color: #666;
}

.retry-button {
  background-color: #0e639c;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-button:hover {
  background-color: #1177bb;
}

/* Mobile preview styles */
.mobile-preview-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 32px;
  background-color: #f9f9f9;
  color: #333;
}

.qr-code-section {
  text-align: center;
  margin-bottom: 32px;
}

.qr-code-section h3 {
  margin-bottom: 16px;
  color: #333;
}

.qr-code-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 24px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.qr-code-placeholder p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.preview-url {
  font-family: 'Courier New', monospace;
  font-size: 12px !important;
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 4px;
  word-break: break-all;
  max-width: 300px;
}

.mobile-instructions {
  max-width: 400px;
  text-align: left;
}

.mobile-instructions h4 {
  margin-bottom: 12px;
  color: #333;
}

.mobile-instructions ol {
  padding-left: 20px;
  line-height: 1.6;
}

.mobile-instructions li {
  margin-bottom: 8px;
  color: #666;
}

/* File upload styles */
.file-upload-section {
  max-width: 600px;
}

.dropzone {
  border: 2px dashed #666;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #2d2d30;
}

.dropzone:hover,
.dropzone.active {
  border-color: #0e639c;
  background-color: #1e3a52;
}

.dropzone-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #cccccc;
}

.dropzone-hint {
  font-size: 14px;
  color: #999;
}

.dropzone-formats {
  font-size: 12px;
  color: #777;
}

.uploaded-files {
  margin-top: 16px;
}

.uploaded-files h4 {
  margin-bottom: 8px;
  color: #ffffff;
  font-size: 14px;
}

.uploaded-files ul {
  list-style: none;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #3e3e42;
  border-radius: 4px;
  margin-bottom: 4px;
  font-size: 14px;
}

.file-name {
  flex: 1;
  color: #ffffff;
}

.file-size {
  color: #999;
  font-size: 12px;
}

.remove-file {
  background: none;
  border: none;
  color: #f44336;
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.remove-file:hover {
  background-color: rgba(244, 67, 54, 0.1);
}

.file-errors {
  margin-top: 16px;
}

.file-errors h4 {
  color: #f44336;
  margin-bottom: 8px;
  font-size: 14px;
}

.error-item {
  color: #f44336;
  font-size: 14px;
  margin-bottom: 8px;
}

.error-message {
  font-size: 12px;
  color: #ff8a80;
  margin-left: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .app-header {
    padding: 0 12px;
    height: 50px;
  }

  .logo h1 {
    font-size: 16px;
  }

  .action-button {
    padding: 6px 8px;
    font-size: 12px;
  }

  .panel-header {
    padding: 6px 12px;
    font-size: 12px;
  }

  .upload-panel {
    padding: 12px;
  }

  .dropzone {
    padding: 24px 16px;
  }
}
