import { useState, useEffect, useRef } from 'react';
import { Snack } from 'snack-sdk';

export const useSnack = (initialCode) => {
  const [snack, setSnack] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [webPreviewUrl, setWebPreviewUrl] = useState('');
  const snackRef = useRef(null);
  const webPreviewRef = useRef(null);

  useEffect(() => {
    const initializeSnack = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create a new Snack instance
        const newSnack = new Snack({
          files: {
            'App.js': {
              type: 'CODE',
              contents: initialCode,
            },
          },
          dependencies: {},
          sdkVersion: '51.0.0', // Use latest stable SDK version
          online: true, // Make it available online
          webPreviewRef: webPreviewRef,
        });

        snackRef.current = newSnack;
        setSnack(newSnack);

        // Listen for state changes
        newSnack.addStateListener((state, prevState) => {
          console.log('Snack state updated:', state);

          if (state.url && state.url !== prevState?.url) {
            setPreviewUrl(state.url);
          }

          if (state.webPreviewURL && state.webPreviewURL !== prevState?.webPreviewURL) {
            setWebPreviewUrl(state.webPreviewURL);
          }

          if (state.error) {
            setError(state.error);
          }
        });

        // Wait for the snack to be ready
        const state = await newSnack.getStateAsync();
        console.log('Initial snack state:', state);

        if (state.url) {
          setPreviewUrl(state.url);
        }

        if (state.webPreviewURL) {
          setWebPreviewUrl(state.webPreviewURL);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Failed to initialize Snack:', err);
        setError(err.message || 'Failed to initialize Snack');
        setIsLoading(false);
      }
    };

    initializeSnack();

    // Cleanup function
    return () => {
      if (snackRef.current) {
        snackRef.current.setOnline(false);
      }
    };
  }, []);

  const updateCode = async (newCode) => {
    if (!snack) return;

    try {
      setError(null);

      // Update the App.js file with new code
      snack.updateFiles({
        'App.js': {
          type: 'CODE',
          contents: newCode,
        },
      });

      // The changes are automatically sent to connected clients
      // No need to manually save for live updates
    } catch (err) {
      console.error('Failed to update code:', err);
      setError(err.message || 'Failed to update code');
    }
  };

  const uploadFile = async (fileName, content) => {
    if (!snack) return;

    try {
      setError(null);

      // Add or update the file
      snack.updateFiles({
        [fileName]: {
          type: 'CODE',
          contents: content,
        },
      });
    } catch (err) {
      console.error('Failed to upload file:', err);
      setError(err.message || 'Failed to upload file');
    }
  };

  const saveSnack = async () => {
    if (!snack) return null;

    try {
      setError(null);
      const result = await snack.saveAsync();
      return result;
    } catch (err) {
      console.error('Failed to save snack:', err);
      setError(err.message || 'Failed to save snack');
      return null;
    }
  };

  return {
    snack,
    isLoading,
    error,
    previewUrl,
    webPreviewUrl,
    webPreviewRef,
    updateCode,
    uploadFile,
    saveSnack,
  };
};
