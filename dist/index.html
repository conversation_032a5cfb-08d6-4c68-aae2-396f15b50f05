<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="A modern web application for writing and previewing React Native code with live mobile simulation. Build React Native apps in your browser with real-time preview." />
    <meta name="keywords" content="React Native, Web Editor, Mobile Preview, Code Editor, Expo, JavaScript, Mobile Development" />
    <meta name="author" content="Sodan" />
    <meta property="og:title" content="React Native Web View - Live Mobile Preview" />
    <meta property="og:description" content="Write and preview React Native code with live mobile simulation in your browser" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://react-native-web-view.vercel.app" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="React Native Web View - Live Mobile Preview" />
    <meta name="twitter:description" content="Write and preview React Native code with live mobile simulation in your browser" />
    <title>React Native Web View - Live Mobile Preview</title>
    <script type="module" crossorigin src="/assets/index-CewOvH7q.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-B-wuX89F.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-DsTaC7rq.js">
    <link rel="modulepreload" crossorigin href="/assets/editor-BPOzP7i_.js">
    <link rel="stylesheet" crossorigin href="/assets/index-1ovTT6z6.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
