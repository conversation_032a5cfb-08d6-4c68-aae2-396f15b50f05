import{g as Pn,R as ve,r as T}from"./vendor-B-wuX89F.js";var Ge={exports:{}},In="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",On=In,_n=On;function Je(){}function Qe(){}Qe.resetWarningCache=Je;var Fn=function(){function t(i,a,o,r,p,c){if(c!==_n){var d=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw d.name="Invariant Violation",d}}t.isRequired=t;function e(){return t}var n={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:Qe,resetWarningCache:Je};return n.PropTypes=n,n};Ge.exports=Fn();var Mn=Ge.exports;const h=Pn(Mn);var G=typeof window<"u"?window:null,Ae=G===null,Ct=Ae?void 0:G.document,Z="addEventListener",X="removeEventListener",pe="getBoundingClientRect",zt="_a",tt="_b",pt="_c",Rt="horizontal",et=function(){return!1},Rn=Ae?"calc":["","-webkit-","-moz-","-o-"].filter(function(t){var e=Ct.createElement("div");return e.style.cssText="width:"+t+"calc(9px)",!!e.style.length}).shift()+"calc",Ze=function(t){return typeof t=="string"||t instanceof String},Oe=function(t){if(Ze(t)){var e=Ct.querySelector(t);if(!e)throw new Error("Selector "+t+" did not match a DOM element");return e}return t},K=function(t,e,n){var i=t[e];return i!==void 0?i:n},Bt=function(t,e,n,i){if(e){if(i==="end")return 0;if(i==="center")return t/2}else if(n){if(i==="start")return 0;if(i==="center")return t/2}return t},Bn=function(t,e){var n=Ct.createElement("div");return n.className="gutter gutter-"+e,n},Nn=function(t,e,n){var i={};return Ze(e)?i[t]=e:i[t]=Rn+"("+e+"% - "+n+"px)",i},Ln=function(t,e){var n;return n={},n[t]=e+"px",n},_e=function(t,e){if(e===void 0&&(e={}),Ae)return{};var n=t,i,a,o,r,p,c;Array.from&&(n=Array.from(n));var d=Oe(n[0]),s=d.parentNode,A=getComputedStyle?getComputedStyle(s):null,w=A?A.flexDirection:null,v=K(e,"sizes")||n.map(function(){return 100/n.length}),S=K(e,"minSize",100),b=Array.isArray(S)?S:n.map(function(){return S}),j=K(e,"maxSize",1/0),E=Array.isArray(j)?j:n.map(function(){return j}),k=K(e,"expandToMin",!1),g=K(e,"gutterSize",10),y=K(e,"gutterAlign","center"),C=K(e,"snapOffset",30),P=Array.isArray(C)?C:n.map(function(){return C}),D=K(e,"dragInterval",1),F=K(e,"direction",Rt),B=K(e,"cursor",F===Rt?"col-resize":"row-resize"),N=K(e,"gutter",Bn),J=K(e,"elementStyle",Nn),ot=K(e,"gutterStyle",Ln);F===Rt?(i="width",a="clientX",o="left",r="right",p="clientWidth"):F==="vertical"&&(i="height",a="clientY",o="top",r="bottom",p="clientHeight");function M(f,l,u,x){var I=J(i,l,u,x);Object.keys(I).forEach(function(O){f.style[O]=I[O]})}function U(f,l,u){var x=ot(i,l,u);Object.keys(x).forEach(function(I){f.style[I]=x[I]})}function q(){return c.map(function(f){return f.size})}function Pt(f){return"touches"in f?f.touches[0][a]:f[a]}function bt(f){var l=c[this.a],u=c[this.b],x=l.size+u.size;l.size=f/this.size*x,u.size=x-f/this.size*x,M(l.element,l.size,this[tt],l.i),M(u.element,u.size,this[pt],u.i)}function wt(f){var l,u=c[this.a],x=c[this.b];this.dragging&&(l=Pt(f)-this.start+(this[tt]-this.dragOffset),D>1&&(l=Math.round(l/D)*D),l<=u.minSize+u.snapOffset+this[tt]?l=u.minSize+this[tt]:l>=this.size-(x.minSize+x.snapOffset+this[pt])&&(l=this.size-(x.minSize+this[pt])),l>=u.maxSize-u.snapOffset+this[tt]?l=u.maxSize+this[tt]:l<=this.size-(x.maxSize-x.snapOffset+this[pt])&&(l=this.size-(x.maxSize+this[pt])),bt.call(this,l),K(e,"onDrag",et)(q()))}function $(){var f=c[this.a].element,l=c[this.b].element,u=f[pe](),x=l[pe]();this.size=u[i]+x[i]+this[tt]+this[pt],this.start=u[o],this.end=u[r]}function Zt(f){if(!getComputedStyle)return null;var l=getComputedStyle(f);if(!l)return null;var u=f[p];return u===0?null:(F===Rt?u-=parseFloat(l.paddingLeft)+parseFloat(l.paddingRight):u-=parseFloat(l.paddingTop)+parseFloat(l.paddingBottom),u)}function kt(f){var l=Zt(s);if(l===null||b.reduce(function(O,H){return O+H},0)>l)return f;var u=0,x=[],I=f.map(function(O,H){var nt=l*O/100,st=Bt(g,H===0,H===f.length-1,y),Q=b[H]+st;return nt<Q?(u+=Q-nt,x.push(0),Q):(x.push(nt-Q),nt)});return u===0?f:I.map(function(O,H){var nt=O;if(u>0&&x[H]-u>0){var st=Math.min(u,x[H]-u);u-=st,nt=O-st}return nt/l*100})}function mt(){var f=this,l=c[f.a].element,u=c[f.b].element;f.dragging&&K(e,"onDragEnd",et)(q()),f.dragging=!1,G[X]("mouseup",f.stop),G[X]("touchend",f.stop),G[X]("touchcancel",f.stop),G[X]("mousemove",f.move),G[X]("touchmove",f.move),f.stop=null,f.move=null,l[X]("selectstart",et),l[X]("dragstart",et),u[X]("selectstart",et),u[X]("dragstart",et),l.style.userSelect="",l.style.webkitUserSelect="",l.style.MozUserSelect="",l.style.pointerEvents="",u.style.userSelect="",u.style.webkitUserSelect="",u.style.MozUserSelect="",u.style.pointerEvents="",f.gutter.style.cursor="",f.parent.style.cursor="",Ct.body.style.cursor=""}function It(f){if(!("button"in f&&f.button!==0)){var l=this,u=c[l.a].element,x=c[l.b].element;l.dragging||K(e,"onDragStart",et)(q()),f.preventDefault(),l.dragging=!0,l.move=wt.bind(l),l.stop=mt.bind(l),G[Z]("mouseup",l.stop),G[Z]("touchend",l.stop),G[Z]("touchcancel",l.stop),G[Z]("mousemove",l.move),G[Z]("touchmove",l.move),u[Z]("selectstart",et),u[Z]("dragstart",et),x[Z]("selectstart",et),x[Z]("dragstart",et),u.style.userSelect="none",u.style.webkitUserSelect="none",u.style.MozUserSelect="none",u.style.pointerEvents="none",x.style.userSelect="none",x.style.webkitUserSelect="none",x.style.MozUserSelect="none",x.style.pointerEvents="none",l.gutter.style.cursor=B,l.parent.style.cursor=B,Ct.body.style.cursor=B,$.call(l),l.dragOffset=Pt(f)-l.end}}v=kt(v);var V=[];c=n.map(function(f,l){var u={element:Oe(f),size:v[l],minSize:b[l],maxSize:E[l],snapOffset:P[l],i:l},x;if(l>0&&(x={a:l-1,b:l,dragging:!1,direction:F,parent:s},x[tt]=Bt(g,l-1===0,!1,y),x[pt]=Bt(g,!1,l===n.length-1,y),w==="row-reverse"||w==="column-reverse")){var I=x.a;x.a=x.b,x.b=I}if(l>0){var O=N(l,F,u.element);U(O,g,l),x[zt]=It.bind(x),O[Z]("mousedown",x[zt]),O[Z]("touchstart",x[zt]),s.insertBefore(O,u.element),x.gutter=O}return M(u.element,u.size,Bt(g,l===0,l===n.length-1,y),l),l>0&&V.push(x),u});function Et(f){var l=f.i===V.length,u=l?V[f.i-1]:V[f.i];$.call(u);var x=l?u.size-f.minSize-u[pt]:f.minSize+u[tt];bt.call(u,x)}c.forEach(function(f){var l=f.element[pe]()[i];l<f.minSize&&(k?Et(f):f.minSize=l)});function rt(f){var l=kt(f);l.forEach(function(u,x){if(x>0){var I=V[x-1],O=c[I.a],H=c[I.b];O.size=l[x-1],H.size=u,M(O.element,O.size,I[tt],O.i),M(H.element,H.size,I[pt],H.i)}})}function Ot(f,l){V.forEach(function(u){if(l!==!0?u.parent.removeChild(u.gutter):(u.gutter[X]("mousedown",u[zt]),u.gutter[X]("touchstart",u[zt])),f!==!0){var x=J(i,u.a.size,u[tt]);Object.keys(x).forEach(function(I){c[u.a].element.style[I]="",c[u.b].element.style[I]=""})}})}return{setSizes:rt,getSizes:q,collapse:function(l){Et(c[l])},destroy:Ot,parent:s,pairs:V}};function ce(t,e){var n={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)===-1&&(n[i]=t[i]);return n}var Xe=function(t){function e(){t.apply(this,arguments)}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.componentDidMount=function(){var i=this.props;i.children;var a=i.gutter,o=ce(i,["children","gutter"]),r=o;r.gutter=function(p,c){var d;return a?d=a(p,c):(d=document.createElement("div"),d.className="gutter gutter-"+c),d.__isSplitGutter=!0,d},this.split=_e(this.parent.children,r)},e.prototype.componentDidUpdate=function(i){var a=this,o=this.props;o.children;var r=o.minSize,p=o.sizes,c=o.collapsed,d=ce(o,["children","minSize","sizes","collapsed"]),s=d,A=i.minSize,w=i.sizes,v=i.collapsed,S=["maxSize","expandToMin","gutterSize","gutterAlign","snapOffset","dragInterval","direction","cursor"],b=S.map(function(k){return a.props[k]!==i[k]}).reduce(function(k,g){return k||g},!1);if(Array.isArray(r)&&Array.isArray(A)){var j=!1;r.forEach(function(k,g){j=j||k!==A[g]}),b=b||j}else Array.isArray(r)||Array.isArray(A)?b=!0:b=b||r!==A;if(b)s.minSize=r,s.sizes=p||this.split.getSizes(),this.split.destroy(!0,!0),s.gutter=function(k,g,y){return y.previousSibling},this.split=_e(Array.from(this.parent.children).filter(function(k){return!k.__isSplitGutter}),s);else if(p){var E=!1;p.forEach(function(k,g){E=E||k!==w[g]}),E&&this.split.setSizes(this.props.sizes)}Number.isInteger(c)&&(c!==v||b)&&this.split.collapse(c)},e.prototype.componentWillUnmount=function(){this.split.destroy(),delete this.split},e.prototype.render=function(){var i=this,a=this.props;a.sizes,a.minSize,a.maxSize,a.expandToMin,a.gutterSize,a.gutterAlign,a.snapOffset,a.dragInterval,a.direction,a.cursor,a.gutter,a.elementStyle,a.gutterStyle,a.onDrag,a.onDragStart,a.onDragEnd,a.collapsed;var o=a.children,r=ce(a,["sizes","minSize","maxSize","expandToMin","gutterSize","gutterAlign","snapOffset","dragInterval","direction","cursor","gutter","elementStyle","gutterStyle","onDrag","onDragStart","onDragEnd","collapsed","children"]),p=r;return ve.createElement("div",Object.assign({},{ref:function(c){i.parent=c}},p),o)},e}(ve.Component);Xe.propTypes={sizes:h.arrayOf(h.number),minSize:h.oneOfType([h.number,h.arrayOf(h.number)]),maxSize:h.oneOfType([h.number,h.arrayOf(h.number)]),expandToMin:h.bool,gutterSize:h.number,gutterAlign:h.string,snapOffset:h.oneOfType([h.number,h.arrayOf(h.number)]),dragInterval:h.number,direction:h.string,cursor:h.string,gutter:h.func,elementStyle:h.func,gutterStyle:h.func,onDrag:h.func,onDragStart:h.func,onDragEnd:h.func,collapsed:h.number,children:h.arrayOf(h.element)};Xe.defaultProps={sizes:void 0,minSize:void 0,maxSize:void 0,expandToMin:void 0,gutterSize:void 0,gutterAlign:void 0,snapOffset:void 0,dragInterval:void 0,direction:void 0,cursor:void 0,gutter:void 0,elementStyle:void 0,gutterStyle:void 0,onDrag:void 0,onDragStart:void 0,onDragEnd:void 0,collapsed:void 0,children:void 0};var Wt={},Un=function(){return typeof Promise=="function"&&Promise.prototype&&Promise.prototype.then},tn={},Y={};let Ce;const qn=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];Y.getSymbolSize=function(e){if(!e)throw new Error('"version" cannot be null or undefined');if(e<1||e>40)throw new Error('"version" should be in range from 1 to 40');return e*4+17};Y.getSymbolTotalCodewords=function(e){return qn[e]};Y.getBCHDigit=function(t){let e=0;for(;t!==0;)e++,t>>>=1;return e};Y.setToSJISFunction=function(e){if(typeof e!="function")throw new Error('"toSJISFunc" is not a valid function.');Ce=e};Y.isKanjiModeEnabled=function(){return typeof Ce<"u"};Y.toSJIS=function(e){return Ce(e)};var Yt={};(function(t){t.L={bit:1},t.M={bit:0},t.Q={bit:3},t.H={bit:2};function e(n){if(typeof n!="string")throw new Error("Param is not a string");switch(n.toLowerCase()){case"l":case"low":return t.L;case"m":case"medium":return t.M;case"q":case"quartile":return t.Q;case"h":case"high":return t.H;default:throw new Error("Unknown EC Level: "+n)}}t.isValid=function(i){return i&&typeof i.bit<"u"&&i.bit>=0&&i.bit<4},t.from=function(i,a){if(t.isValid(i))return i;try{return e(i)}catch{return a}}})(Yt);function en(){this.buffer=[],this.length=0}en.prototype={get:function(t){const e=Math.floor(t/8);return(this.buffer[e]>>>7-t%8&1)===1},put:function(t,e){for(let n=0;n<e;n++)this.putBit((t>>>e-n-1&1)===1)},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}};var $n=en;function Tt(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}Tt.prototype.set=function(t,e,n,i){const a=t*this.size+e;this.data[a]=n,i&&(this.reservedBit[a]=!0)};Tt.prototype.get=function(t,e){return this.data[t*this.size+e]};Tt.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n};Tt.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]};var Hn=Tt,nn={};(function(t){const e=Y.getSymbolSize;t.getRowColCoords=function(i){if(i===1)return[];const a=Math.floor(i/7)+2,o=e(i),r=o===145?26:Math.ceil((o-13)/(2*a-2))*2,p=[o-7];for(let c=1;c<a-1;c++)p[c]=p[c-1]-r;return p.push(6),p.reverse()},t.getPositions=function(i){const a=[],o=t.getRowColCoords(i),r=o.length;for(let p=0;p<r;p++)for(let c=0;c<r;c++)p===0&&c===0||p===0&&c===r-1||p===r-1&&c===0||a.push([o[p],o[c]]);return a}})(nn);var an={};const Kn=Y.getSymbolSize,Fe=7;an.getPositions=function(e){const n=Kn(e);return[[0,0],[n-Fe,0],[0,n-Fe]]};var on={};(function(t){t.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const e={N1:3,N2:3,N3:40,N4:10};t.isValid=function(a){return a!=null&&a!==""&&!isNaN(a)&&a>=0&&a<=7},t.from=function(a){return t.isValid(a)?parseInt(a,10):void 0},t.getPenaltyN1=function(a){const o=a.size;let r=0,p=0,c=0,d=null,s=null;for(let A=0;A<o;A++){p=c=0,d=s=null;for(let w=0;w<o;w++){let v=a.get(A,w);v===d?p++:(p>=5&&(r+=e.N1+(p-5)),d=v,p=1),v=a.get(w,A),v===s?c++:(c>=5&&(r+=e.N1+(c-5)),s=v,c=1)}p>=5&&(r+=e.N1+(p-5)),c>=5&&(r+=e.N1+(c-5))}return r},t.getPenaltyN2=function(a){const o=a.size;let r=0;for(let p=0;p<o-1;p++)for(let c=0;c<o-1;c++){const d=a.get(p,c)+a.get(p,c+1)+a.get(p+1,c)+a.get(p+1,c+1);(d===4||d===0)&&r++}return r*e.N2},t.getPenaltyN3=function(a){const o=a.size;let r=0,p=0,c=0;for(let d=0;d<o;d++){p=c=0;for(let s=0;s<o;s++)p=p<<1&2047|a.get(d,s),s>=10&&(p===1488||p===93)&&r++,c=c<<1&2047|a.get(s,d),s>=10&&(c===1488||c===93)&&r++}return r*e.N3},t.getPenaltyN4=function(a){let o=0;const r=a.data.length;for(let c=0;c<r;c++)o+=a.data[c];return Math.abs(Math.ceil(o*100/r/5)-10)*e.N4};function n(i,a,o){switch(i){case t.Patterns.PATTERN000:return(a+o)%2===0;case t.Patterns.PATTERN001:return a%2===0;case t.Patterns.PATTERN010:return o%3===0;case t.Patterns.PATTERN011:return(a+o)%3===0;case t.Patterns.PATTERN100:return(Math.floor(a/2)+Math.floor(o/3))%2===0;case t.Patterns.PATTERN101:return a*o%2+a*o%3===0;case t.Patterns.PATTERN110:return(a*o%2+a*o%3)%2===0;case t.Patterns.PATTERN111:return(a*o%3+(a+o)%2)%2===0;default:throw new Error("bad maskPattern:"+i)}}t.applyMask=function(a,o){const r=o.size;for(let p=0;p<r;p++)for(let c=0;c<r;c++)o.isReserved(c,p)||o.xor(c,p,n(a,c,p))},t.getBestMask=function(a,o){const r=Object.keys(t.Patterns).length;let p=0,c=1/0;for(let d=0;d<r;d++){o(d),t.applyMask(d,a);const s=t.getPenaltyN1(a)+t.getPenaltyN2(a)+t.getPenaltyN3(a)+t.getPenaltyN4(a);t.applyMask(d,a),s<c&&(c=s,p=d)}return p}})(on);var Gt={};const ct=Yt,Nt=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],Lt=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];Gt.getBlocksCount=function(e,n){switch(n){case ct.L:return Nt[(e-1)*4+0];case ct.M:return Nt[(e-1)*4+1];case ct.Q:return Nt[(e-1)*4+2];case ct.H:return Nt[(e-1)*4+3];default:return}};Gt.getTotalCodewordsCount=function(e,n){switch(n){case ct.L:return Lt[(e-1)*4+0];case ct.M:return Lt[(e-1)*4+1];case ct.Q:return Lt[(e-1)*4+2];case ct.H:return Lt[(e-1)*4+3];default:return}};var rn={},Jt={};const At=new Uint8Array(512),qt=new Uint8Array(256);(function(){let e=1;for(let n=0;n<255;n++)At[n]=e,qt[e]=n,e<<=1,e&256&&(e^=285);for(let n=255;n<512;n++)At[n]=At[n-255]})();Jt.log=function(e){if(e<1)throw new Error("log("+e+")");return qt[e]};Jt.exp=function(e){return At[e]};Jt.mul=function(e,n){return e===0||n===0?0:At[qt[e]+qt[n]]};(function(t){const e=Jt;t.mul=function(i,a){const o=new Uint8Array(i.length+a.length-1);for(let r=0;r<i.length;r++)for(let p=0;p<a.length;p++)o[r+p]^=e.mul(i[r],a[p]);return o},t.mod=function(i,a){let o=new Uint8Array(i);for(;o.length-a.length>=0;){const r=o[0];for(let c=0;c<a.length;c++)o[c]^=e.mul(a[c],r);let p=0;for(;p<o.length&&o[p]===0;)p++;o=o.slice(p)}return o},t.generateECPolynomial=function(i){let a=new Uint8Array([1]);for(let o=0;o<i;o++)a=t.mul(a,new Uint8Array([1,e.exp(o)]));return a}})(rn);const pn=rn;function De(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}De.prototype.initialize=function(e){this.degree=e,this.genPoly=pn.generateECPolynomial(this.degree)};De.prototype.encode=function(e){if(!this.genPoly)throw new Error("Encoder not initialized");const n=new Uint8Array(e.length+this.degree);n.set(e);const i=pn.mod(n,this.genPoly),a=this.degree-i.length;if(a>0){const o=new Uint8Array(this.degree);return o.set(i,a),o}return i};var Vn=De,cn={},lt={},je={};je.isValid=function(e){return!isNaN(e)&&e>=1&&e<=40};var at={};const ln="[0-9]+",Wn="[A-Z $%*+\\-./:]+";let Dt="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";Dt=Dt.replace(/u/g,"\\u");const Yn="(?:(?![A-Z0-9 $%*+\\-./:]|"+Dt+`)(?:.|[\r
]))+`;at.KANJI=new RegExp(Dt,"g");at.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g");at.BYTE=new RegExp(Yn,"g");at.NUMERIC=new RegExp(ln,"g");at.ALPHANUMERIC=new RegExp(Wn,"g");const Gn=new RegExp("^"+Dt+"$"),Jn=new RegExp("^"+ln+"$"),Qn=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");at.testKanji=function(e){return Gn.test(e)};at.testNumeric=function(e){return Jn.test(e)};at.testAlphanumeric=function(e){return Qn.test(e)};(function(t){const e=je,n=at;t.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},t.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},t.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},t.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},t.MIXED={bit:-1},t.getCharCountIndicator=function(o,r){if(!o.ccBits)throw new Error("Invalid mode: "+o);if(!e.isValid(r))throw new Error("Invalid version: "+r);return r>=1&&r<10?o.ccBits[0]:r<27?o.ccBits[1]:o.ccBits[2]},t.getBestModeForData=function(o){return n.testNumeric(o)?t.NUMERIC:n.testAlphanumeric(o)?t.ALPHANUMERIC:n.testKanji(o)?t.KANJI:t.BYTE},t.toString=function(o){if(o&&o.id)return o.id;throw new Error("Invalid mode")},t.isValid=function(o){return o&&o.bit&&o.ccBits};function i(a){if(typeof a!="string")throw new Error("Param is not a string");switch(a.toLowerCase()){case"numeric":return t.NUMERIC;case"alphanumeric":return t.ALPHANUMERIC;case"kanji":return t.KANJI;case"byte":return t.BYTE;default:throw new Error("Unknown mode: "+a)}}t.from=function(o,r){if(t.isValid(o))return o;try{return i(o)}catch{return r}}})(lt);(function(t){const e=Y,n=Gt,i=Yt,a=lt,o=je,r=7973,p=e.getBCHDigit(r);function c(w,v,S){for(let b=1;b<=40;b++)if(v<=t.getCapacity(b,S,w))return b}function d(w,v){return a.getCharCountIndicator(w,v)+4}function s(w,v){let S=0;return w.forEach(function(b){const j=d(b.mode,v);S+=j+b.getBitsLength()}),S}function A(w,v){for(let S=1;S<=40;S++)if(s(w,S)<=t.getCapacity(S,v,a.MIXED))return S}t.from=function(v,S){return o.isValid(v)?parseInt(v,10):S},t.getCapacity=function(v,S,b){if(!o.isValid(v))throw new Error("Invalid QR Code version");typeof b>"u"&&(b=a.BYTE);const j=e.getSymbolTotalCodewords(v),E=n.getTotalCodewordsCount(v,S),k=(j-E)*8;if(b===a.MIXED)return k;const g=k-d(b,v);switch(b){case a.NUMERIC:return Math.floor(g/10*3);case a.ALPHANUMERIC:return Math.floor(g/11*2);case a.KANJI:return Math.floor(g/13);case a.BYTE:default:return Math.floor(g/8)}},t.getBestVersionForData=function(v,S){let b;const j=i.from(S,i.M);if(Array.isArray(v)){if(v.length>1)return A(v,j);if(v.length===0)return 1;b=v[0]}else b=v;return c(b.mode,b.getLength(),j)},t.getEncodedBits=function(v){if(!o.isValid(v)||v<7)throw new Error("Invalid QR Code version");let S=v<<12;for(;e.getBCHDigit(S)-p>=0;)S^=r<<e.getBCHDigit(S)-p;return v<<12|S}})(cn);var sn={};const xe=Y,dn=1335,Zn=21522,Me=xe.getBCHDigit(dn);sn.getEncodedBits=function(e,n){const i=e.bit<<3|n;let a=i<<10;for(;xe.getBCHDigit(a)-Me>=0;)a^=dn<<xe.getBCHDigit(a)-Me;return(i<<10|a)^Zn};var un={};const Xn=lt;function gt(t){this.mode=Xn.NUMERIC,this.data=t.toString()}gt.getBitsLength=function(e){return 10*Math.floor(e/3)+(e%3?e%3*3+1:0)};gt.prototype.getLength=function(){return this.data.length};gt.prototype.getBitsLength=function(){return gt.getBitsLength(this.data.length)};gt.prototype.write=function(e){let n,i,a;for(n=0;n+3<=this.data.length;n+=3)i=this.data.substr(n,3),a=parseInt(i,10),e.put(a,10);const o=this.data.length-n;o>0&&(i=this.data.substr(n),a=parseInt(i,10),e.put(a,o*3+1))};var ti=gt;const ei=lt,le=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function vt(t){this.mode=ei.ALPHANUMERIC,this.data=t}vt.getBitsLength=function(e){return 11*Math.floor(e/2)+6*(e%2)};vt.prototype.getLength=function(){return this.data.length};vt.prototype.getBitsLength=function(){return vt.getBitsLength(this.data.length)};vt.prototype.write=function(e){let n;for(n=0;n+2<=this.data.length;n+=2){let i=le.indexOf(this.data[n])*45;i+=le.indexOf(this.data[n+1]),e.put(i,11)}this.data.length%2&&e.put(le.indexOf(this.data[n]),6)};var ni=vt;const ii=lt;function xt(t){this.mode=ii.BYTE,typeof t=="string"?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}xt.getBitsLength=function(e){return e*8};xt.prototype.getLength=function(){return this.data.length};xt.prototype.getBitsLength=function(){return xt.getBitsLength(this.data.length)};xt.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)};var ai=xt;const oi=lt,ri=Y;function ht(t){this.mode=oi.KANJI,this.data=t}ht.getBitsLength=function(e){return e*13};ht.prototype.getLength=function(){return this.data.length};ht.prototype.getBitsLength=function(){return ht.getBitsLength(this.data.length)};ht.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=ri.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else if(n>=57408&&n<=60351)n-=49472;else throw new Error("Invalid SJIS character: "+this.data[e]+`
Make sure your charset is UTF-8`);n=(n>>>8&255)*192+(n&255),t.put(n,13)}};var pi=ht,mn={exports:{}};(function(t){var e={single_source_shortest_paths:function(n,i,a){var o={},r={};r[i]=0;var p=e.PriorityQueue.make();p.push(i,0);for(var c,d,s,A,w,v,S,b,j;!p.empty();){c=p.pop(),d=c.value,A=c.cost,w=n[d]||{};for(s in w)w.hasOwnProperty(s)&&(v=w[s],S=A+v,b=r[s],j=typeof r[s]>"u",(j||b>S)&&(r[s]=S,p.push(s,S),o[s]=d))}if(typeof a<"u"&&typeof r[a]>"u"){var E=["Could not find a path from ",i," to ",a,"."].join("");throw new Error(E)}return o},extract_shortest_path_from_predecessor_list:function(n,i){for(var a=[],o=i;o;)a.push(o),n[o],o=n[o];return a.reverse(),a},find_path:function(n,i,a){var o=e.single_source_shortest_paths(n,i,a);return e.extract_shortest_path_from_predecessor_list(o,a)},PriorityQueue:{make:function(n){var i=e.PriorityQueue,a={},o;n=n||{};for(o in i)i.hasOwnProperty(o)&&(a[o]=i[o]);return a.queue=[],a.sorter=n.sorter||i.default_sorter,a},default_sorter:function(n,i){return n.cost-i.cost},push:function(n,i){var a={value:n,cost:i};this.queue.push(a),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return this.queue.length===0}}};t.exports=e})(mn);var ci=mn.exports;(function(t){const e=lt,n=ti,i=ni,a=ai,o=pi,r=at,p=Y,c=ci;function d(E){return unescape(encodeURIComponent(E)).length}function s(E,k,g){const y=[];let C;for(;(C=E.exec(g))!==null;)y.push({data:C[0],index:C.index,mode:k,length:C[0].length});return y}function A(E){const k=s(r.NUMERIC,e.NUMERIC,E),g=s(r.ALPHANUMERIC,e.ALPHANUMERIC,E);let y,C;return p.isKanjiModeEnabled()?(y=s(r.BYTE,e.BYTE,E),C=s(r.KANJI,e.KANJI,E)):(y=s(r.BYTE_KANJI,e.BYTE,E),C=[]),k.concat(g,y,C).sort(function(D,F){return D.index-F.index}).map(function(D){return{data:D.data,mode:D.mode,length:D.length}})}function w(E,k){switch(k){case e.NUMERIC:return n.getBitsLength(E);case e.ALPHANUMERIC:return i.getBitsLength(E);case e.KANJI:return o.getBitsLength(E);case e.BYTE:return a.getBitsLength(E)}}function v(E){return E.reduce(function(k,g){const y=k.length-1>=0?k[k.length-1]:null;return y&&y.mode===g.mode?(k[k.length-1].data+=g.data,k):(k.push(g),k)},[])}function S(E){const k=[];for(let g=0;g<E.length;g++){const y=E[g];switch(y.mode){case e.NUMERIC:k.push([y,{data:y.data,mode:e.ALPHANUMERIC,length:y.length},{data:y.data,mode:e.BYTE,length:y.length}]);break;case e.ALPHANUMERIC:k.push([y,{data:y.data,mode:e.BYTE,length:y.length}]);break;case e.KANJI:k.push([y,{data:y.data,mode:e.BYTE,length:d(y.data)}]);break;case e.BYTE:k.push([{data:y.data,mode:e.BYTE,length:d(y.data)}])}}return k}function b(E,k){const g={},y={start:{}};let C=["start"];for(let P=0;P<E.length;P++){const D=E[P],F=[];for(let B=0;B<D.length;B++){const N=D[B],J=""+P+B;F.push(J),g[J]={node:N,lastCount:0},y[J]={};for(let ot=0;ot<C.length;ot++){const M=C[ot];g[M]&&g[M].node.mode===N.mode?(y[M][J]=w(g[M].lastCount+N.length,N.mode)-w(g[M].lastCount,N.mode),g[M].lastCount+=N.length):(g[M]&&(g[M].lastCount=N.length),y[M][J]=w(N.length,N.mode)+4+e.getCharCountIndicator(N.mode,k))}}C=F}for(let P=0;P<C.length;P++)y[C[P]].end=0;return{map:y,table:g}}function j(E,k){let g;const y=e.getBestModeForData(E);if(g=e.from(k,y),g!==e.BYTE&&g.bit<y.bit)throw new Error('"'+E+'" cannot be encoded with mode '+e.toString(g)+`.
 Suggested mode is: `+e.toString(y));switch(g===e.KANJI&&!p.isKanjiModeEnabled()&&(g=e.BYTE),g){case e.NUMERIC:return new n(E);case e.ALPHANUMERIC:return new i(E);case e.KANJI:return new o(E);case e.BYTE:return new a(E)}}t.fromArray=function(k){return k.reduce(function(g,y){return typeof y=="string"?g.push(j(y,null)):y.data&&g.push(j(y.data,y.mode)),g},[])},t.fromString=function(k,g){const y=A(k,p.isKanjiModeEnabled()),C=S(y),P=b(C,g),D=c.find_path(P.map,"start","end"),F=[];for(let B=1;B<D.length-1;B++)F.push(P.table[D[B]].node);return t.fromArray(v(F))},t.rawSplit=function(k){return t.fromArray(A(k,p.isKanjiModeEnabled()))}})(un);const Qt=Y,se=Yt,li=$n,si=Hn,di=nn,ui=an,he=on,ye=Gt,mi=Vn,$t=cn,fi=sn,gi=lt,de=un;function vi(t,e){const n=t.size,i=ui.getPositions(e);for(let a=0;a<i.length;a++){const o=i[a][0],r=i[a][1];for(let p=-1;p<=7;p++)if(!(o+p<=-1||n<=o+p))for(let c=-1;c<=7;c++)r+c<=-1||n<=r+c||(p>=0&&p<=6&&(c===0||c===6)||c>=0&&c<=6&&(p===0||p===6)||p>=2&&p<=4&&c>=2&&c<=4?t.set(o+p,r+c,!0,!0):t.set(o+p,r+c,!1,!0))}}function xi(t){const e=t.size;for(let n=8;n<e-8;n++){const i=n%2===0;t.set(n,6,i,!0),t.set(6,n,i,!0)}}function hi(t,e){const n=di.getPositions(e);for(let i=0;i<n.length;i++){const a=n[i][0],o=n[i][1];for(let r=-2;r<=2;r++)for(let p=-2;p<=2;p++)r===-2||r===2||p===-2||p===2||r===0&&p===0?t.set(a+r,o+p,!0,!0):t.set(a+r,o+p,!1,!0)}}function yi(t,e){const n=t.size,i=$t.getEncodedBits(e);let a,o,r;for(let p=0;p<18;p++)a=Math.floor(p/3),o=p%3+n-8-3,r=(i>>p&1)===1,t.set(a,o,r,!0),t.set(o,a,r,!0)}function ue(t,e,n){const i=t.size,a=fi.getEncodedBits(e,n);let o,r;for(o=0;o<15;o++)r=(a>>o&1)===1,o<6?t.set(o,8,r,!0):o<8?t.set(o+1,8,r,!0):t.set(i-15+o,8,r,!0),o<8?t.set(8,i-o-1,r,!0):o<9?t.set(8,15-o-1+1,r,!0):t.set(8,15-o-1,r,!0);t.set(i-8,8,1,!0)}function bi(t,e){const n=t.size;let i=-1,a=n-1,o=7,r=0;for(let p=n-1;p>0;p-=2)for(p===6&&p--;;){for(let c=0;c<2;c++)if(!t.isReserved(a,p-c)){let d=!1;r<e.length&&(d=(e[r]>>>o&1)===1),t.set(a,p-c,d),o--,o===-1&&(r++,o=7)}if(a+=i,a<0||n<=a){a-=i,i=-i;break}}}function wi(t,e,n){const i=new li;n.forEach(function(c){i.put(c.mode.bit,4),i.put(c.getLength(),gi.getCharCountIndicator(c.mode,t)),c.write(i)});const a=Qt.getSymbolTotalCodewords(t),o=ye.getTotalCodewordsCount(t,e),r=(a-o)*8;for(i.getLengthInBits()+4<=r&&i.put(0,4);i.getLengthInBits()%8!==0;)i.putBit(0);const p=(r-i.getLengthInBits())/8;for(let c=0;c<p;c++)i.put(c%2?17:236,8);return ki(i,t,e)}function ki(t,e,n){const i=Qt.getSymbolTotalCodewords(e),a=ye.getTotalCodewordsCount(e,n),o=i-a,r=ye.getBlocksCount(e,n),p=i%r,c=r-p,d=Math.floor(i/r),s=Math.floor(o/r),A=s+1,w=d-s,v=new mi(w);let S=0;const b=new Array(r),j=new Array(r);let E=0;const k=new Uint8Array(t.buffer);for(let D=0;D<r;D++){const F=D<c?s:A;b[D]=k.slice(S,S+F),j[D]=v.encode(b[D]),S+=F,E=Math.max(E,F)}const g=new Uint8Array(i);let y=0,C,P;for(C=0;C<E;C++)for(P=0;P<r;P++)C<b[P].length&&(g[y++]=b[P][C]);for(C=0;C<w;C++)for(P=0;P<r;P++)g[y++]=j[P][C];return g}function Ei(t,e,n,i){let a;if(Array.isArray(t))a=de.fromArray(t);else if(typeof t=="string"){let d=e;if(!d){const s=de.rawSplit(t);d=$t.getBestVersionForData(s,n)}a=de.fromString(t,d||40)}else throw new Error("Invalid data");const o=$t.getBestVersionForData(a,n);if(!o)throw new Error("The amount of data is too big to be stored in a QR Code");if(!e)e=o;else if(e<o)throw new Error(`
The chosen QR Code version cannot contain this amount of data.
Minimum version required to store current data is: `+o+`.
`);const r=wi(e,n,a),p=Qt.getSymbolSize(e),c=new si(p);return vi(c,e),xi(c),hi(c,e),ue(c,n,0),e>=7&&yi(c,e),bi(c,r),isNaN(i)&&(i=he.getBestMask(c,ue.bind(null,c,n))),he.applyMask(i,c),ue(c,n,i),{modules:c,version:e,errorCorrectionLevel:n,maskPattern:i,segments:a}}tn.create=function(e,n){if(typeof e>"u"||e==="")throw new Error("No input text");let i=se.M,a,o;return typeof n<"u"&&(i=se.from(n.errorCorrectionLevel,se.M),a=$t.from(n.version),o=he.from(n.maskPattern),n.toSJISFunc&&Qt.setToSJISFunction(n.toSJISFunc)),Ei(e,a,i,o)};var fn={},Te={};(function(t){function e(n){if(typeof n=="number"&&(n=n.toString()),typeof n!="string")throw new Error("Color should be defined as hex string");let i=n.slice().replace("#","").split("");if(i.length<3||i.length===5||i.length>8)throw new Error("Invalid hex color: "+n);(i.length===3||i.length===4)&&(i=Array.prototype.concat.apply([],i.map(function(o){return[o,o]}))),i.length===6&&i.push("F","F");const a=parseInt(i.join(""),16);return{r:a>>24&255,g:a>>16&255,b:a>>8&255,a:a&255,hex:"#"+i.slice(0,6).join("")}}t.getOptions=function(i){i||(i={}),i.color||(i.color={});const a=typeof i.margin>"u"||i.margin===null||i.margin<0?4:i.margin,o=i.width&&i.width>=21?i.width:void 0,r=i.scale||4;return{width:o,scale:o?4:r,margin:a,color:{dark:e(i.color.dark||"#000000ff"),light:e(i.color.light||"#ffffffff")},type:i.type,rendererOpts:i.rendererOpts||{}}},t.getScale=function(i,a){return a.width&&a.width>=i+a.margin*2?a.width/(i+a.margin*2):a.scale},t.getImageWidth=function(i,a){const o=t.getScale(i,a);return Math.floor((i+a.margin*2)*o)},t.qrToImageData=function(i,a,o){const r=a.modules.size,p=a.modules.data,c=t.getScale(r,o),d=Math.floor((r+o.margin*2)*c),s=o.margin*c,A=[o.color.light,o.color.dark];for(let w=0;w<d;w++)for(let v=0;v<d;v++){let S=(w*d+v)*4,b=o.color.light;if(w>=s&&v>=s&&w<d-s&&v<d-s){const j=Math.floor((w-s)/c),E=Math.floor((v-s)/c);b=A[p[j*r+E]?1:0]}i[S++]=b.r,i[S++]=b.g,i[S++]=b.b,i[S]=b.a}}})(Te);(function(t){const e=Te;function n(a,o,r){a.clearRect(0,0,o.width,o.height),o.style||(o.style={}),o.height=r,o.width=r,o.style.height=r+"px",o.style.width=r+"px"}function i(){try{return document.createElement("canvas")}catch{throw new Error("You need to specify a canvas element")}}t.render=function(o,r,p){let c=p,d=r;typeof c>"u"&&(!r||!r.getContext)&&(c=r,r=void 0),r||(d=i()),c=e.getOptions(c);const s=e.getImageWidth(o.modules.size,c),A=d.getContext("2d"),w=A.createImageData(s,s);return e.qrToImageData(w.data,o,c),n(A,d,s),A.putImageData(w,0,0),d},t.renderToDataURL=function(o,r,p){let c=p;typeof c>"u"&&(!r||!r.getContext)&&(c=r,r=void 0),c||(c={});const d=t.render(o,r,c),s=c.type||"image/png",A=c.rendererOpts||{};return d.toDataURL(s,A.quality)}})(fn);var gn={};const Si=Te;function Re(t,e){const n=t.a/255,i=e+'="'+t.hex+'"';return n<1?i+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':i}function me(t,e,n){let i=t+e;return typeof n<"u"&&(i+=" "+n),i}function zi(t,e,n){let i="",a=0,o=!1,r=0;for(let p=0;p<t.length;p++){const c=Math.floor(p%e),d=Math.floor(p/e);!c&&!o&&(o=!0),t[p]?(r++,p>0&&c>0&&t[p-1]||(i+=o?me("M",c+n,.5+d+n):me("m",a,0),a=0,o=!1),c+1<e&&t[p+1]||(i+=me("h",r),r=0)):a++}return i}gn.render=function(e,n,i){const a=Si.getOptions(n),o=e.modules.size,r=e.modules.data,p=o+a.margin*2,c=a.color.light.a?"<path "+Re(a.color.light,"fill")+' d="M0 0h'+p+"v"+p+'H0z"/>':"",d="<path "+Re(a.color.dark,"stroke")+' d="'+zi(r,o,a.margin)+'"/>',s='viewBox="0 0 '+p+" "+p+'"',w='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+s+' shape-rendering="crispEdges">'+c+d+`</svg>
`;return typeof i=="function"&&i(null,w),w};const Ai=Un,be=tn,vn=fn,Ci=gn;function Pe(t,e,n,i,a){const o=[].slice.call(arguments,1),r=o.length,p=typeof o[r-1]=="function";if(!p&&!Ai())throw new Error("Callback required as last argument");if(p){if(r<2)throw new Error("Too few arguments provided");r===2?(a=n,n=e,e=i=void 0):r===3&&(e.getContext&&typeof a>"u"?(a=i,i=void 0):(a=i,i=n,n=e,e=void 0))}else{if(r<1)throw new Error("Too few arguments provided");return r===1?(n=e,e=i=void 0):r===2&&!e.getContext&&(i=n,n=e,e=void 0),new Promise(function(c,d){try{const s=be.create(n,i);c(t(s,e,i))}catch(s){d(s)}})}try{const c=be.create(n,i);a(null,t(c,e,i))}catch(c){a(c)}}Wt.create=be.create;Wt.toCanvas=Pe.bind(null,vn.render);Wt.toDataURL=Pe.bind(null,vn.renderToDataURL);Wt.toString=Pe.bind(null,function(t,e,n){return Ci.render(t,n)});function ut(t,e,n,i){function a(o){return o instanceof n?o:new n(function(r){r(o)})}return new(n||(n=Promise))(function(o,r){function p(s){try{d(i.next(s))}catch(A){r(A)}}function c(s){try{d(i.throw(s))}catch(A){r(A)}}function d(s){s.done?o(s.value):a(s.value).then(p,c)}d((i=i.apply(t,e||[])).next())})}const Di=new Map([["1km","application/vnd.1000minds.decision-model+xml"],["3dml","text/vnd.in3d.3dml"],["3ds","image/x-3ds"],["3g2","video/3gpp2"],["3gp","video/3gp"],["3gpp","video/3gpp"],["3mf","model/3mf"],["7z","application/x-7z-compressed"],["7zip","application/x-7z-compressed"],["123","application/vnd.lotus-1-2-3"],["aab","application/x-authorware-bin"],["aac","audio/x-acc"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abw","application/x-abiword"],["ac","application/vnd.nokia.n-gage.ac+xml"],["ac3","audio/ac3"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acu","application/vnd.acucobol"],["acutc","application/vnd.acucorp"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afm","application/x-font-type1"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/pdf"],["aif","audio/x-aiff"],["aifc","audio/x-aiff"],["aiff","audio/x-aiff"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["amr","audio/amr"],["apk","application/vnd.android.package-archive"],["apng","image/apng"],["appcache","text/cache-manifest"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["arc","application/x-freearc"],["arj","application/x-arj"],["asc","application/pgp-signature"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asx","video/x-ms-asf"],["atc","application/vnd.acucorp"],["atom","application/atom+xml"],["atomcat","application/atomcat+xml"],["atomdeleted","application/atomdeleted+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au","audio/x-au"],["avi","video/x-msvideo"],["avif","image/avif"],["aw","application/applixware"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azv","image/vnd.airzip.accelerator.azv"],["azw","application/vnd.amazon.ebook"],["b16","image/vnd.pco.b16"],["bat","application/x-msdownload"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bdoc","application/x-bdoc"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin","application/octet-stream"],["blb","application/x-blorb"],["blorb","application/x-blorb"],["bmi","application/vnd.bmi"],["bmml","application/vnd.balsamiq.bmml+xml"],["bmp","image/bmp"],["book","application/vnd.framemaker"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bpk","application/octet-stream"],["bpmn","application/octet-stream"],["bsp","model/vnd.valve.source.compiled-map"],["btif","image/prs.btif"],["buffer","application/octet-stream"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c","text/x-c"],["c4d","application/vnd.clonk.c4group"],["c4f","application/vnd.clonk.c4group"],["c4g","application/vnd.clonk.c4group"],["c4p","application/vnd.clonk.c4group"],["c4u","application/vnd.clonk.c4group"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["cab","application/vnd.ms-cab-compressed"],["caf","audio/x-caf"],["cap","application/vnd.tcpdump.pcap"],["car","application/vnd.curl.car"],["cat","application/vnd.ms-pki.seccat"],["cb7","application/x-cbr"],["cba","application/x-cbr"],["cbr","application/x-cbr"],["cbt","application/x-cbr"],["cbz","application/x-cbr"],["cc","text/x-c"],["cco","application/x-cocoa"],["cct","application/x-director"],["ccxml","application/ccxml+xml"],["cdbcmsg","application/vnd.contact.cmsg"],["cda","application/x-cdf"],["cdf","application/x-netcdf"],["cdfx","application/cdfx+xml"],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdr","application/cdr"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer","application/pkix-cert"],["cfs","application/x-cfs-compressed"],["cgm","image/cgm"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cjs","application/node"],["cla","application/vnd.claymore"],["class","application/octet-stream"],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod","application/vnd.rim.cod"],["coffee","text/coffeescript"],["com","application/x-msdownload"],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt","application/mac-compactpro"],["crd","application/x-mscardfile"],["crl","application/pkix-crl"],["crt","application/x-x509-ca-cert"],["crx","application/x-chrome-extension"],["cryptonote","application/vnd.rig.cryptonote"],["csh","application/x-csh"],["csl","application/vnd.citationstyles.style+xml"],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["csr","application/octet-stream"],["css","text/css"],["cst","application/x-director"],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxt","application/x-director"],["cxx","text/x-c"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["dart","application/vnd.dart"],["dataless","application/vnd.fdsn.seed"],["davmount","application/davmount+xml"],["dbf","application/vnd.dbf"],["dbk","application/docbook+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["ddf","application/vnd.syncml.dmddf+xml"],["dds","image/vnd.ms-dds"],["deb","application/x-debian-package"],["def","text/plain"],["deploy","application/octet-stream"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dgc","application/x-dgc-compressed"],["dic","text/x-c"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["disposition-notification","message/disposition-notification"],["dist","application/octet-stream"],["distz","application/octet-stream"],["djv","image/vnd.djvu"],["djvu","image/vnd.djvu"],["dll","application/octet-stream"],["dmg","application/x-apple-diskimage"],["dmn","application/octet-stream"],["dmp","application/vnd.tcpdump.pcap"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.template.macroEnabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroEnabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp","application/vnd.osgi.dp"],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drle","image/dicom-rle"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dvb","video/vnd.dvb.file"],["dvi","application/x-dvi"],["dwd","application/atsc-dwd+xml"],["dwf","model/vnd.dwf"],["dwg","image/vnd.dwg"],["dxf","image/vnd.dxf"],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ear","application/java-archive"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["ecma","application/ecmascript"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["elc","application/octet-stream"],["emf","image/emf"],["eml","message/rfc822"],["emma","application/emma+xml"],["emotionml","application/emotionml+xml"],["emz","application/x-msmetafile"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es","application/ecmascript"],["es3","application/vnd.eszigno3+xml"],["esa","application/vnd.osgi.subsystem"],["esf","application/vnd.epson.esf"],["et3","application/vnd.eszigno3+xml"],["etx","text/x-setext"],["eva","application/x-eva"],["evy","application/x-envoy"],["exe","application/octet-stream"],["exi","application/exi"],["exp","application/express"],["exr","image/aces"],["ext","application/vnd.novadigm.ext"],["ez","application/andrew-inset"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f","text/x-fortran"],["f4v","video/mp4"],["f77","text/x-fortran"],["f90","text/x-fortran"],["fbs","image/vnd.fastbidsheet"],["fcdt","application/vnd.adobe.formscentral.fcdt"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fdt","application/fdt+xml"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fgd","application/x-director"],["fh","image/x-freehand"],["fh4","image/x-freehand"],["fh5","image/x-freehand"],["fh7","image/x-freehand"],["fhc","image/x-freehand"],["fig","application/x-xfig"],["fits","image/fits"],["flac","audio/x-flac"],["fli","video/x-fli"],["flo","application/vnd.micrografx.flo"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fnc","application/vnd.frogans.fnc"],["fo","application/vnd.software602.filler.form+xml"],["for","text/x-fortran"],["fpx","image/vnd.fpx"],["frame","application/vnd.framemaker"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fxpl","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gam","application/x-tads"],["gbr","application/rpki-ghostbusters"],["gca","application/x-gca-compressed"],["gdl","model/vnd.gdl"],["gdoc","application/vnd.google-apps.document"],["geo","application/vnd.dynageo"],["geojson","application/geo+json"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["glb","model/gltf-binary"],["gltf","model/gltf+json"],["gml","application/gml+xml"],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gpg","application/gpg-keys"],["gph","application/vnd.flographit"],["gpx","application/gpx+xml"],["gqf","application/vnd.grafeq"],["gqs","application/vnd.grafeq"],["gram","application/srgs"],["gramps","application/x-gramps-xml"],["gre","application/vnd.geometry-explorer"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsf","application/x-font-ghostscript"],["gsheet","application/vnd.google-apps.spreadsheet"],["gslides","application/vnd.google-apps.presentation"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxf","application/gxf"],["gxt","application/vnd.geonext"],["gz","application/gzip"],["gzip","application/gzip"],["h","text/x-c"],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hbs","text/x-handlebars-template"],["hdd","application/x-virtualbox-hdd"],["hdf","application/x-hdf"],["heic","image/heic"],["heics","image/heic-sequence"],["heif","image/heif"],["heifs","image/heif-sequence"],["hej2","image/hej2k"],["held","application/atsc-held+xml"],["hh","text/x-c"],["hjson","application/hjson"],["hlp","application/winhlp"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx","application/mac-binhex40"],["hsj2","image/hsj2"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["icm","application/vnd.iccprofile"],["ico","image/x-icon"],["ics","text/calendar"],["ief","image/ief"],["ifb","text/calendar"],["ifm","application/vnd.shana.informed.formdata"],["iges","model/iges"],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs","model/iges"],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["img","application/octet-stream"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["in","text/plain"],["ini","text/plain"],["ink","application/inkml+xml"],["inkml","application/inkml+xml"],["install","application/x-install-instructions"],["iota","application/vnd.astraea-software.iota"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["iso","application/x-iso9660-image"],["itp","application/vnd.shana.informed.formtemplate"],["its","application/its+xml"],["ivp","application/vnd.immervision-ivp"],["ivu","application/vnd.immervision-ivu"],["jad","text/vnd.sun.j2me.app-descriptor"],["jade","text/jade"],["jam","application/vnd.jam"],["jar","application/java-archive"],["jardiff","application/x-java-archive-diff"],["java","text/x-java-source"],["jhc","image/jphc"],["jisp","application/vnd.jisp"],["jls","image/jls"],["jlt","application/vnd.hp-jlyt"],["jng","image/x-jng"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jp2","image/jp2"],["jpe","image/jpeg"],["jpeg","image/jpeg"],["jpf","image/jpx"],["jpg","image/jpeg"],["jpg2","image/jp2"],["jpgm","video/jpm"],["jpgv","video/jpeg"],["jph","image/jph"],["jpm","video/jpm"],["jpx","image/jpx"],["js","application/javascript"],["json","application/json"],["json5","application/json5"],["jsonld","application/ld+json"],["jsonl","application/jsonl"],["jsonml","application/jsonml+json"],["jsx","text/jsx"],["jxr","image/jxr"],["jxra","image/jxra"],["jxrs","image/jxrs"],["jxs","image/jxs"],["jxsc","image/jxsc"],["jxsi","image/jxsi"],["jxss","image/jxss"],["kar","audio/midi"],["karbon","application/vnd.kde.karbon"],["kdb","application/octet-stream"],["kdbx","application/x-keepass2"],["key","application/x-iwork-keynote-sffkey"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["knp","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["kpt","application/vnd.kde.kpresenter"],["kpxx","application/vnd.ds-keypoint"],["ksp","application/vnd.kde.kspread"],["ktr","application/vnd.kahootz"],["ktx","image/ktx"],["ktx2","image/ktx2"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["kwt","application/vnd.kde.kword"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["less","text/less"],["lgr","application/lgr+xml"],["lha","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["list3820","application/vnd.ibm.modcap"],["listafp","application/vnd.ibm.modcap"],["litcoffee","text/coffeescript"],["lnk","application/x-ms-shortcut"],["log","text/plain"],["lostxml","application/lost+xml"],["lrf","application/octet-stream"],["lrm","application/vnd.ms-lrm"],["ltf","application/vnd.frogans.ltf"],["lua","text/x-lua"],["luac","application/x-lua-bytecode"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh","application/octet-stream"],["m1v","video/mpeg"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3a","audio/mpeg"],["m3u","text/plain"],["m3u8","application/vnd.apple.mpegurl"],["m4a","audio/x-m4a"],["m4p","application/mp4"],["m4s","video/iso.segment"],["m4u","application/vnd.mpegurl"],["m4v","video/x-m4v"],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m21","application/mp21"],["ma","application/mathematica"],["mads","application/mads+xml"],["maei","application/mmt-aei+xml"],["mag","application/vnd.ecowin.chart"],["maker","application/vnd.framemaker"],["man","text/troff"],["manifest","text/cache-manifest"],["map","application/json"],["mar","application/octet-stream"],["markdown","text/markdown"],["mathml","application/mathml+xml"],["mb","application/mathematica"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc1","application/vnd.medcalcdata"],["mcd","application/vnd.mcd"],["mcurl","text/vnd.curl.mcurl"],["md","text/markdown"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["mdx","text/mdx"],["me","text/troff"],["mesh","model/mesh"],["meta4","application/metalink4+xml"],["metalink","application/metalink+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mft","application/rpki-manifest"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mid","audio/midi"],["midi","audio/midi"],["mie","application/x-mie"],["mif","application/vnd.mif"],["mime","message/rfc822"],["mj2","video/mj2"],["mjp2","video/mj2"],["mjs","application/javascript"],["mk3d","video/x-matroska"],["mka","audio/x-matroska"],["mkd","text/x-markdown"],["mks","video/x-matroska"],["mkv","video/x-matroska"],["mlp","application/vnd.dolby.mlp"],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mmf","application/vnd.smaf"],["mml","text/mathml"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mng","video/x-mng"],["mny","application/x-msmoney"],["mobi","application/x-mobipocket-ebook"],["mods","application/mods+xml"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2","audio/mpeg"],["mp2a","audio/mpeg"],["mp3","audio/mpeg"],["mp4","video/mp4"],["mp4a","audio/mp4"],["mp4s","application/mp4"],["mp4v","video/mp4"],["mp21","application/mp21"],["mpc","application/vnd.mophun.certificate"],["mpd","application/dash+xml"],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg","video/mpeg"],["mpg4","video/mp4"],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/vnd.ms-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","text/troff"],["mscml","application/mediaservercontrol+xml"],["mseed","application/vnd.fdsn.mseed"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msi","application/x-msdownload"],["msl","application/vnd.mobius.msl"],["msm","application/octet-stream"],["msp","application/octet-stream"],["msty","application/vnd.muvee.style"],["mtl","model/mtl"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musd","application/mmt-usd+xml"],["musicxml","application/vnd.recordare.musicxml+xml"],["mvb","application/x-msmediaview"],["mvt","application/vnd.mapbox-vector-tile"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxmf","audio/mobile-xmf"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nb","application/mathematica"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncx","application/x-dtbncx+xml"],["nfo","text/x-nfo"],["ngdat","application/vnd.nokia.n-gage.data"],["nitf","application/vnd.nitf"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nq","application/n-quads"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nt","application/n-triples"],["ntf","application/vnd.nitf"],["numbers","application/x-iwork-numbers-sffnumbers"],["nzb","application/x-nzb"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["obgx","application/vnd.openblox.game+xml"],["obj","model/obj"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogex","model/vnd.opengex"],["ogg","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omdoc","application/omdoc+xml"],["onepkg","application/onenote"],["onetmp","application/onenote"],["onetoc","application/onenote"],["onetoc2","application/onenote"],["opf","application/oebps-package+xml"],["opml","text/x-opml"],["oprc","application/vnd.palm"],["opus","audio/ogg"],["org","text/x-org"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["osm","application/vnd.openstreetmap.data+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","font/otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["ova","application/x-virtualbox-ova"],["ovf","application/x-virtualbox-ovf"],["owl","application/rdf+xml"],["oxps","application/oxps"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c","application/pkcs7-mime"],["p7m","application/pkcs7-mime"],["p7r","application/x-pkcs7-certreqresp"],["p7s","application/pkcs7-signature"],["p8","application/pkcs8"],["p10","application/x-pkcs10"],["p12","application/x-pkcs12"],["pac","application/x-ns-proxy-autoconfig"],["pages","application/x-iwork-pages-sffpages"],["pas","text/x-pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcap","application/vnd.tcpdump.pcap"],["pcf","application/x-font-pcf"],["pcl","application/vnd.hp-pcl"],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb","application/x-pilot"],["pde","text/x-processing"],["pdf","application/pdf"],["pem","application/x-x509-user-cert"],["pfa","application/x-font-type1"],["pfb","application/x-font-type1"],["pfm","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfx","application/x-pkcs12"],["pgm","image/x-portable-graymap"],["pgn","application/x-chess-pgn"],["pgp","application/pgp"],["php","application/x-httpd-php"],["php3","application/x-httpd-php"],["php4","application/x-httpd-php"],["phps","application/x-httpd-php-source"],["phtml","application/x-httpd-php"],["pic","image/x-pict"],["pkg","application/octet-stream"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pkpass","application/vnd.apple.pkpass"],["pl","application/x-perl"],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["pm","application/x-perl"],["pml","application/vnd.ctc-posml"],["png","image/png"],["pnm","image/x-portable-anymap"],["portpkg","application/vnd.macports.portpkg"],["pot","application/vnd.ms-powerpoint"],["potm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroEnabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps","application/vnd.ms-powerpoint"],["ppsm","application/vnd.ms-powerpoint.slideshow.macroEnabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt","application/powerpoint"],["pptm","application/vnd.ms-powerpoint.presentation.macroEnabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["pqa","application/vnd.palm"],["prc","application/x-pilot"],["pre","application/vnd.lotus-freelance"],["prf","application/pics-rules"],["provx","application/provenance+xml"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd","application/x-photoshop"],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["pti","image/prs.pti"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pwn","application/vnd.3m.post-it-notes"],["pya","audio/vnd.ms-playready.media.pya"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qfx","application/vnd.intu.qfx"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qwd","application/vnd.quark.quarkxpress"],["qwt","application/vnd.quark.quarkxpress"],["qxb","application/vnd.quark.quarkxpress"],["qxd","application/vnd.quark.quarkxpress"],["qxl","application/vnd.quark.quarkxpress"],["qxt","application/vnd.quark.quarkxpress"],["ra","audio/x-realaudio"],["ram","audio/x-pn-realaudio"],["raml","application/raml+yaml"],["rapd","application/route-apd+xml"],["rar","application/x-rar"],["ras","image/x-cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["relo","application/p2p-overlay+xml"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["ris","application/x-research-info-systems"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm","audio/x-pn-realaudio"],["rmi","audio/midi"],["rmp","audio/x-pn-realaudio-plugin"],["rms","application/vnd.jcp.javame.midlet-rms"],["rmvb","application/vnd.rn-realmedia-vbr"],["rnc","application/relax-ng-compact-syntax"],["rng","application/xml"],["roa","application/rpki-roa"],["roff","text/troff"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsa","application/x-pkcs7"],["rsat","application/atsc-rsat+xml"],["rsd","application/rsd+xml"],["rsheet","application/urc-ressheet+xml"],["rss","application/rss+xml"],["rtf","text/rtf"],["rtx","text/richtext"],["run","application/x-makeself"],["rusd","application/route-usd+xml"],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["sass","text/x-sass"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm","application/vnd.lotus-screencam"],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["scss","text/x-scss"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkd","application/vnd.solent.sdkm+xml"],["sdkm","application/vnd.solent.sdkm+xml"],["sdp","application/sdp"],["sdw","application/vnd.stardivision.writer"],["sea","application/octet-stream"],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["senmlx","application/senml+xml"],["sensmlx","application/sensml+xml"],["ser","application/java-serialized-object"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sfv","text/x-sfv"],["sgi","image/sgi"],["sgl","application/vnd.stardivision.writer-global"],["sgm","text/sgml"],["sgml","text/sgml"],["sh","application/x-sh"],["shar","application/x-shar"],["shex","text/shex"],["shf","application/shf+xml"],["shtml","text/html"],["sid","image/x-mrsid-image"],["sieve","application/sieve"],["sig","application/pgp-signature"],["sil","audio/silk"],["silo","model/mesh"],["sis","application/vnd.symbian.install"],["sisx","application/vnd.symbian.install"],["sit","application/x-stuffit"],["sitx","application/x-stuffitx"],["siv","application/sieve"],["skd","application/vnd.koan"],["skm","application/vnd.koan"],["skp","application/vnd.koan"],["skt","application/vnd.koan"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slim","text/slim"],["slm","text/slim"],["sls","application/route-s-tsid+xml"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi","application/smil"],["smil","application/smil"],["smv","video/x-smv"],["smzip","application/vnd.stepmania.package"],["snd","audio/basic"],["snf","application/x-font-snf"],["so","application/octet-stream"],["spc","application/x-pkcs7-certificates"],["spdx","text/spdx"],["spf","application/vnd.yamaha.smaf-phrase"],["spl","application/x-futuresplash"],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spx","audio/ogg"],["sql","application/x-sql"],["src","application/x-wais-source"],["srt","application/x-subrip"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["ssdl","application/ssdl+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssml","application/ssml+xml"],["sst","application/octet-stream"],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl","model/stl"],["stpx","model/step+xml"],["stpxz","model/step-xml+zip"],["stpz","model/step+zip"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["styl","text/stylus"],["stylus","text/stylus"],["sub","text/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["susp","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svg","image/svg+xml"],["svgz","image/svg+xml"],["swa","application/x-director"],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["swidtag","application/swid+xml"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t","text/troff"],["t3","application/x-t3vm-image"],["t38","image/t38"],["taglet","application/vnd.mynfc"],["tao","application/vnd.tao.intent-module-archive"],["tap","image/vnd.tencent.tap"],["tar","application/x-tar"],["tcap","application/vnd.3gpp2.tcap"],["tcl","application/x-tcl"],["td","application/urc-targetdesc+xml"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["teicorpus","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text","text/plain"],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tfx","image/tiff-fx"],["tga","image/x-tga"],["tgz","application/x-tar"],["thmx","application/vnd.ms-officetheme"],["tif","image/tiff"],["tiff","image/tiff"],["tk","application/x-tcl"],["tmo","application/vnd.tmobile-livetv"],["toml","application/toml"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","text/troff"],["tra","application/vnd.trueapp"],["trig","application/trig"],["trm","application/x-msterminal"],["ts","video/mp2t"],["tsd","application/timestamped-data"],["tsv","text/tab-separated-values"],["ttc","font/collection"],["ttf","font/ttf"],["ttl","text/turtle"],["ttml","application/ttml+xml"],["twd","application/vnd.simtech-mindmapper"],["twds","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["u8dsn","message/global-delivery-status"],["u8hdr","message/global-headers"],["u8mdn","message/global-disposition-notification"],["u8msg","message/global"],["u32","application/x-authorware-bin"],["ubj","application/ubjson"],["udeb","application/x-debian-package"],["ufd","application/vnd.ufdl"],["ufdl","application/vnd.ufdl"],["ulx","application/x-glulx"],["umj","application/vnd.umajin"],["unityweb","application/vnd.unity"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["urls","text/uri-list"],["usdz","model/vnd.usdz+zip"],["ustar","application/x-ustar"],["utz","application/vnd.uiq.theme"],["uu","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvd","application/vnd.dece.data"],["uvf","application/vnd.dece.data"],["uvg","image/vnd.dece.graphic"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvt","application/vnd.dece.ttml+xml"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["uvva","audio/vnd.dece.audio"],["uvvd","application/vnd.dece.data"],["uvvf","application/vnd.dece.data"],["uvvg","image/vnd.dece.graphic"],["uvvh","video/vnd.dece.hd"],["uvvi","image/vnd.dece.graphic"],["uvvm","video/vnd.dece.mobile"],["uvvp","video/vnd.dece.pd"],["uvvs","video/vnd.dece.sd"],["uvvt","application/vnd.dece.ttml+xml"],["uvvu","video/vnd.uvvu.mp4"],["uvvv","video/vnd.dece.video"],["uvvx","application/vnd.dece.unspecified"],["uvvz","application/vnd.dece.zip"],["uvx","application/vnd.dece.unspecified"],["uvz","application/vnd.dece.zip"],["vbox","application/x-virtualbox-vbox"],["vbox-extpack","application/x-virtualbox-vbox-extpack"],["vcard","text/vcard"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vdi","application/x-virtualbox-vdi"],["vds","model/vnd.sap.vds"],["vhd","application/x-virtualbox-vhd"],["vis","application/vnd.visionary"],["viv","video/vnd.vivo"],["vlc","application/videolan"],["vmdk","application/x-virtualbox-vmdk"],["vob","video/x-ms-vob"],["vor","application/vnd.stardivision.writer"],["vox","application/x-authorware-bin"],["vrml","model/vrml"],["vsd","application/vnd.visio"],["vsf","application/vnd.vsf"],["vss","application/vnd.visio"],["vst","application/vnd.visio"],["vsw","application/vnd.visio"],["vtf","image/vnd.valve.source.texture"],["vtt","text/vtt"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w3d","application/x-director"],["wad","application/x-doom"],["wadl","application/vnd.sun.wadl+xml"],["war","application/java-archive"],["wasm","application/wasm"],["wav","audio/x-wav"],["wax","audio/x-ms-wax"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["wdp","image/vnd.ms-photo"],["weba","audio/webm"],["webapp","application/x-web-app-manifest+json"],["webm","video/webm"],["webmanifest","application/manifest+json"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf","image/wmf"],["wml","text/vnd.wap.wml"],["wmlc","application/wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-msmetafile"],["woff","font/woff"],["woff2","font/woff2"],["word","application/msword"],["wpd","application/vnd.wordperfect"],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wqd","application/vnd.wqd"],["wri","application/x-mswrite"],["wrl","model/vrml"],["wsc","message/vnd.wfa.wsc"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wtb","application/vnd.webturbo"],["wvx","video/x-ms-wvx"],["x3d","model/x3d+xml"],["x3db","model/x3d+fastinfoset"],["x3dbz","model/x3d+binary"],["x3dv","model/x3d-vrml"],["x3dvz","model/x3d+vrml"],["x3dz","model/x3d+xml"],["x32","application/x-authorware-bin"],["x_b","model/vnd.parasolid.transmit.binary"],["x_t","model/vnd.parasolid.transmit.text"],["xaml","application/xaml+xml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xav","application/xcap-att+xml"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm","image/x-xbitmap"],["xca","application/xcap-caps+xml"],["xcs","application/calendar+xml"],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xel","application/xcap-el+xml"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xht","application/xhtml+xml"],["xhtml","application/xhtml+xml"],["xhvml","application/xv+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla","application/vnd.ms-excel"],["xlam","application/vnd.ms-excel.addin.macroEnabled.12"],["xlc","application/vnd.ms-excel"],["xlf","application/xliff+xml"],["xlm","application/vnd.ms-excel"],["xls","application/vnd.ms-excel"],["xlsb","application/vnd.ms-excel.sheet.binary.macroEnabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroEnabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt","application/vnd.ms-excel"],["xltm","application/vnd.ms-excel.template.macroEnabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlw","application/vnd.ms-excel"],["xm","audio/xm"],["xml","application/xml"],["xns","application/xcap-ns+xml"],["xo","application/vnd.olpc-sugar"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpl","application/xproc+xml"],["xpm","image/x-xpixmap"],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xpx","application/vnd.intercon.formnet"],["xsd","application/xml"],["xsl","application/xml"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xul","application/vnd.mozilla.xul+xml"],["xvm","application/xv+xml"],["xvml","application/xv+xml"],["xwd","image/x-xwindowdump"],["xyz","chemical/x-xyz"],["xz","application/x-xz"],["yaml","text/yaml"],["yang","application/yang"],["yin","application/yin+xml"],["yml","text/yaml"],["ymp","text/x-suse-ymp"],["z","application/x-compress"],["z1","application/x-zmachine"],["z2","application/x-zmachine"],["z3","application/x-zmachine"],["z4","application/x-zmachine"],["z5","application/x-zmachine"],["z6","application/x-zmachine"],["z7","application/x-zmachine"],["z8","application/x-zmachine"],["zaz","application/vnd.zzazz.deck+xml"],["zip","application/zip"],["zir","application/vnd.zul"],["zirz","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zsh","text/x-scriptzsh"]]);function yt(t,e,n){const i=ji(t),{webkitRelativePath:a}=t,o=typeof e=="string"?e:typeof a=="string"&&a.length>0?a:`./${t.name}`;return typeof i.path!="string"&&Be(i,"path",o),Be(i,"relativePath",o),i}function ji(t){const{name:e}=t;if(e&&e.lastIndexOf(".")!==-1&&!t.type){const i=e.split(".").pop().toLowerCase(),a=Di.get(i);a&&Object.defineProperty(t,"type",{value:a,writable:!1,configurable:!1,enumerable:!0})}return t}function Be(t,e,n){Object.defineProperty(t,e,{value:n,writable:!1,configurable:!1,enumerable:!0})}const Ti=[".DS_Store","Thumbs.db"];function Pi(t){return ut(this,void 0,void 0,function*(){return Ht(t)&&Ii(t.dataTransfer)?Mi(t.dataTransfer,t.type):Oi(t)?_i(t):Array.isArray(t)&&t.every(e=>"getFile"in e&&typeof e.getFile=="function")?Fi(t):[]})}function Ii(t){return Ht(t)}function Oi(t){return Ht(t)&&Ht(t.target)}function Ht(t){return typeof t=="object"&&t!==null}function _i(t){return we(t.target.files).map(e=>yt(e))}function Fi(t){return ut(this,void 0,void 0,function*(){return(yield Promise.all(t.map(n=>n.getFile()))).map(n=>yt(n))})}function Mi(t,e){return ut(this,void 0,void 0,function*(){if(t.items){const n=we(t.items).filter(a=>a.kind==="file");if(e!=="drop")return n;const i=yield Promise.all(n.map(Ri));return Ne(xn(i))}return Ne(we(t.files).map(n=>yt(n)))})}function Ne(t){return t.filter(e=>Ti.indexOf(e.name)===-1)}function we(t){if(t===null)return[];const e=[];for(let n=0;n<t.length;n++){const i=t[n];e.push(i)}return e}function Ri(t){if(typeof t.webkitGetAsEntry!="function")return Le(t);const e=t.webkitGetAsEntry();return e&&e.isDirectory?hn(e):Le(t,e)}function xn(t){return t.reduce((e,n)=>[...e,...Array.isArray(n)?xn(n):[n]],[])}function Le(t,e){return ut(this,void 0,void 0,function*(){var n;if(globalThis.isSecureContext&&typeof t.getAsFileSystemHandle=="function"){const o=yield t.getAsFileSystemHandle();if(o===null)throw new Error(`${t} is not a File`);if(o!==void 0){const r=yield o.getFile();return r.handle=o,yt(r)}}const i=t.getAsFile();if(!i)throw new Error(`${t} is not a File`);return yt(i,(n=e==null?void 0:e.fullPath)!==null&&n!==void 0?n:void 0)})}function Bi(t){return ut(this,void 0,void 0,function*(){return t.isDirectory?hn(t):Ni(t)})}function hn(t){const e=t.createReader();return new Promise((n,i)=>{const a=[];function o(){e.readEntries(r=>ut(this,void 0,void 0,function*(){if(r.length){const p=Promise.all(r.map(Bi));a.push(p),o()}else try{const p=yield Promise.all(a);n(p)}catch(p){i(p)}}),r=>{i(r)})}o()})}function Ni(t){return ut(this,void 0,void 0,function*(){return new Promise((e,n)=>{t.file(i=>{const a=yt(i,t.fullPath);e(a)},i=>{n(i)})})})}var fe=function(t,e){if(t&&e){var n=Array.isArray(e)?e:e.split(",");if(n.length===0)return!0;var i=t.name||"",a=(t.type||"").toLowerCase(),o=a.replace(/\/.*$/,"");return n.some(function(r){var p=r.trim().toLowerCase();return p.charAt(0)==="."?i.toLowerCase().endsWith(p):p.endsWith("/*")?o===p.replace(/\/.*$/,""):a===p})}return!0};function Ue(t){return qi(t)||Ui(t)||bn(t)||Li()}function Li(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ui(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function qi(t){if(Array.isArray(t))return ke(t)}function qe(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),n.push.apply(n,i)}return n}function $e(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?qe(Object(n),!0).forEach(function(i){yn(t,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):qe(Object(n)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(n,i))})}return t}function yn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function jt(t,e){return Ki(t)||Hi(t,e)||bn(t,e)||$i()}function $i(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bn(t,e){if(t){if(typeof t=="string")return ke(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return ke(t,e)}}function ke(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function Hi(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var i=[],a=!0,o=!1,r,p;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(i.push(r.value),!(e&&i.length===e));a=!0);}catch(c){o=!0,p=c}finally{try{!a&&n.return!=null&&n.return()}finally{if(o)throw p}}return i}}function Ki(t){if(Array.isArray(t))return t}var Vi=typeof fe=="function"?fe:fe.default,Wi="file-invalid-type",Yi="file-too-large",Gi="file-too-small",Ji="too-many-files",Qi=function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",n=e.split(","),i=n.length>1?"one of ".concat(n.join(", ")):n[0];return{code:Wi,message:"File type must be ".concat(i)}},He=function(e){return{code:Yi,message:"File is larger than ".concat(e," ").concat(e===1?"byte":"bytes")}},Ke=function(e){return{code:Gi,message:"File is smaller than ".concat(e," ").concat(e===1?"byte":"bytes")}},Zi={code:Ji,message:"Too many files"};function wn(t,e){var n=t.type==="application/x-moz-file"||Vi(t,e);return[n,n?null:Qi(e)]}function kn(t,e,n){if(dt(t.size))if(dt(e)&&dt(n)){if(t.size>n)return[!1,He(n)];if(t.size<e)return[!1,Ke(e)]}else{if(dt(e)&&t.size<e)return[!1,Ke(e)];if(dt(n)&&t.size>n)return[!1,He(n)]}return[!0,null]}function dt(t){return t!=null}function Xi(t){var e=t.files,n=t.accept,i=t.minSize,a=t.maxSize,o=t.multiple,r=t.maxFiles,p=t.validator;return!o&&e.length>1||o&&r>=1&&e.length>r?!1:e.every(function(c){var d=wn(c,n),s=jt(d,1),A=s[0],w=kn(c,i,a),v=jt(w,1),S=v[0],b=p?p(c):null;return A&&S&&!b})}function Kt(t){return typeof t.isPropagationStopped=="function"?t.isPropagationStopped():typeof t.cancelBubble<"u"?t.cancelBubble:!1}function Ut(t){return t.dataTransfer?Array.prototype.some.call(t.dataTransfer.types,function(e){return e==="Files"||e==="application/x-moz-file"}):!!t.target&&!!t.target.files}function Ve(t){t.preventDefault()}function ta(t){return t.indexOf("MSIE")!==-1||t.indexOf("Trident/")!==-1}function ea(t){return t.indexOf("Edge/")!==-1}function na(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:window.navigator.userAgent;return ta(t)||ea(t)}function it(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];return function(i){for(var a=arguments.length,o=new Array(a>1?a-1:0),r=1;r<a;r++)o[r-1]=arguments[r];return e.some(function(p){return!Kt(i)&&p&&p.apply(void 0,[i].concat(o)),Kt(i)})}}function ia(){return"showOpenFilePicker"in window}function aa(t){if(dt(t)){var e=Object.entries(t).filter(function(n){var i=jt(n,2),a=i[0],o=i[1],r=!0;return En(a)||(console.warn('Skipped "'.concat(a,'" because it is not a valid MIME type. Check https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types/Common_types for a list of valid MIME types.')),r=!1),(!Array.isArray(o)||!o.every(Sn))&&(console.warn('Skipped "'.concat(a,'" because an invalid file extension was provided.')),r=!1),r}).reduce(function(n,i){var a=jt(i,2),o=a[0],r=a[1];return $e($e({},n),{},yn({},o,r))},{});return[{description:"Files",accept:e}]}return t}function oa(t){if(dt(t))return Object.entries(t).reduce(function(e,n){var i=jt(n,2),a=i[0],o=i[1];return[].concat(Ue(e),[a],Ue(o))},[]).filter(function(e){return En(e)||Sn(e)}).join(",")}function ra(t){return t instanceof DOMException&&(t.name==="AbortError"||t.code===t.ABORT_ERR)}function pa(t){return t instanceof DOMException&&(t.name==="SecurityError"||t.code===t.SECURITY_ERR)}function En(t){return t==="audio/*"||t==="video/*"||t==="image/*"||t==="text/*"||t==="application/*"||/\w+\/[-+.\w]+/g.test(t)}function Sn(t){return/^.*\.[\w]+$/.test(t)}var ca=["children"],la=["open"],sa=["refKey","role","onKeyDown","onFocus","onBlur","onClick","onDragEnter","onDragOver","onDragLeave","onDrop"],da=["refKey","onChange","onClick"];function ua(t){return ga(t)||fa(t)||zn(t)||ma()}function ma(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function fa(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ga(t){if(Array.isArray(t))return Ee(t)}function ge(t,e){return ha(t)||xa(t,e)||zn(t,e)||va()}function va(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function zn(t,e){if(t){if(typeof t=="string")return Ee(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Ee(t,e)}}function Ee(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,i=new Array(e);n<e;n++)i[n]=t[n];return i}function xa(t,e){var n=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(n!=null){var i=[],a=!0,o=!1,r,p;try{for(n=n.call(t);!(a=(r=n.next()).done)&&(i.push(r.value),!(e&&i.length===e));a=!0);}catch(c){o=!0,p=c}finally{try{!a&&n.return!=null&&n.return()}finally{if(o)throw p}}return i}}function ha(t){if(Array.isArray(t))return t}function We(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter(function(a){return Object.getOwnPropertyDescriptor(t,a).enumerable})),n.push.apply(n,i)}return n}function _(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?We(Object(n),!0).forEach(function(i){Se(t,i,n[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):We(Object(n)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(n,i))})}return t}function Se(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Vt(t,e){if(t==null)return{};var n=ya(t,e),i,a;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(a=0;a<o.length;a++)i=o[a],!(e.indexOf(i)>=0)&&Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}function ya(t,e){if(t==null)return{};var n={},i=Object.keys(t),a,o;for(o=0;o<i.length;o++)a=i[o],!(e.indexOf(a)>=0)&&(n[a]=t[a]);return n}var Ie=T.forwardRef(function(t,e){var n=t.children,i=Vt(t,ca),a=ba(i),o=a.open,r=Vt(a,la);return T.useImperativeHandle(e,function(){return{open:o}},[o]),ve.createElement(T.Fragment,null,n(_(_({},r),{},{open:o})))});Ie.displayName="Dropzone";var An={disabled:!1,getFilesFromEvent:Pi,maxSize:1/0,minSize:0,multiple:!0,maxFiles:0,preventDropOnDocument:!0,noClick:!1,noKeyboard:!1,noDrag:!1,noDragEventsBubbling:!1,validator:null,useFsAccessApi:!1,autoFocus:!1};Ie.defaultProps=An;Ie.propTypes={children:h.func,accept:h.objectOf(h.arrayOf(h.string)),multiple:h.bool,preventDropOnDocument:h.bool,noClick:h.bool,noKeyboard:h.bool,noDrag:h.bool,noDragEventsBubbling:h.bool,minSize:h.number,maxSize:h.number,maxFiles:h.number,disabled:h.bool,getFilesFromEvent:h.func,onFileDialogCancel:h.func,onFileDialogOpen:h.func,useFsAccessApi:h.bool,autoFocus:h.bool,onDragEnter:h.func,onDragLeave:h.func,onDragOver:h.func,onDrop:h.func,onDropAccepted:h.func,onDropRejected:h.func,onError:h.func,validator:h.func};var ze={isFocused:!1,isFileDialogActive:!1,isDragActive:!1,isDragAccept:!1,isDragReject:!1,acceptedFiles:[],fileRejections:[]};function ba(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e=_(_({},An),t),n=e.accept,i=e.disabled,a=e.getFilesFromEvent,o=e.maxSize,r=e.minSize,p=e.multiple,c=e.maxFiles,d=e.onDragEnter,s=e.onDragLeave,A=e.onDragOver,w=e.onDrop,v=e.onDropAccepted,S=e.onDropRejected,b=e.onFileDialogCancel,j=e.onFileDialogOpen,E=e.useFsAccessApi,k=e.autoFocus,g=e.preventDropOnDocument,y=e.noClick,C=e.noKeyboard,P=e.noDrag,D=e.noDragEventsBubbling,F=e.onError,B=e.validator,N=T.useMemo(function(){return oa(n)},[n]),J=T.useMemo(function(){return aa(n)},[n]),ot=T.useMemo(function(){return typeof j=="function"?j:Ye},[j]),M=T.useMemo(function(){return typeof b=="function"?b:Ye},[b]),U=T.useRef(null),q=T.useRef(null),Pt=T.useReducer(wa,ze),bt=ge(Pt,2),wt=bt[0],$=bt[1],Zt=wt.isFocused,kt=wt.isFileDialogActive,mt=T.useRef(typeof window<"u"&&window.isSecureContext&&E&&ia()),It=function(){!mt.current&&kt&&setTimeout(function(){if(q.current){var z=q.current.files;z.length||($({type:"closeDialog"}),M())}},300)};T.useEffect(function(){return window.addEventListener("focus",It,!1),function(){window.removeEventListener("focus",It,!1)}},[q,kt,M,mt]);var V=T.useRef([]),Et=function(z){U.current&&U.current.contains(z.target)||(z.preventDefault(),V.current=[])};T.useEffect(function(){return g&&(document.addEventListener("dragover",Ve,!1),document.addEventListener("drop",Et,!1)),function(){g&&(document.removeEventListener("dragover",Ve),document.removeEventListener("drop",Et))}},[U,g]),T.useEffect(function(){return!i&&k&&U.current&&U.current.focus(),function(){}},[U,k,i]);var rt=T.useCallback(function(m){F?F(m):console.error(m)},[F]),Ot=T.useCallback(function(m){m.preventDefault(),m.persist(),Ft(m),V.current=[].concat(ua(V.current),[m.target]),Ut(m)&&Promise.resolve(a(m)).then(function(z){if(!(Kt(m)&&!D)){var R=z.length,L=R>0&&Xi({files:z,accept:N,minSize:r,maxSize:o,multiple:p,maxFiles:c,validator:B}),W=R>0&&!L;$({isDragAccept:L,isDragReject:W,isDragActive:!0,type:"setDraggedFiles"}),d&&d(m)}}).catch(function(z){return rt(z)})},[a,d,rt,D,N,r,o,p,c,B]),f=T.useCallback(function(m){m.preventDefault(),m.persist(),Ft(m);var z=Ut(m);if(z&&m.dataTransfer)try{m.dataTransfer.dropEffect="copy"}catch{}return z&&A&&A(m),!1},[A,D]),l=T.useCallback(function(m){m.preventDefault(),m.persist(),Ft(m);var z=V.current.filter(function(L){return U.current&&U.current.contains(L)}),R=z.indexOf(m.target);R!==-1&&z.splice(R,1),V.current=z,!(z.length>0)&&($({type:"setDraggedFiles",isDragActive:!1,isDragAccept:!1,isDragReject:!1}),Ut(m)&&s&&s(m))},[U,s,D]),u=T.useCallback(function(m,z){var R=[],L=[];m.forEach(function(W){var St=wn(W,N),ft=ge(St,2),te=ft[0],ee=ft[1],ne=kn(W,r,o),Mt=ge(ne,2),ie=Mt[0],ae=Mt[1],oe=B?B(W):null;if(te&&ie&&!oe)R.push(W);else{var re=[ee,ae];oe&&(re=re.concat(oe)),L.push({file:W,errors:re.filter(function(Tn){return Tn})})}}),(!p&&R.length>1||p&&c>=1&&R.length>c)&&(R.forEach(function(W){L.push({file:W,errors:[Zi]})}),R.splice(0)),$({acceptedFiles:R,fileRejections:L,isDragReject:L.length>0,type:"setFiles"}),w&&w(R,L,z),L.length>0&&S&&S(L,z),R.length>0&&v&&v(R,z)},[$,p,N,r,o,c,w,v,S,B]),x=T.useCallback(function(m){m.preventDefault(),m.persist(),Ft(m),V.current=[],Ut(m)&&Promise.resolve(a(m)).then(function(z){Kt(m)&&!D||u(z,m)}).catch(function(z){return rt(z)}),$({type:"reset"})},[a,u,rt,D]),I=T.useCallback(function(){if(mt.current){$({type:"openDialog"}),ot();var m={multiple:p,types:J};window.showOpenFilePicker(m).then(function(z){return a(z)}).then(function(z){u(z,null),$({type:"closeDialog"})}).catch(function(z){ra(z)?(M(z),$({type:"closeDialog"})):pa(z)?(mt.current=!1,q.current?(q.current.value=null,q.current.click()):rt(new Error("Cannot open the file picker because the https://developer.mozilla.org/en-US/docs/Web/API/File_System_Access_API is not supported and no <input> was provided."))):rt(z)});return}q.current&&($({type:"openDialog"}),ot(),q.current.value=null,q.current.click())},[$,ot,M,E,u,rt,J,p]),O=T.useCallback(function(m){!U.current||!U.current.isEqualNode(m.target)||(m.key===" "||m.key==="Enter"||m.keyCode===32||m.keyCode===13)&&(m.preventDefault(),I())},[U,I]),H=T.useCallback(function(){$({type:"focus"})},[]),nt=T.useCallback(function(){$({type:"blur"})},[]),st=T.useCallback(function(){y||(na()?setTimeout(I,0):I())},[y,I]),Q=function(z){return i?null:z},Xt=function(z){return C?null:Q(z)},_t=function(z){return P?null:Q(z)},Ft=function(z){D&&z.stopPropagation()},Cn=T.useMemo(function(){return function(){var m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},z=m.refKey,R=z===void 0?"ref":z,L=m.role,W=m.onKeyDown,St=m.onFocus,ft=m.onBlur,te=m.onClick,ee=m.onDragEnter,ne=m.onDragOver,Mt=m.onDragLeave,ie=m.onDrop,ae=Vt(m,sa);return _(_(Se({onKeyDown:Xt(it(W,O)),onFocus:Xt(it(St,H)),onBlur:Xt(it(ft,nt)),onClick:Q(it(te,st)),onDragEnter:_t(it(ee,Ot)),onDragOver:_t(it(ne,f)),onDragLeave:_t(it(Mt,l)),onDrop:_t(it(ie,x)),role:typeof L=="string"&&L!==""?L:"presentation"},R,U),!i&&!C?{tabIndex:0}:{}),ae)}},[U,O,H,nt,st,Ot,f,l,x,C,P,i]),Dn=T.useCallback(function(m){m.stopPropagation()},[]),jn=T.useMemo(function(){return function(){var m=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},z=m.refKey,R=z===void 0?"ref":z,L=m.onChange,W=m.onClick,St=Vt(m,da),ft=Se({accept:N,multiple:p,type:"file",style:{border:0,clip:"rect(0, 0, 0, 0)",clipPath:"inset(50%)",height:"1px",margin:"0 -1px -1px 0",overflow:"hidden",padding:0,position:"absolute",width:"1px",whiteSpace:"nowrap"},onChange:Q(it(L,x)),onClick:Q(it(W,Dn)),tabIndex:-1},R,q);return _(_({},ft),St)}},[q,n,p,x,i]);return _(_({},wt),{},{isFocused:Zt&&!i,getRootProps:Cn,getInputProps:jn,rootRef:U,inputRef:q,open:Q(I)})}function wa(t,e){switch(e.type){case"focus":return _(_({},t),{},{isFocused:!0});case"blur":return _(_({},t),{},{isFocused:!1});case"openDialog":return _(_({},ze),{},{isFileDialogActive:!0});case"closeDialog":return _(_({},t),{},{isFileDialogActive:!1});case"setDraggedFiles":return _(_({},t),{},{isDragActive:e.isDragActive,isDragAccept:e.isDragAccept,isDragReject:e.isDragReject});case"setFiles":return _(_({},t),{},{acceptedFiles:e.acceptedFiles,fileRejections:e.fileRejections,isDragReject:e.isDragReject});case"reset":return _({},ze);default:return t}}function Ye(){}export{Xe as S,Wt as b,ba as u};
